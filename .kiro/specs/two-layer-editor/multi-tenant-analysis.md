# 多租户架构支持分析

## T3 Stack多租户支持情况

### 原生支持
T3 Stack本身**不提供开箱即用的多租户功能**，但可以通过以下方式实现：

#### 1. 数据库级别多租户 (推荐)
```typescript
// Prisma Schema设计
model Team {
  id        String   @id @default(uuid())
  name      String
  slug      String   @unique
  domain    String?  @unique
  
  // 关联所有租户数据
  users     User[]
  projects  Project[]
  
  @@map("teams")
}

model User {
  id     String @id @default(uuid())
  email  String @unique
  
  // 多租户关联
  teamId String
  team   Team   @relation(fields: [teamId], references: [id])
  
  @@map("users")
}

model Project {
  id     String @id @default(uuid())
  name   String
  
  // 租户隔离
  teamId String
  team   Team   @relation(fields: [teamId], references: [id])
  
  @@map("projects")
}
```

#### 2. tRPC中间件实现租户隔离
```typescript
// 租户上下文中间件
const tenantMiddleware = t.middleware(async ({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }
  
  // 获取用户的租户信息
  const teamMember = await ctx.prisma.teamMember.findFirst({
    where: { userId: ctx.user.id },
    include: { team: true }
  })
  
  if (!teamMember) {
    throw new TRPCError({ code: 'FORBIDDEN' })
  }
  
  return next({
    ctx: {
      ...ctx,
      tenant: teamMember.team,
      userRole: teamMember.role
    }
  })
})

// 受保护的租户路由
const tenantProcedure = t.procedure.use(tenantMiddleware)

export const projectRouter = createTRPCRouter({
  list: tenantProcedure
    .query(async ({ ctx }) => {
      // 自动租户隔离
      return ctx.prisma.project.findMany({
        where: { teamId: ctx.tenant.id }
      })
    }),
    
  create: tenantProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return ctx.prisma.project.create({
        data: {
          ...input,
          teamId: ctx.tenant.id // 自动设置租户ID
        }
      })
    })
})
```

#### 3. NextAuth.js多租户认证
```typescript
// pages/api/auth/[...nextauth].ts
export default NextAuth({
  callbacks: {
    async session({ session, token }) {
      if (session.user?.email) {
        // 获取用户的租户信息
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          include: {
            teamMembers: {
              include: { team: true }
            }
          }
        })
        
        session.user.id = user?.id
        session.user.teams = user?.teamMembers.map(tm => ({
          id: tm.team.id,
          name: tm.team.name,
          role: tm.role
        }))
      }
      return session
    }
  }
})
```

### T3 Stack多租户实现复杂度
- **开发工作量**: 2-3周
- **技术难度**: 中等
- **维护成本**: 中等
- **灵活性**: 高

## Strapi多租户支持情况

### 原生支持
Strapi **不提供原生的多租户支持**，社区有一些插件但不够成熟：

#### 1. 现有插件方案
```typescript
// strapi-plugin-multi-tenant (社区插件)
// 问题：
- 维护不活跃
- 功能不完整
- 与新版本Strapi兼容性问题
- 缺乏企业级功能
```

#### 2. 自定义实现方案
```typescript
// 需要大量自定义开发
// 1. 修改Strapi核心认证逻辑
// 2. 自定义权限系统
// 3. 数据隔离中间件
// 4. API路由改造

// 示例：自定义中间件
module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    // 获取租户信息
    const tenantId = ctx.request.header['x-tenant-id']
    
    // 修改查询条件
    if (ctx.request.url.startsWith('/api/')) {
      // 需要深度修改Strapi的查询逻辑
      // 复杂度极高
    }
    
    await next()
  }
}
```

### Strapi多租户实现复杂度
- **开发工作量**: 4-6周
- **技术难度**: 高
- **维护成本**: 高
- **灵活性**: 低

## 其他方案的多租户支持

### Supabase多租户支持 ✅
```typescript
// Row Level Security (RLS) 原生支持
-- 创建租户隔离策略
CREATE POLICY "Users can only see their team's projects" ON projects
  FOR ALL USING (team_id = auth.jwt() ->> 'team_id');

-- 自动租户隔离，无需应用层代码
```
**优势**: 数据库级别隔离，安全性最高

### ZenStack多租户支持 ✅
```typescript
// 声明式多租户规则
model Project {
  id     String @id
  teamId String
  team   Team   @relation(fields: [teamId], references: [id])
  
  // 自动租户隔离
  @@allow('all', team.members?[user == auth()])
}
```
**优势**: 声明式规则，自动生成隔离逻辑

### Boxyhq多租户支持 ✅
```typescript
// 原生企业级多租户
- 完整的团队管理
- 角色权限系统
- 数据隔离机制
- 域名路由支持
```
**优势**: 企业级成熟方案

## 重新评估推荐方案

基于多租户需求，重新排序：

| 方案 | 多租户支持 | 实现复杂度 | 开发周期 | 推荐指数 |
|------|------------|------------|----------|----------|
| **ZenStack + 本土化** | **原生支持** | **低** | **15-21周** | **⭐⭐⭐⭐⭐** |
| **Supabase + 本土化** | **原生支持** | **低** | **10-14周** | **⭐⭐⭐⭐⭐** |
| **Boxyhq改造** | **原生支持** | **低** | **8-12周** | **⭐⭐⭐⭐** |
| T3 Stack + 本土化 | 需要自实现 | 中 | 14-18周 | ⭐⭐⭐ |
| Strapi + 自定义 | 需要大量自定义 | 高 | 12-18周 | ⭐⭐ |

## 最终建议

考虑到多租户是核心需求，我重新推荐：

### 🥇 **ZenStack + 本土化**
- ✅ 声明式多租户规则
- ✅ 自动生成隔离逻辑
- ✅ 完全本土化
- ✅ 开发效率高

### 🥈 **Supabase + 本土化**
- ✅ RLS数据库级隔离
- ✅ 最高安全性
- ✅ 最快上线
- ✅ 实时协作功能

### 🥉 **Boxyhq改造 + 本土化**
- ✅ 企业级多租户
- ✅ 最低风险
- ✅ 最快MVP
- ⚠️ 需要支付系统改造

## 多租户实现示例

### ZenStack方案
```typescript
model Team {
  id      String @id @default(uuid())
  name    String
  members TeamMember[]
  projects Project[]
}

model Project {
  id     String @id @default(uuid())
  teamId String
  team   Team @relation(fields: [teamId], references: [id])
  
  // 自动多租户隔离
  @@allow('all', team.members?[user == auth()])
}
```

### Supabase方案
```sql
-- 自动租户隔离策略
CREATE POLICY "tenant_isolation" ON projects
  FOR ALL USING (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid()
    )
  );
```

你觉得哪个方案更适合？多租户确实是一个关键的架构决策点。