# 两层编辑器技术栈优化方案

## 优化目标

确定最终技术栈，避免后续变更，确保项目的技术一致性和可维护性。

## 最终技术栈决策

### 🎯 核心技术栈（不可变更）

| 分类 | 技术选择 | 版本 | 理由 |
|------|----------|------|------|
| **前端框架** | Next.js + React + TypeScript | 15.x + 18.x + 5.x | 与Boxyhq兼容，SSR支持 |
| **数据库** | PostgreSQL + Prisma | 6.9.x | 现有基础，关系型数据适合 |
| **认证系统** | NextAuth.js | 4.24.x | Boxyhq现有，成熟稳定 |
| **样式系统** | Tailwind CSS | 3.4.x | 现有基础，高度可定制 |
| **状态管理** | Zustand + TanStack Query | 4.x + 5.x | 轻量级，适合复杂编辑器状态 |
| **拖拽系统** | @dnd-kit | 18.x | 现代化，性能好，可访问性强 |
| **UI组件库** | shadcn/ui (Radix UI) | latest | 无样式冲突，高质量组件 |
| **表单处理** | React Hook Form + Zod | 7.x + 3.x | 性能好，类型安全 |
| **媒体处理** | Sharp + FFmpeg.wasm | 0.34.x + 0.12.x | 图片+视频处理能力 |
| **图标系统** | Lucide React | 0.4.x | 一致的图标风格 |

### 🚫 移除的依赖

以下现有依赖将被移除以避免冲突：

- `react-daisyui` - 替换为shadcn/ui
- `daisyui` - 替换为shadcn/ui
- `@boxyhq/react-ui` - 仅保留必要的Boxyhq功能
- `formik` + `yup` - 替换为React Hook Form + Zod
- `@heroicons/react` - 替换为Lucide React

### ➕ 新增的依赖

```json
{
  "dependencies": {
    "zustand": "^4.5.0",
    "@tanstack/react-query": "^5.17.0",
    "@dnd-kit/core": "^6.1.0",
    "@dnd-kit/sortable": "^8.0.0",
    "@dnd-kit/utilities": "^3.2.2",
    "react-hook-form": "^7.48.2",
    "zod": "^3.22.4",
    "lucide-react": "^0.303.0",
    "ffmpeg": "^0.0.4",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-slider": "^1.1.2",
    "@radix-ui/react-switch": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "@radix-ui/react-tooltip": "^1.0.7",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.2.0"
  },
  "devDependencies": {
    "@types/ffmpeg": "^1.0.4"
  }
}
```

## UI组件系统设计

### shadcn/ui 组件选择

基于编辑器需求，选择以下shadcn/ui组件：

```typescript
// 核心组件
- Button, Input, Label, Textarea
- Dialog, Sheet, Popover, Tooltip
- Select, Combobox, Command
- Tabs, Accordion, Collapsible
- Slider, Switch, Checkbox, RadioGroup
- Card, Badge, Avatar, Separator
- ScrollArea, ResizablePanels
- DropdownMenu, ContextMenu
- Form (基于React Hook Form)
- Table, DataTable
```

### 自定义组件扩展

```typescript
// 编辑器专用组件
- DragDropCanvas (基于@dnd-kit)
- PropertyPanel (动态表单)
- ComponentLibrary (组件展示)
- MediaBrowser (媒体选择器)
- ColorPicker (颜色选择)
- IconPicker (图标选择)
- CodeEditor (代码编辑)
- Timeline (动画时间轴)
```

## 状态管理架构

### Zustand Store 设计

```typescript
// 编辑器主状态
interface EditorStore {
  // 编辑模式
  mode: 'page' | 'component'
  setMode: (mode: 'page' | 'component') => void
  
  // 选中状态
  selectedComponentId: string | null
  setSelectedComponent: (id: string | null) => void
  
  // 拖拽状态
  draggedItem: DragItem | null
  setDraggedItem: (item: DragItem | null) => void
  
  // 历史记录
  history: HistoryState[]
  historyIndex: number
  undo: () => void
  redo: () => void
  
  // 页面数据
  pageData: PageData
  updatePageData: (data: Partial<PageData>) => void
  
  // 组件定义
  componentDefinitions: ComponentDefinition[]
  updateComponentDefinition: (id: string, definition: ComponentDefinition) => void
}

// 媒体库状态
interface MediaStore {
  assets: Asset[]
  folders: MediaFolder[]
  selectedAssets: string[]
  uploadProgress: Record<string, number>
  
  uploadAsset: (file: File, folderId?: string) => Promise<Asset>
  deleteAsset: (id: string) => Promise<void>
  createFolder: (name: string, parentId?: string) => Promise<MediaFolder>
}

// 组件市场状态
interface MarketplaceStore {
  components: ComponentListing[]
  categories: Category[]
  searchQuery: string
  filters: MarketplaceFilters
  
  searchComponents: (query: string) => Promise<void>
  purchaseComponent: (id: string) => Promise<void>
  publishComponent: (component: ComponentDefinition) => Promise<void>
}
```

### TanStack Query 使用

```typescript
// API查询hooks
const useProjects = () => useQuery({
  queryKey: ['projects'],
  queryFn: fetchProjects
})

const usePages = (projectId: string) => useQuery({
  queryKey: ['pages', projectId],
  queryFn: () => fetchPages(projectId)
})

const useComponentDefinitions = () => useQuery({
  queryKey: ['componentDefinitions'],
  queryFn: fetchComponentDefinitions
})

// 变更操作
const useUpdatePage = () => useMutation({
  mutationFn: updatePage,
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['pages'] })
  }
})
```

## 拖拽系统架构

### @dnd-kit 配置

```typescript
// 拖拽上下文配置
const dragDropConfig = {
  sensors: [
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  ],
  collisionDetection: closestCenter,
  modifiers: [restrictToWindowEdges],
}

// 拖拽类型定义
interface DragItem {
  type: 'component' | 'element' | 'asset'
  id: string
  data: any
}

// 放置区域配置
interface DropZone {
  id: string
  accepts: DragItem['type'][]
  onDrop: (item: DragItem) => void
}
```

## 媒体处理架构

### Sharp + FFmpeg.wasm 集成

```typescript
// 图片处理 (Sharp - 服务端)
const processImage = async (file: Buffer) => {
  const processed = await sharp(file)
    .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
    .jpeg({ quality: 85 })
    .toBuffer()
  
  // 生成缩略图
  const thumbnail = await sharp(file)
    .resize(300, 200, { fit: 'cover' })
    .jpeg({ quality: 70 })
    .toBuffer()
  
  return { processed, thumbnail }
}

// 视频处理 (FFmpeg.wasm - 客户端)
const processVideo = async (file: File) => {
  const ffmpeg = createFFmpeg({ log: true })
  await ffmpeg.load()
  
  ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(file))
  
  // 生成缩略图
  await ffmpeg.run('-i', 'input.mp4', '-ss', '00:00:01', '-vframes', '1', 'thumbnail.jpg')
  
  // 压缩视频
  await ffmpeg.run('-i', 'input.mp4', '-c:v', 'libx264', '-crf', '28', 'output.mp4')
  
  const thumbnail = ffmpeg.FS('readFile', 'thumbnail.jpg')
  const compressed = ffmpeg.FS('readFile', 'output.mp4')
  
  return { thumbnail, compressed }
}
```

## 迁移计划

### 阶段1：依赖清理和安装 (1-2天)

1. 移除冲突的UI依赖
2. 安装新的核心依赖
3. 更新TypeScript配置
4. 配置shadcn/ui

### 阶段2：UI组件迁移 (3-5天)

1. 创建shadcn/ui组件库
2. 迁移现有页面到新UI系统
3. 创建编辑器专用组件
4. 测试UI一致性

### 阶段3：状态管理重构 (2-3天)

1. 设置Zustand stores
2. 配置TanStack Query
3. 迁移现有状态逻辑
4. 测试状态同步

### 阶段4：拖拽系统集成 (2-3天)

1. 配置@dnd-kit
2. 实现拖拽组件
3. 集成到编辑器界面
4. 测试拖拽功能

### 阶段5：媒体处理集成 (2-3天)

1. 配置Sharp和FFmpeg.wasm
2. 实现媒体处理API
3. 创建媒体管理界面
4. 测试媒体功能

## 风险评估

### 高风险项
- UI组件迁移可能影响现有功能
- FFmpeg.wasm包体积较大，可能影响加载速度
- 状态管理重构可能引入新bug

### 缓解措施
- 分阶段迁移，保持功能可用
- 使用CDN加载FFmpeg.wasm，按需加载
- 充分测试，保持向后兼容

## 性能考虑

### 包体积优化
- 使用Tree Shaking移除未使用代码
- 按需加载重型依赖（FFmpeg.wasm）
- 使用动态导入分割代码

### 运行时性能
- Zustand轻量级状态管理
- TanStack Query智能缓存
- @dnd-kit高性能拖拽
- Sharp服务端图片处理

## 总结

这个技术栈优化方案：

✅ **统一性**: 移除冲突依赖，使用一致的UI系统
✅ **现代化**: 采用最新的最佳实践和工具
✅ **性能**: 选择高性能的轻量级解决方案
✅ **可维护性**: 类型安全，良好的开发体验
✅ **扩展性**: 支持复杂编辑器功能需求

**关键决策**：
1. 统一使用shadcn/ui作为UI组件库
2. Zustand + TanStack Query作为状态管理方案
3. @dnd-kit作为拖拽解决方案
4. React Hook Form + Zod作为表单方案
5. Sharp + FFmpeg.wasm作为媒体处理方案

这个技术栈将作为项目的最终选择，不再变更。