# Boxyhq改造详细计划

## 需要改动的核心模块

### 1. 支付系统改造 (最重要)

#### 1.1 移除Stripe依赖
```typescript
// 需要移除的文件和代码
- lib/stripe.ts (完全重写)
- pages/api/teams/[slug]/payments/ (重构)
- pages/api/webhooks/stripe.ts (删除)
- models/subscription.ts (部分重构)
- components/billing/ (UI重构)
```

#### 1.2 新增本土支付系统
```typescript
// 新增文件结构
lib/payments/
├── wechat-pay.ts          # 微信支付SDK集成
├── alipay.ts              # 支付宝SDK集成
├── unionpay.ts            # 银联支付SDK集成
├── payment-factory.ts     # 支付工厂模式
└── webhook-handlers.ts    # 本土支付回调处理

// 微信支付集成示例
import WxPay from 'wechatpay-node-v3'

export class WeChatPayService {
  private wxpay: WxPay
  
  constructor() {
    this.wxpay = new WxPay({
      appid: process.env.WECHAT_APPID!,
      mchid: process.env.WECHAT_MCHID!,
      publicKey: process.env.WECHAT_PUBLIC_KEY!,
      privateKey: process.env.WECHAT_PRIVATE_KEY!,
    })
  }
  
  async createOrder(params: CreateOrderParams) {
    return await this.wxpay.transactions_native({
      description: params.description,
      out_trade_no: params.orderNo,
      amount: {
        total: Math.round(params.amount * 100), // 转换为分
        currency: 'CNY'
      },
      notify_url: `${process.env.APP_URL}/api/webhooks/wechat-pay`
    })
  }
}
```

#### 1.3 数据模型扩展
```typescript
// 扩展 prisma/schema.prisma
model Payment {
  id            String   @id @default(uuid())
  teamId        String
  team          Team     @relation(fields: [teamId], references: [id])
  
  // 支付提供商
  provider      PaymentProvider // 'wechat' | 'alipay' | 'unionpay'
  
  // 订单信息
  orderNo       String   @unique
  amount        Float
  currency      String   @default("CNY")
  
  // 支付状态
  status        PaymentStatus // 'pending' | 'paid' | 'failed' | 'refunded'
  
  // 第三方信息
  providerOrderNo String?
  providerResponse Json?
  
  // 时间戳
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())
  paidAt        DateTime?
  
  @@index([teamId])
  @@index([provider, status])
}

enum PaymentProvider {
  WECHAT
  ALIPAY
  UNIONPAY
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}
```

### 2. 审计日志系统改造

#### 2.1 移除Retraced依赖
```typescript
// 需要移除的文件
- lib/retraced.ts (完全重写)

// 需要修改的文件 (移除sendAudit调用)
- pages/api/teams/[slug]/sso.ts
- pages/api/teams/[slug]/members.ts
- pages/api/teams/[slug]/invitations.ts
- 所有其他API文件中的审计调用
```

#### 2.2 新增自建审计系统
```typescript
// 新增审计系统
lib/audit/
├── audit-logger.ts       # 审计日志记录器
├── audit-viewer.ts       # 审计日志查看器
└── audit-types.ts        # 审计事件类型定义

// 自建审计日志记录器
export class AuditLogger {
  static async log(params: AuditLogParams) {
    await prisma.auditLog.create({
      data: {
        action: params.action,
        userId: params.userId,
        teamId: params.teamId,
        resource: params.resource,
        resourceId: params.resourceId,
        details: params.details,
        ipAddress: params.ipAddress,
        userAgent: params.userAgent,
        timestamp: new Date()
      }
    })
  }
  
  static async getTeamLogs(teamId: string, filters?: AuditFilters) {
    return await prisma.auditLog.findMany({
      where: {
        teamId,
        ...filters
      },
      include: {
        user: {
          select: { name: true, email: true }
        }
      },
      orderBy: { timestamp: 'desc' }
    })
  }
}
```

#### 2.3 审计数据模型
```typescript
// 扩展 prisma/schema.prisma
model AuditLog {
  id         String   @id @default(uuid())
  
  // 操作信息
  action     String   // 'user.create', 'project.delete' 等
  resource   String   // 'user', 'project', 'team' 等
  resourceId String?  // 资源ID
  
  // 用户和团队
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  teamId     String
  team       Team     @relation(fields: [teamId], references: [id])
  
  // 详细信息
  details    Json?    // 操作详情
  
  // 请求信息
  ipAddress  String?
  userAgent  String?
  
  // 时间戳
  timestamp  DateTime @default(now())
  
  @@index([teamId, timestamp])
  @@index([userId, timestamp])
  @@index([action])
}
```

### 3. SAML SSO系统配置

#### 3.1 Jackson自托管配置
```typescript
// 修改 lib/jackson.ts
const opts = {
  externalUrl: env.appUrl,
  samlPath: env.jackson.sso.path,
  oidcPath: env.jackson.sso.oidcPath,
  samlAudience: env.jackson.sso.issuer,
  db: {
    engine: 'sql',
    type: 'postgres',
    url: env.databaseUrl, // 使用本地数据库
  },
  // 移除外部依赖
  selfHosted: true,
  // 本土化配置
  idpDiscoveryPath: '/auth/sso/idp-select',
  idpEnabled: true,
}
```

#### 3.2 环境变量调整
```env
# 移除外部Jackson服务配置
# JACKSON_URL=
# JACKSON_EXTERNAL_URL=
# JACKSON_API_KEY=

# 使用自托管配置
JACKSON_PRODUCT_ID=your-product-id
JACKSON_WEBHOOK_SECRET=your-webhook-secret
```

### 4. 其他第三方服务本土化

#### 4.1 监控和错误追踪
```typescript
// 替换Sentry为本土监控
// 可选方案：
- 阿里云ARMS
- 腾讯云APM
- 自建监控系统

// 替换Mixpanel为本土分析
// 可选方案：
- 百度统计
- 腾讯分析
- 自建分析系统
```

#### 4.2 邮件服务本土化
```typescript
// 替换SMTP为本土邮件服务
// lib/email/sendEmail.ts 修改
import { AliyunEmailService } from './aliyun-email'
import { TencentEmailService } from './tencent-email'

export class EmailService {
  private provider: 'aliyun' | 'tencent' | 'smtp'
  
  async sendEmail(params: EmailParams) {
    switch (this.provider) {
      case 'aliyun':
        return await AliyunEmailService.send(params)
      case 'tencent':
        return await TencentEmailService.send(params)
      default:
        return await this.sendSMTP(params)
    }
  }
}
```

### 5. 新增编辑器模块

#### 5.1 目录结构
```
website-builder/
├── components/
│   ├── editor/              # 新增：编辑器组件
│   │   ├── PageEditor/
│   │   ├── ComponentEditor/
│   │   ├── MediaLibrary/
│   │   └── shared/
│   └── [existing components]
├── pages/
│   ├── editor/              # 新增：编辑器页面
│   │   ├── [projectId]/
│   │   │   ├── index.tsx    # 页面编辑器
│   │   │   └── components/  # 组件编辑器
│   │   └── projects/        # 项目管理
│   └── [existing pages]
├── lib/
│   ├── editor/              # 新增：编辑器逻辑
│   │   ├── stores/          # Zustand状态管理
│   │   ├── api/             # 编辑器API
│   │   └── utils/           # 工具函数
│   └── [existing lib]
```

#### 5.2 数据模型扩展
```typescript
// 扩展 prisma/schema.prisma
model Project {
  id          String   @id @default(uuid())
  name        String
  description String?
  
  // 团队关联 (利用现有多租户)
  teamId      String
  team        Team     @relation(fields: [teamId], references: [id])
  
  // 项目配置
  domain      String?  @unique
  subdomain   String?  @unique
  favicon     String?
  
  // 项目状态
  status      ProjectStatus @default(DRAFT)
  publishedAt DateTime?
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
  
  // 关系
  pages       Page[]
  
  @@index([teamId])
}

model Page {
  id        String @id @default(uuid())
  title     String
  slug      String
  
  // 项目关联
  projectId String
  project   Project @relation(fields: [projectId], references: [id])
  
  // 页面内容 (JSON格式)
  content   Json @default("{}")
  
  // SEO设置
  metaTitle       String?
  metaDescription String?
  
  // 页面设置
  isHomePage      Boolean @default(false)
  isPublished     Boolean @default(false)
  
  // 时间戳
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  
  @@unique([projectId, slug])
}
```

## 改造工程量评估

### 阶段1：支付系统改造 (4-5周)
- **微信支付集成**: 1.5周
- **支付宝集成**: 1.5周  
- **银联支付集成**: 1周
- **支付UI重构**: 1周

### 阶段2：审计系统改造 (2-3周)
- **自建审计系统**: 1.5周
- **审计UI开发**: 1周
- **现有API集成**: 0.5周

### 阶段3：其他服务本土化 (1-2周)
- **监控服务替换**: 0.5周
- **邮件服务本土化**: 0.5周
- **SAML配置调整**: 0.5周
- **环境变量整理**: 0.5周

### 阶段4：编辑器模块开发 (6-8周)
- **基础架构**: 1周
- **页面编辑器**: 3周
- **组件编辑器**: 3周
- **媒体管理**: 1周

## 总改造工程量

**总计**: 13-18周
**核心改造**: 7-10周 (支付+审计+本土化)
**编辑器开发**: 6-8周

## 风险评估

### 低风险项
- ✅ SAML SSO (只需配置调整)
- ✅ 编辑器模块 (独立开发)
- ✅ 多租户架构 (已有完整实现)

### 中风险项  
- ⚠️ 支付系统改造 (需要测试各种支付场景)
- ⚠️ 审计系统替换 (需要保证数据完整性)

### 高风险项
- 🚨 数据迁移 (如果有现有支付数据)

## 最终评估

Boxyhq改造方案的**核心优势**：
1. **保留企业级多租户架构** - 这是最大价值
2. **保留用户管理和权限系统** - 成熟稳定
3. **渐进式改造** - 可以分阶段上线
4. **风险可控** - 基于已验证的基础

**主要工作量**集中在支付系统本土化，这是必须要做的改造。

你觉得这个改造计划如何？哪些部分需要更详细的说明？