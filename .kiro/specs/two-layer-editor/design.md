# 两层编辑系统设计文档

## 概览

两层编辑系统是基于 Next.js 15 构建的多租户网站构建器，通过分离页面编辑和组件编辑两个层次，为不同技能水平的用户提供合适的编辑体验。系统采用现代化的技术栈，充分利用 Next.js 15 和 React 19 的最新特性，为编辑器提供卓越的性能和开发体验。

## 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                前端应用层 (Next.js 15 + React 19)            │
├─────────────────────────────────────────────────────────────┤
│  页面编辑器  │  组件编辑器  │  媒体管理  │  模板市场  │  发布系统  │
│  (并行路由)  │  (拦截路由)  │  (流式UI)  │  (ISR缓存) │  (静态导出) │
├─────────────────────────────────────────────────────────────┤
│              API 路由层 (App Router + Server Actions)        │
├─────────────────────────────────────────────────────────────┤
│  项目管理API │  组件管理API │  媒体API  │  交互API  │  发布API   │
│  (类型安全)  │  (实时同步)  │  (流处理) │  (事件驱动) │  (增量构建) │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (React Server Components)      │
├─────────────────────────────────────────────────────────────┤
│  编辑器引擎  │  组件引擎   │  媒体处理  │  交互引擎  │  静态生成   │
│  (React编译器)│ (组件缓存)  │ (并行处理) │ (状态机)  │ (Turbopack) │
├─────────────────────────────────────────────────────────────┤
│                数据访问层 (ZenStack + Prisma)                │
├─────────────────────────────────────────────────────────────┤
│  多租户权限  │  数据验证   │  关系管理  │  缓存策略  │  事务处理   │
├─────────────────────────────────────────────────────────────┤
│                    数据库层 (PostgreSQL)                     │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

基于现代化开发需求和性能考虑，采用以下技术栈：

- **前端框架**: Next.js 15 + React 19 + TypeScript
  - React Compiler: 自动优化编辑器组件重渲染
  - Turbopack: 开发服务器速度提升 76.7%
  - App Router: 并行路由支持多面板编辑器
  - Server Actions: 简化保存/发布操作
- **数据库**: PostgreSQL + ZenStack + Prisma
- **认证系统**: NextAuth.js v5 (Auth.js)
- **状态管理**: Zustand + TanStack Query v5
- **拖拽系统**: @dnd-kit/core + @dnd-kit/sortable
- **UI 组件**: shadcn/ui + Radix UI + Tailwind CSS
- **动画系统**: Framer Motion + CSS-in-JS
- **媒体处理**: Sharp + 客户端图片处理
- **静态生成**: Next.js 静态导出 + 自定义生成器

## 组件和接口设计

### 1. 编辑器核心组件

#### 两层编辑器主组件
```typescript
interface TwoLayerEditorProps {
  projectId: string
  pageId: string
  teamId: string
}

interface EditorState {
  mode: 'page' | 'component'
  selectedComponentId: string | null
  editingComponentId: string | null
  draggedItem: DragItem | null
  history: HistoryState[]
  historyIndex: number
}
```

#### 页面编辑器组件
```typescript
interface PageEditorProps {
  pageId: string
  onEditComponent: (componentId: string) => void
}

interface ComponentInstance {
  id: string
  definitionId: string
  props: Record<string, any>
  styles: Record<string, any>
  position: { x: number; y: number; width: number; height: number }
  zIndex: number
}
```

#### 组件编辑器组件
```typescript
interface ComponentEditorProps {
  componentId: string | null
  onSave: (component: ComponentDefinition) => void
}

interface ComponentDefinition {
  id: string
  name: string
  type: ComponentType
  structure: ElementNode[]
  exposedProps: PropDefinition[]
  exposedEvents: EventDefinition[]
  styles: StyleDefinition
}
```

### 2. 组件市场系统

#### 组件市场接口
```typescript
interface ComponentMarketplace {
  searchComponents: (query: ComponentSearchQuery) => Promise<ComponentListing[]>
  purchaseComponent: (componentId: string) => Promise<PurchaseResult>
  publishComponent: (component: ComponentDefinition) => Promise<PublishResult>
  reviewComponent: (componentId: string, review: ComponentReview) => Promise<void>
}

interface ComponentListing {
  id: string
  name: string
  description: string
  price: number | null // null 表示免费
  rating: number
  downloads: number
  preview: string
  tags: string[]
  creator: UserInfo
}
```

### 3. 媒体管理系统

#### 媒体库接口
```typescript
interface MediaLibrary {
  uploadAsset: (file: File, folderId?: string) => Promise<Asset>
  getAssets: (libraryId: string, filters?: AssetFilters) => Promise<Asset[]>
  createFolder: (name: string, parentId?: string) => Promise<MediaFolder>
  deleteAsset: (assetId: string) => Promise<void>
  generateVariants: (assetId: string) => Promise<AssetVariant[]>
}

interface Asset {
  id: string
  filename: string
  originalName: string
  url: string
  thumbnailUrl?: string
  type: AssetType
  size: number
  width?: number
  height?: number
  duration?: number
  variants: AssetVariant[]
}
```

### 4. 交互设计系统

#### 交互引擎接口
```typescript
interface InteractionEngine {
  createInteraction: (config: InteractionConfig) => Promise<Interaction>
  updateInteraction: (id: string, config: InteractionConfig) => Promise<Interaction>
  previewInteraction: (interaction: Interaction) => Promise<PreviewResult>
  generateInteractionCode: (interactions: Interaction[]) => string
}

interface InteractionConfig {
  trigger: TriggerConfig
  conditions: ConditionConfig[]
  actions: ActionConfig[]
}

interface TriggerConfig {
  type: 'click' | 'hover' | 'scroll' | 'form_submit' | 'page_load'
  selector: string
  options: Record<string, any>
}
```

### 5. 动画系统

#### 动画引擎接口
```typescript
interface AnimationEngine {
  createAnimation: (config: AnimationConfig) => Promise<Animation>
  createTimeline: (animations: Animation[]) => Promise<Timeline>
  previewAnimation: (animation: Animation) => Promise<PreviewResult>
  generateAnimationCSS: (animations: Animation[]) => string
}

interface AnimationConfig {
  type: AnimationType
  duration: number
  delay: number
  easing: string
  iterations: number
  keyframes: Keyframe[]
}
```

## 数据模型设计

### 扩展 Boxyhq 现有模型

基于 Boxyhq 现有的 User 和 Team 模型，扩展以下数据模型：

```prisma
// 扩展现有的 Team 模型
model Team {
  // ... Boxyhq 原有字段
  
  // 新增字段
  projects           Project[]
  componentDefinitions ComponentDefinition[]
  mediaLibraries     MediaLibrary[]
}

// 项目模型
model Project {
  id          String   @id @default(uuid())
  name        String
  description String?
  
  teamId      String
  team        Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  
  domain      String?  @unique
  subdomain   String?  @unique
  favicon     String?
  seoTitle    String?
  seoDesc     String?
  
  status      ProjectStatus @default(DRAFT)
  publishedAt DateTime?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
  
  pages              Page[]
  assets             Asset[]
  componentInstances ComponentInstance[]
  
  @@index([teamId])
  @@index([status])
}

// 页面模型
model Page {
  id        String @id @default(uuid())
  title     String
  slug      String
  
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  content   Json @default("{}")
  
  metaTitle       String?
  metaDescription String?
  metaKeywords    String?
  
  isHomePage      Boolean @default(false)
  isPublished     Boolean @default(false)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  
  componentInstances ComponentInstance[]
  
  @@unique([projectId, slug])
  @@index([projectId])
}

// 组件定义模型
model ComponentDefinition {
  id          String @id @default(uuid())
  name        String
  description String?
  
  creatorId   String?
  creator     User?    @relation(fields: [creatorId], references: [id])
  teamId      String?
  team        Team?    @relation(fields: [teamId], references: [id], onDelete: Cascade)
  
  type        ComponentType
  category    String
  version     String @default("1.0.0")
  
  structure   Json
  styles      Json
  scripts     Json?
  
  props       Json
  events      Json
  
  preview     String?
  tags        String[] @default([])
  
  status      ComponentStatus @default(DRAFT)
  isPublic    Boolean @default(false)
  
  price       Float?
  downloads   Int @default(0)
  rating      Float?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
  
  instances   ComponentInstance[]
  reviews     ComponentReview[]
  
  @@index([teamId])
  @@index([isPublic])
  @@index([downloads])
}
```

## 错误处理

### 错误类型定义
```typescript
enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  GENERATION_FAILED = 'GENERATION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

interface AppError {
  type: ErrorType
  message: string
  details?: Record<string, any>
  timestamp: Date
}
```

### 错误处理策略
1. **客户端错误**: 使用 React Error Boundary 捕获组件错误
2. **API 错误**: 统一的错误响应格式和状态码
3. **上传错误**: 文件上传失败时的重试机制
4. **生成错误**: 静态站点生成失败时的回滚机制

## 测试策略

### 单元测试
- 组件渲染测试
- 业务逻辑测试
- 工具函数测试

### 集成测试
- API 路由测试
- 数据库操作测试
- 文件上传测试

### E2E 测试
- 编辑器工作流测试
- 组件市场购买流程测试
- 网站发布流程测试

### 测试工具
- **单元测试**: Jest + React Testing Library
- **E2E 测试**: Playwright
- **API 测试**: Supertest