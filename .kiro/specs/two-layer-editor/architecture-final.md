# 两层编辑器 - 最终架构方案

## 🏗️ 完整的分层路由架构

### 路由分层设计

```
📁 平台层级 (所有人可访问)
├── / (平台官网首页 - 展示平台能力和项目)
├── /about, /pricing, /features (平台介绍页面)
└── /signin, /signup (认证页面)

📁 应用层级 (_app 布局 - 需要登录)
├── /dashboard (用户控制台)
├── /projects/* (项目管理)
├── /editor/* (编辑器)
└── /templates (模板库)

📁 管理员层级 (_admin 布局 - 管理员专用)
├── /admin (平台管理首页)
├── /admin/projects/new (创建平台项目)
└── /admin/content/* (编辑平台内容)

📁 用户网站展示层级
├── /site/{slug} (用户网站预览)
├── /site/{slug}/{page} (用户网站页面预览)
└── {custom-domain}/* (自定义域名访问)
```

### 项目类型分层

- **WEBSITE**: 用户个人网站项目
- **PLATFORM**: 平台展示项目（管理员创建，显示在首页）
- **TEAM**: 团队协作项目

## 🌐 自定义域名系统

### 域名状态管理
- `NONE`: 未设置域名
- `PENDING`: 等待验证
- `VERIFIED`: 已验证可用
- `FAILED`: 验证失败

### DNS 验证流程
1. 用户在项目设置中输入自定义域名
2. 系统提供 DNS 设置说明（CNAME + TXT 记录）
3. 自动验证域名所有权
4. 验证成功后启用自定义域名访问

### 智能路由处理
- 自动检测是否为自定义域名
- 根据域名查找对应的项目
- 渲染用户网站内容
- 提供编辑入口（仅对所有者）

## 🔐 ZenStack 权限系统

### 设计理念
- **声明式权限**：权限规则写在 schema.zmodel 中
- **自动化执行**：Prisma 客户端自动应用权限
- **零手动检查**：不需要手动写权限检查代码
- **类型安全**：编译时检查权限规则

### 权限规则示例
```zmodel
model Project {
  // ...字段定义
  
  @@allow('create', auth() != null)
  @@allow('read', 
    // 网站项目：所有者可见
    (type == WEBSITE && owner == auth()) ||
    // 平台项目：已发布的所有人可见，未发布的只有管理员可见
    (type == PLATFORM && isPublished) ||
    (type == PLATFORM && auth().role == ADMIN) ||
    // 团队项目：团队成员可见
    (type == TEAM && (owner == auth() || team.members?[user == auth()] || members?[user == auth()]))
  )
  @@allow('update', 
    // 网站项目：所有者可编辑
    (type == WEBSITE && owner == auth()) ||
    // 平台项目：只有管理员可编辑
    (type == PLATFORM && auth().role == ADMIN) ||
    // 团队项目：所有者或团队管理员或项目成员可编辑
    (type == TEAM && (owner == auth() || team.members?[user == auth() && role == ADMIN] || members?[user == auth() && role in [ADMIN, EDITOR]]))
  )
  @@allow('delete', 
    // 网站项目：所有者可删除
    (type == WEBSITE && owner == auth()) ||
    // 平台项目：只有管理员可删除
    (type == PLATFORM && auth().role == ADMIN) ||
    // 团队项目：所有者或团队管理员可删除
    (type == TEAM && (owner == auth() || team.members?[user == auth() && role == ADMIN]))
  )
}
```

## 🎯 用户使用流程

### 网站创建和发布流程
1. **创建项目**：在控制台创建网站项目
2. **编辑内容**：使用页面/组件编辑器设计网站
3. **预览测试**：通过 `/site/project-slug` 预览
4. **设置域名**：在项目设置中配置自定义域名
5. **DNS 配置**：按照说明设置 DNS 记录
6. **域名验证**：系统自动验证域名所有权
7. **正式发布**：用户域名正式指向网站

### 编辑器功能
- **页面编辑器**：拖拽式页面构建，响应式设计支持
- **组件编辑器**：可视化组件构建，高级属性管理
- **实时预览**：所见即所得的编辑体验
- **媒体管理**：层级文件夹管理，多格式文件支持
- **模板市场**：精美的市场界面，模板导入导出

## 📦 技术架构

### 核心技术栈
- **前端框架**: React 18 + TypeScript
- **全栈框架**: Remix
- **状态管理**: Zustand
- **UI 组件**: Radix UI + Tailwind CSS
- **动画库**: Framer Motion
- **数据库**: Prisma + ZenStack + PostgreSQL
- **构建工具**: Vite + tsup

### Monorepo 结构
```
packages/
├── ui/                    # 基础 UI 组件库
├── editor-core/          # 编辑器核心架构
├── page-editor/          # 页面编辑器
├── component-editor/     # 组件编辑器
├── media-manager/        # 媒体管理系统
├── template-market/      # 模板市场系统
├── preview-system/       # 实时预览系统
├── interaction-system/   # 交互设计系统
├── database/             # 数据库层 (ZenStack)
├── auth/                 # 认证系统
├── permissions/          # 权限系统
└── utils/                # 工具函数

apps/
├── builder/              # 主应用 (编辑器 + 控制台 + 平台)
└── component-editor-demo/# 组件编辑器演示
```

## 🎨 设计理念

### 用户体验优先
- 参考 Figma、Notion、Linear 等优秀产品的设计理念
- 流畅的动画和微交互
- 直观的可视化编辑界面
- 完善的键盘快捷键支持

### 现代化架构
- 严格的 TypeScript 类型系统
- 高度模块化的包结构
- 组件化和可复用设计
- 优秀的开发者体验

### 性能优化
- 虚拟化长列表
- 智能缓存策略
- 懒加载和代码分割
- 高效的状态管理

## 🚀 核心价值

### 技术价值
- 现代化的前端架构实践
- 复杂系统的模块化设计
- 优秀的用户体验设计
- 完整的权限控制系统

### 产品价值
- 降低网站开发门槛
- 提升设计师和开发者协作效率
- 构建完整的设计开发生态
- 支持自定义域名的专业网站

### 商业价值
- 开源的现代化编辑器解决方案
- 可复用的组件和设计模式
- 完整的技术文档和最佳实践
- 支持多种商业模式的平台架构

---

这个架构方案已经过完整验证，具备：
- ✅ 清晰的路由分层
- ✅ 完整的自定义域名支持
- ✅ 优雅的 ZenStack 权限控制
- ✅ 现代化的技术栈
- ✅ 优秀的用户体验设计

可以作为最终的技术架构指导文档。