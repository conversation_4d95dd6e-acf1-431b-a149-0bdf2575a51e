# Boxyhq目录架构重构计划

## 当前目录结构问题分析

### 现有目录结构
```
website-builder/
├── components/           # 企业功能组件混杂
│   ├── account/
│   ├── apiKey/
│   ├── auth/
│   ├── billing/         # 需要重构
│   ├── invitation/
│   ├── layouts/
│   ├── shared/
│   ├── team/
│   └── webhook/
├── pages/               # 页面路由混杂
│   ├── api/            # API路由
│   ├── auth/
│   ├── invitations/
│   ├── settings/
│   ├── teams/
│   └── [其他页面]
├── lib/                # 业务逻辑混杂
│   ├── auth/
│   ├── email/
│   ├── guards/
│   ├── jackson/
│   ├── middleware/
│   └── [其他工具]
├── models/             # 数据模型
├── hooks/              # React hooks
├── types/              # TypeScript类型
└── [其他配置文件]
```

### 存在的问题

1. **功能模块混杂**：企业功能和编辑器功能会混在一起
2. **组件分类不清**：缺乏明确的功能域划分
3. **API路由扁平**：所有API都在一个层级
4. **业务逻辑分散**：相关逻辑分布在不同目录
5. **缺乏编辑器专用结构**：没有为编辑器功能预留空间

## 目标目录架构设计

### 方案1：功能域分离架构
```
website-builder/
├── src/                    # 新增：源码根目录
│   ├── domains/           # 新增：按业务域组织
│   │   ├── auth/          # 认证域
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── api/
│   │   │   ├── hooks/
│   │   │   ├── types/
│   │   │   └── utils/
│   │   ├── team/          # 团队管理域
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── api/
│   │   │   ├── hooks/
│   │   │   └── models/
│   │   ├── billing/       # 计费域 (需要重构)
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── api/
│   │   │   ├── services/  # 支付服务
│   │   │   └── types/
│   │   ├── audit/         # 审计域 (新增)
│   │   │   ├── components/
│   │   │   ├── api/
│   │   │   ├── services/
│   │   │   └── types/
│   │   └── editor/        # 编辑器域 (新增)
│   │       ├── components/
│   │       │   ├── PageEditor/
│   │       │   ├── ComponentEditor/
│   │       │   ├── MediaLibrary/
│   │       │   └── shared/
│   │       ├── pages/
│   │       ├── api/
│   │       ├── stores/    # Zustand状态
│   │       ├── hooks/
│   │       ├── types/
│   │       └── utils/
│   ├── shared/            # 共享资源
│   │   ├── components/    # 通用组件
│   │   ├── hooks/         # 通用hooks
│   │   ├── utils/         # 工具函数
│   │   ├── types/         # 通用类型
│   │   └── constants/     # 常量
│   └── core/              # 核心基础设施
│       ├── auth/          # 认证核心
│       ├── database/      # 数据库配置
│       ├── middleware/    # 中间件
│       └── config/        # 配置管理
├── pages/                 # Next.js页面路由 (简化)
│   ├── api/              # API路由入口
│   ├── auth/             # 认证页面
│   ├── teams/            # 团队页面
│   ├── editor/           # 编辑器页面
│   └── [其他页面]
├── public/               # 静态资源
├── prisma/               # 数据库
├── styles/               # 样式
└── [配置文件]
```

### 方案2：分层架构 (推荐)
```
website-builder/
├── app/                   # 应用层
│   ├── (auth)/           # 认证相关页面组
│   ├── (dashboard)/      # 仪表板页面组
│   ├── (editor)/         # 编辑器页面组 (新增)
│   └── (admin)/          # 管理页面组
├── components/           # 组件层 (重新组织)
│   ├── ui/              # 基础UI组件
│   │   ├── button/
│   │   ├── input/
│   │   ├── modal/
│   │   └── [shadcn/ui组件]
│   ├── business/        # 业务组件
│   │   ├── auth/
│   │   ├── team/
│   │   ├── billing/     # 需要重构
│   │   └── audit/       # 新增
│   ├── editor/          # 编辑器组件 (新增)
│   │   ├── page-editor/
│   │   ├── component-editor/
│   │   ├── media-library/
│   │   └── shared/
│   └── layout/          # 布局组件
├── lib/                 # 业务逻辑层 (重新组织)
│   ├── auth/           # 认证逻辑
│   ├── team/           # 团队管理
│   ├── billing/        # 计费逻辑 (重构)
│   │   ├── wechat-pay.ts
│   │   ├── alipay.ts
│   │   └── payment-factory.ts
│   ├── audit/          # 审计逻辑 (新增)
│   │   ├── logger.ts
│   │   └── viewer.ts
│   ├── editor/         # 编辑器逻辑 (新增)
│   │   ├── stores/
│   │   ├── api/
│   │   └── utils/
│   ├── shared/         # 共享逻辑
│   └── core/           # 核心基础设施
├── pages/api/          # API路由 (重新组织)
│   ├── auth/
│   ├── teams/
│   ├── billing/        # 重构支付API
│   ├── audit/          # 新增审计API
│   └── editor/         # 新增编辑器API
├── hooks/              # React hooks (按功能分组)
│   ├── auth/
│   ├── team/
│   ├── billing/
│   ├── audit/
│   └── editor/         # 新增
├── types/              # TypeScript类型 (按功能分组)
│   ├── auth.ts
│   ├── team.ts
│   ├── billing.ts      # 重构
│   ├── audit.ts        # 新增
│   └── editor.ts       # 新增
├── stores/             # 状态管理 (新增)
│   ├── auth-store.ts
│   ├── team-store.ts
│   └── editor-store.ts # 编辑器状态
├── models/             # 数据模型 (保留但重新组织)
├── prisma/             # 数据库 (扩展)
└── [其他配置]
```

## 目录重构工程量评估

### 阶段1：基础架构调整 (2-3周)
```typescript
// 需要移动和重新组织的文件
移动文件数量：约150-200个文件
重新组织导入：约500-800个import语句
更新路径引用：约300-500个路径引用
```

**具体工作**：
- 创建新的目录结构
- 移动现有组件到对应域
- 更新所有import路径
- 调整TypeScript路径映射
- 更新构建配置

### 阶段2：计费模块重构 (3-4周)
```typescript
// 需要重构的计费相关文件
components/billing/          → components/business/billing/
lib/stripe.ts               → lib/billing/payment-providers/
pages/api/teams/[slug]/payments/ → pages/api/billing/
models/subscription.ts      → models/billing/
```

**具体工作**：
- 重构支付组件UI
- 重写支付服务逻辑
- 调整API路由结构
- 更新数据模型

### 阶段3：审计模块新增 (2-3周)
```typescript
// 新增审计模块结构
components/business/audit/   # 审计UI组件
lib/audit/                  # 审计逻辑
pages/api/audit/            # 审计API
hooks/audit/                # 审计hooks
types/audit.ts              # 审计类型
```

### 阶段4：编辑器模块新增 (1-2周架构，6-8周开发)
```typescript
// 新增编辑器模块结构
components/editor/          # 编辑器组件
lib/editor/                # 编辑器逻辑
pages/api/editor/          # 编辑器API
hooks/editor/              # 编辑器hooks
stores/editor-store.ts     # 编辑器状态
types/editor.ts            # 编辑器类型
```

## 重构策略和风险控制

### 渐进式重构策略
```typescript
// 阶段性重构，避免大爆炸式改动
Phase 1: 创建新结构，保留旧结构 (并行存在)
Phase 2: 逐个模块迁移，测试验证
Phase 3: 清理旧结构，统一引用
Phase 4: 新功能开发 (编辑器)
```

### 风险控制措施
1. **版本控制**：每个阶段创建分支
2. **自动化测试**：确保重构不破坏功能
3. **渐进式迁移**：避免一次性大改动
4. **路径别名**：使用TypeScript路径映射简化迁移

## 重构后的优势

### 1. 清晰的功能边界
- 每个业务域独立管理
- 减少模块间耦合
- 便于团队协作

### 2. 更好的可维护性
- 相关代码集中管理
- 清晰的依赖关系
- 便于单元测试

### 3. 扩展性增强
- 新功能有明确的放置位置
- 业务逻辑与UI分离
- 支持微前端架构演进

## 总工程量重新评估

| 阶段 | 原估算 | 目录重构 | 新总计 |
|------|--------|----------|--------|
| 支付系统改造 | 4-5周 | +1周 | 5-6周 |
| 审计系统改造 | 2-3周 | +1周 | 3-4周 |
| 基础架构调整 | - | 2-3周 | 2-3周 |
| 编辑器开发 | 6-8周 | +1周 | 7-9周 |
| **总计** | **12-16周** | **+5周** | **17-22周** |

## 最终建议

考虑到目录重构的复杂性，我建议：

### 🥇 重新推荐：ZenStack + 本土化
- **开发周期**：15-21周
- **无需重构现有复杂架构**
- **从零开始，架构清晰**
- **完全本土化**

### 🥈 Boxyhq改造 + 重构
- **开发周期**：17-22周
- **需要大量架构调整**
- **保留企业级功能**
- **渐进式改造**

目录重构确实增加了显著的工程量。你觉得是否还值得选择Boxyhq改造方案？