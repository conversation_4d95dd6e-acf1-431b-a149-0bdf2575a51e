import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { useLoaderData, Link } from '@remix-run/react'
import { requireAuth } from '~/lib/auth.server'
import { db } from '~/lib/db.server'
import { Button } from '@two-layer/ui'

/**
 * 编辑器首页加载器
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireAuth(request)
  
  // 获取用户最近的项目
  const recentProjects = await db.project.findMany({
    where: {
      OR: [
        { ownerId: user.id },
        {
          team: {
            members: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      ],
    },
    include: {
      team: true,
      pages: {
        take: 3,
        orderBy: { updatedAt: 'desc' },
      },
      components: {
        take: 3,
        orderBy: { updatedAt: 'desc' },
      },
    },
    orderBy: { updatedAt: 'desc' },
    take: 5,
  })
  
  return json({
    user,
    recentProjects,
  })
}

/**
 * 编辑器首页组件
 */
export default function EditorIndex() {
  const { user, recentProjects } = useLoaderData<typeof loader>()
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 欢迎区域 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          欢迎回来，{user.name}！
        </h1>
        <p className="text-gray-600">
          选择一个编辑模式开始创建精美的网站和组件
        </p>
      </div>
      
      {/* 编辑模式选择 */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {/* 页面编辑器 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">页面编辑器</h3>
              <p className="text-sm text-gray-600">使用组件构建完整的网页</p>
            </div>
          </div>
          <p className="text-gray-700 mb-4">
            拖拽现有组件到画布上，配置属性和样式，快速构建响应式网页。
          </p>
          <Button asChild className="w-full">
            <Link to="/dashboard">选择页面</Link>
          </Button>
        </div>
        
        {/* 组件编辑器 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">组件编辑器</h3>
              <p className="text-sm text-gray-600">创建可复用的自定义组件</p>
            </div>
          </div>
          <p className="text-gray-700 mb-4">
            使用基础元素构建自定义组件，定义属性接口，发布到组件库供他人使用。
          </p>
          <Button asChild variant="outline" className="w-full">
            <Link to="/dashboard">选择组件</Link>
          </Button>
        </div>
      </div>
      
      {/* 最近项目 */}
      {recentProjects.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">最近项目</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentProjects.map((project: typeof recentProjects[0]) => (
              <div key={project.id} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-gray-900 truncate">{project.name}</h3>
                  <span className="text-xs text-gray-500">{project.team.name}</span>
                </div>
                
                {project.description && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {project.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>{project.pages.length} 页面</span>
                  <span>{project.components.length} 组件</span>
                </div>
                
                <div className="flex space-x-2">
                  {project.pages.length > 0 && (
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link to={`/editor/page/${project.pages[0].id}`}>
                        编辑页面
                      </Link>
                    </Button>
                  )}
                  {project.components.length > 0 && (
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link to={`/editor/component/${project.components[0].id}`}>
                        编辑组件
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}