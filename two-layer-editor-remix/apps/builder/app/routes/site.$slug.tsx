// 用户网站展示页面
import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { optionalAuth } from "~/lib/auth.server";
import { getUserPrisma } from "~/lib/db.server";

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  if (data?.project) {
    return [
      { title: data.project.seoTitle || data.project.name },
      { name: "description", content: data.project.seoDescription || `${data.project.name} - 使用两层编辑器构建` },
    ];
  }

  return [
    { title: "网站不存在 - 两层编辑器" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await optionalAuth(request);
  const { slug } = params;
  
  if (!slug) {
    throw new Response("Not Found", { status: 404 });
  }
  
  const db = getUser<PERSON>risma(user);
  
  const project = await db.project.findFirst({
    where: {
      slug,
      isPublished: true,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      description: true,
      seoTitle: true,
      seoDescription: true,
      ownerId: true,
      owner: {
        select: { name: true },
      },
      pages: {
        where: { isPublished: true },
        orderBy: { createdAt: 'asc' },
        select: {
          id: true,
          title: true,
          slug: true,
          content: true,
          isHomePage: true,
        },
      },
    },
  });
  
  if (!project) {
    throw new Response("Not Found", { status: 404 });
  }
  
  const homePage = project.pages.find(page => page.isHomePage) || project.pages[0];
  const isOwner = user?.id === project.ownerId;
  
  return json({
    project,
    homePage,
    isOwner,
  });
}

export default function SiteSlug() {
  const { project, homePage, isOwner } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen">
      {/* 网站导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">
                {project.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {isOwner && (
                <a 
                  href={`/editor/project/${project.slug}`}
                  className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700"
                >
                  编辑网站
                </a>
              )}
              <a 
                href="/"
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                由两层编辑器构建
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* 网站内容 */}
      <main>
        {homePage ? (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="prose max-w-none">
              <h1>{homePage.title}</h1>
              {homePage.content && (
                <div>
                  {/* 这里需要实现内容渲染器 */}
                  <div className="bg-gray-100 p-8 rounded-lg text-center">
                    <h2 className="text-2xl font-bold mb-4">网站内容</h2>
                    <p className="text-gray-600 mb-4">
                      这里将渲染用户在编辑器中设计的页面内容
                    </p>
                    <div className="text-left">
                      <h3 className="font-semibold mb-2">页面数据预览：</h3>
                      <pre className="text-sm bg-white p-4 rounded border overflow-auto max-h-64">
                        {JSON.stringify(homePage.content, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {project.name}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              {project.description || "网站正在建设中..."}
            </p>
            {isOwner && (
              <div className="space-y-4">
                <p className="text-gray-500">
                  您还没有创建首页，点击下面的按钮开始编辑
                </p>
                <a 
                  href={`/projects/${project.slug}/pages/new`}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  创建首页
                </a>
              </div>
            )}
          </div>
        )}
      </main>

      {/* 网站页脚 */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>
              由 <strong>{project.owner.name}</strong> 创建 · 
              使用 <a href="/" className="text-blue-600 hover:text-blue-700">两层编辑器</a> 构建
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}