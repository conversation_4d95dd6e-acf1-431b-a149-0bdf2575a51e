// 项目自定义域名设置页面
import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { requireAuth } from "~/lib/auth.server";
import { getUserPrisma } from "~/lib/db.server";
import { setCustomDomain, generateDomainSetupInstructions, verifyDomainOwnership } from "~/lib/domain.server";
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle, Input, Label } from "@two-layer/ui";

export const meta: MetaFunction = () => {
  return [
    { title: "自定义域名 - 两层编辑器" },
    { name: "description", content: "为您的项目设置自定义域名" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  const { projectSlug } = params;
  
  if (!projectSlug) {
    throw new Response("Not Found", { status: 404 });
  }
  
  const db = getUserPrisma(user);
  
  const project = await db.project.findFirst({
    where: {
      slug: projectSlug,
      ownerId: user.id,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      customDomain: true,
      domainStatus: true,
      domainVerifiedAt: true,
    },
  });
  
  if (!project) {
    throw new Response("Not Found", { status: 404 });
  }
  
  // 生成域名设置指导
  const instructions = project.customDomain 
    ? generateDomainSetupInstructions(project.customDomain, project.slug)
    : null;
  
  return json({ user, project, instructions });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const user = await requireAuth(request);
  const { projectSlug } = params;
  
  if (!projectSlug) {
    throw new Response("Not Found", { status: 404 });
  }
  
  const db = getUserPrisma(user);
  
  const project = await db.project.findFirst({
    where: {
      slug: projectSlug,
      ownerId: user.id,
    },
  });
  
  if (!project) {
    throw new Response("Not Found", { status: 404 });
  }
  
  const formData = await request.formData();
  const action = formData.get("_action") as string;
  
  try {
    if (action === "set-domain") {
      const domain = formData.get("domain") as string;
      
      if (!domain) {
        return json({ error: "请输入域名" }, { status: 400 });
      }
      
      // 验证域名格式
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
      if (!domainRegex.test(domain)) {
        return json({ error: "域名格式不正确" }, { status: 400 });
      }
      
      await setCustomDomain(user, project.id, domain);
      
      return json({ success: "域名设置成功，请按照下面的指导配置DNS" });
      
    } else if (action === "verify-domain") {
      if (!project.customDomain) {
        return json({ error: "请先设置域名" }, { status: 400 });
      }
      
      const isVerified = await verifyDomainOwnership(project.customDomain);
      
      if (isVerified) {
        await db.project.update({
          where: { id: project.id },
          data: {
            domainStatus: 'VERIFIED',
            domainVerifiedAt: new Date(),
          },
        });
        
        return json({ success: "域名验证成功！" });
      } else {
        return json({ error: "域名验证失败，请检查DNS配置" }, { status: 400 });
      }
      
    } else if (action === "remove-domain") {
      await db.project.update({
        where: { id: project.id },
        data: {
          customDomain: null,
          domainStatus: 'NONE',
          domainVerifiedAt: null,
        },
      });
      
      return json({ success: "域名已移除" });
    }
    
    return json({ error: "无效的操作" }, { status: 400 });
    
  } catch (error) {
    console.error('域名操作失败:', error);
    return json({ 
      error: error instanceof Error ? error.message : "操作失败，请重试" 
    }, { status: 500 });
  }
}

export default function ProjectDomain() {
  const { user, project, instructions } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-8">
              <a href="/dashboard" className="text-xl font-bold text-gray-900">
                两层编辑器
              </a>
              <div className="hidden md:flex space-x-4">
                <a href="/dashboard" className="text-gray-600 hover:text-gray-900">
                  控制台
                </a>
                <a href="/projects" className="text-gray-600 hover:text-gray-900">
                  项目
                </a>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">{user.name}</span>
              <a href={`/projects/${project.slug}/pages`} className="text-sm text-gray-600 hover:text-gray-900">
                返回项目
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">自定义域名</h1>
          <p className="text-gray-600 mt-2">为项目 "{project.name}" 设置自定义域名</p>
        </div>

        {/* 消息提示 */}
        {actionData?.success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="text-green-800 text-sm">
              {actionData.success}
            </div>
          </div>
        )}

        {actionData?.error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-800 text-sm">
              {actionData.error}
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* 当前域名状态 */}
          <Card>
            <CardHeader>
              <CardTitle>域名状态</CardTitle>
              <CardDescription>
                当前项目的域名配置状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              {project.customDomain ? (
                <div className="space-y-4">
                  <div>
                    <Label>自定义域名</Label>
                    <div className="mt-1 text-lg font-medium">
                      {project.customDomain}
                    </div>
                  </div>
                  
                  <div>
                    <Label>验证状态</Label>
                    <div className="mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        project.domainStatus === 'VERIFIED' 
                          ? 'bg-green-100 text-green-800'
                          : project.domainStatus === 'PENDING'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {project.domainStatus === 'VERIFIED' && '已验证'}
                        {project.domainStatus === 'PENDING' && '等待验证'}
                        {project.domainStatus === 'FAILED' && '验证失败'}
                      </span>
                    </div>
                  </div>
                  
                  {project.domainVerifiedAt && (
                    <div>
                      <Label>验证时间</Label>
                      <div className="mt-1 text-sm text-gray-600">
                        {new Date(project.domainVerifiedAt).toLocaleString()}
                      </div>
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    {project.domainStatus !== 'VERIFIED' && (
                      <Form method="post">
                        <input type="hidden" name="_action" value="verify-domain" />
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? "验证中..." : "验证域名"}
                        </Button>
                      </Form>
                    )}
                    
                    <Form method="post">
                      <input type="hidden" name="_action" value="remove-domain" />
                      <Button variant="destructive" type="submit" disabled={isSubmitting}>
                        移除域名
                      </Button>
                    </Form>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    还没有设置自定义域名
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 设置域名 */}
          <Card>
            <CardHeader>
              <CardTitle>设置自定义域名</CardTitle>
              <CardDescription>
                输入您想要绑定的域名，例如：www.example.com
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="_action" value="set-domain" />
                
                <div>
                  <Label htmlFor="domain">域名</Label>
                  <Input
                    id="domain"
                    name="domain"
                    type="text"
                    placeholder="www.example.com"
                    defaultValue={project.customDomain || ""}
                    className="mt-1"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    请输入完整的域名，包括子域名（如www）
                  </p>
                </div>
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "设置中..." : "设置域名"}
                </Button>
              </Form>
            </CardContent>
          </Card>

          {/* DNS配置指导 */}
          {instructions && (
            <Card>
              <CardHeader>
                <CardTitle>DNS配置指导</CardTitle>
                <CardDescription>
                  请在您的域名提供商处添加以下DNS记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* CNAME记录 */}
                  <div>
                    <h4 className="font-medium mb-2">1. 添加CNAME记录</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <Label>类型</Label>
                          <div className="font-mono">{instructions.dns.type}</div>
                        </div>
                        <div>
                          <Label>名称</Label>
                          <div className="font-mono">{instructions.dns.name}</div>
                        </div>
                        <div>
                          <Label>值</Label>
                          <div className="font-mono">{instructions.dns.value}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* TXT记录 */}
                  <div>
                    <h4 className="font-medium mb-2">2. 添加TXT记录（用于验证）</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <Label>类型</Label>
                          <div className="font-mono">{instructions.verification.method}</div>
                        </div>
                        <div>
                          <Label>名称</Label>
                          <div className="font-mono">{instructions.verification.name}</div>
                        </div>
                        <div>
                          <Label>值</Label>
                          <div className="font-mono break-all">{instructions.verification.value}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 配置步骤 */}
                  <div>
                    <h4 className="font-medium mb-2">配置步骤</h4>
                    <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                      {instructions.instructions.map((step, index) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ol>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 注意事项 */}
          <Card>
            <CardHeader>
              <CardTitle>注意事项</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• DNS配置可能需要几分钟到几小时才能生效</li>
                <li>• 确保您的项目已发布，否则自定义域名无法访问</li>
                <li>• 我们会自动为您的域名配置SSL证书</li>
                <li>• 如果遇到问题，请联系技术支持</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}