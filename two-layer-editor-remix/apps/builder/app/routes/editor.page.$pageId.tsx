import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from '@remix-run/node'
import { useLoaderData, useActionData } from '@remix-run/react'
import { requireAuth } from '~/lib/auth.server'
import { db } from '~/lib/db.server'
import { checkPermission } from '~/lib/permissions.server'
import { PageEditor } from '~/components/editor/PageEditor'

/**
 * 页面编辑器加载器
 */
export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await requireAuth(request)
  const { pageId } = params
  
  if (!pageId) {
    throw new Response('Page ID is required', { status: 400 })
  }
  
  // 获取页面数据
  const page = await db.page.findUnique({
    where: { id: pageId },
    include: {
      project: {
        include: {
          team: true,
        },
      },
      components: {
        orderBy: { position: 'asc' },
      },
    },
  })
  
  if (!page) {
    throw new Response('Page not found', { status: 404 })
  }
  
  // 检查权限
  const hasPermission = await checkPermission(user, 'WRITE', page.project)
  if (!hasPermission) {
    throw new Response('Forbidden', { status: 403 })
  }
  
  // 获取可用的组件库
  const components = await db.component.findMany({
    where: {
      OR: [
        { isPublic: true },
        { createdById: user.id },
        {
          project: {
            teamId: page.project.teamId,
          },
        },
      ],
    },
    orderBy: { name: 'asc' },
  })
  
  return json({
    page,
    components,
    user,
  })
}

/**
 * 页面编辑器动作处理器
 */
export const action = async ({ request, params }: ActionFunctionArgs) => {
  const user = await requireAuth(request)
  const { pageId } = params
  
  if (!pageId) {
    throw new Response('Page ID is required', { status: 400 })
  }
  
  const formData = await request.formData()
  const intent = formData.get('intent') as string
  
  // 获取页面并检查权限
  const page = await db.page.findUnique({
    where: { id: pageId },
    include: { project: true },
  })
  
  if (!page) {
    throw new Response('Page not found', { status: 404 })
  }
  
  const hasPermission = await checkPermission(user, 'WRITE', page.project)
  if (!hasPermission) {
    throw new Response('Forbidden', { status: 403 })
  }
  
  try {
    switch (intent) {
      case 'updatePage': {
        const title = formData.get('title') as string
        const description = formData.get('description') as string
        const seoTitle = formData.get('seoTitle') as string
        const seoDescription = formData.get('seoDescription') as string
        
        const updatedPage = await db.page.update({
          where: { id: pageId },
          data: {
            title,
            description,
            seoTitle,
            seoDescription,
            updatedAt: new Date(),
          },
        })
        
        return json({ success: true, page: updatedPage })
      }
      
      case 'addComponent': {
        const componentId = formData.get('componentId') as string
        const parentId = formData.get('parentId') as string | null
        const position = parseInt(formData.get('position') as string) || 0
        const props = JSON.parse(formData.get('props') as string || '{}')
        const styles = JSON.parse(formData.get('styles') as string || '{}')
        
        const componentInstance = await db.componentInstance.create({
          data: {
            pageId,
            componentId,
            parentId: parentId || undefined,
            position,
            props,
            styles,
          },
        })
        
        return json({ success: true, componentInstance })
      }
      
      case 'updateComponent': {
        const instanceId = formData.get('instanceId') as string
        const props = JSON.parse(formData.get('props') as string || '{}')
        const styles = JSON.parse(formData.get('styles') as string || '{}')
        
        const componentInstance = await db.componentInstance.update({
          where: { id: instanceId },
          data: {
            props,
            styles,
            updatedAt: new Date(),
          },
        })
        
        return json({ success: true, componentInstance })
      }
      
      case 'removeComponent': {
        const instanceId = formData.get('instanceId') as string
        
        await db.componentInstance.delete({
          where: { id: instanceId },
        })
        
        return json({ success: true })
      }
      
      case 'moveComponent': {
        const instanceId = formData.get('instanceId') as string
        const parentId = formData.get('parentId') as string | null
        const position = parseInt(formData.get('position') as string) || 0
        
        const componentInstance = await db.componentInstance.update({
          where: { id: instanceId },
          data: {
            parentId: parentId || undefined,
            position,
            updatedAt: new Date(),
          },
        })
        
        return json({ success: true, componentInstance })
      }
      
      default:
        throw new Response('Invalid intent', { status: 400 })
    }
  } catch (error) {
    console.error('Page editor action error:', error)
    return json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * 页面编辑器组件
 */
export default function PageEditorRoute() {
  const { page, components, user } = useLoaderData<typeof loader>()
  const actionData = useActionData<typeof action>()
  
  return (
    <PageEditor
      page={page}
      components={components}
      user={user}
      actionData={actionData}
    />
  )
}