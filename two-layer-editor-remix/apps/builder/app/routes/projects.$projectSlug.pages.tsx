/**
 * 项目页面管理路由
 * 提供页面的创建、编辑、删除等管理功能
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { requireAuth } from "~/lib/auth.server";
import { getUserPrisma } from "~/lib/db.server";
import { checkPermission } from "~/lib/permissions.server";
import { PageManager } from "@two-layer/page-editor";
import type { Page, Project } from "@two-layer/types";

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `页面管理 - ${data?.project.name} - 两层编辑器` },
    { name: "description", content: `管理 ${data?.project.name} 项目的所有页面` },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  const db = getUserPrisma(user);
  
  const { projectSlug } = params;
  if (!projectSlug) {
    throw new Response("项目不存在", { status: 404 });
  }

  // 获取项目信息
  const project = await db.project.findFirst({
    where: {
      slug: projectSlug,
      OR: [
        { ownerId: user.id },
        { members: { some: { userId: user.id } } },
        { team: { members: { some: { userId: user.id } } } }
      ]
    },
    include: {
      owner: {
        select: { id: true, name: true, image: true }
      },
      team: {
        select: { id: true, name: true }
      }
    }
  });

  if (!project) {
    throw new Response("项目不存在", { status: 404 });
  }

  // 检查权限
  const hasPermission = await checkPermission(user.id, project.id, 'page.read');
  if (!hasPermission) {
    throw new Response("没有权限访问此项目", { status: 403 });
  }

  return json({
    user,
    project: project as Project
  });
}

export default function ProjectPagesRoute() {
  const { user, project } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  const handlePageEdit = (page: Page) => {
    // 跳转到页面编辑器
    navigate(`/projects/${project.slug}/editor/page/${page.id}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/projects/${project.slug}`)}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                ← 返回项目
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  {project.name}
                </h1>
                <p className="text-sm text-gray-600">页面管理</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/projects/${project.slug}/domain`)}
                className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700"
              >
                域名设置
              </button>
              <span className="text-sm text-gray-600">
                {user.name}
              </span>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PageManager
          project={project}
          onPageEdit={handlePageEdit}
        />
      </main>
    </div>
  );
}