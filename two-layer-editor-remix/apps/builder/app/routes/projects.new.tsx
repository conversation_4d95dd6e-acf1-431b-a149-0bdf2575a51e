// 创建新项目页面
import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import { requireAuth } from "~/lib/auth.server";
import { getUserPrisma } from "~/lib/db.server";
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle, Input, Label, Textarea } from "@two-layer/ui";

export const meta: MetaFunction = () => {
  return [
    { title: "创建项目 - 两层编辑器" },
    { name: "description", content: "创建一个新的网站项目" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireAuth(request);
  return json({ user });
}

export async function action({ request }: ActionFunctionArgs) {
  const user = await requireAuth(request);
  const db = getUserPrisma(user);
  
  const formData = await request.formData();
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const seoTitle = formData.get("seoTitle") as string;
  const seoDescription = formData.get("seoDescription") as string;
  
  if (!name) {
    return json({ error: "项目名称不能为空" }, { status: 400 });
  }
  
  // 生成项目slug
  const slug = name.toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fff]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '') + '-' + Date.now();
  
  try {
    const project = await db.project.create({
      data: {
        name,
        slug,
        description,
        seoTitle: seoTitle || name,
        seoDescription: seoDescription || description,
        ownerId: user.id,
        type: 'WEBSITE',
        settings: {},
      },
    });
    
    return redirect(`/projects/${project.slug}/pages`);
  } catch (error) {
    console.error('创建项目失败:', error);
    return json({ error: "创建项目失败，请重试" }, { status: 500 });
  }
}

export default function NewProject() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-8">
              <a href="/dashboard" className="text-xl font-bold text-gray-900">
                两层编辑器
              </a>
              <div className="hidden md:flex space-x-4">
                <a href="/dashboard" className="text-gray-600 hover:text-gray-900">
                  控制台
                </a>
                <a href="/projects" className="text-gray-600 hover:text-gray-900">
                  项目
                </a>
                <a href="/templates" className="text-gray-600 hover:text-gray-900">
                  模板
                </a>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/projects" className="text-sm text-gray-600 hover:text-gray-900">
                返回项目列表
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">创建新项目</h1>
          <p className="text-gray-600 mt-2">填写项目信息，开始构建您的网站</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>项目信息</CardTitle>
            <CardDescription>
              请填写项目的基本信息，这些信息可以在后续修改
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form method="post" className="space-y-6">
              <div>
                <Label htmlFor="name">项目名称 *</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  required
                  placeholder="例如：我的个人网站"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">项目描述</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="简单描述一下这个项目的用途..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="seoTitle">SEO标题</Label>
                <Input
                  id="seoTitle"
                  name="seoTitle"
                  type="text"
                  placeholder="网站在搜索引擎中显示的标题"
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  如果不填写，将使用项目名称作为SEO标题
                </p>
              </div>

              <div>
                <Label htmlFor="seoDescription">SEO描述</Label>
                <Textarea
                  id="seoDescription"
                  name="seoDescription"
                  placeholder="网站在搜索引擎中显示的描述"
                  className="mt-1"
                  rows={2}
                />
                <p className="text-sm text-gray-500 mt-1">
                  如果不填写，将使用项目描述作为SEO描述
                </p>
              </div>

              {actionData?.error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="text-red-800 text-sm">
                    {actionData.error}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <Button variant="outline" asChild>
                  <a href="/projects">取消</a>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "创建中..." : "创建项目"}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}