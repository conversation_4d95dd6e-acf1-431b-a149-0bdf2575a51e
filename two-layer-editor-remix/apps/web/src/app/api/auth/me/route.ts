import { NextRequest, NextResponse } from 'next/server'
import { getUserById } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('user-session')
    
    if (!sessionCookie) {
      return NextResponse.json({ user: null })
    }

    const sessionData = JSON.parse(sessionCookie.value)
    const user = await getUserById(sessionData.id)

    if (!user) {
      // 清除无效的会话
      const response = NextResponse.json({ user: null })
      response.cookies.delete('user-session')
      return response
    }

    return NextResponse.json({ 
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        avatar: user.image
      }
    })
  } catch (error) {
    console.error('获取用户信息错误:', error)
    return NextResponse.json({ user: null })
  }
}