import { AdminHeader } from '@/components/admin/header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@two-layer/ui/components/card'
import { Button } from '@two-layer/ui/components/button'
import { 
  Users, 
  FolderOpen, 
  Globe, 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react'

// 模拟数据
const stats = [
  {
    title: '总用户数',
    value: '2,847',
    change: '+12%',
    trend: 'up' as const,
    icon: Users,
  },
  {
    title: '活跃项目',
    value: '1,234',
    change: '+8%',
    trend: 'up' as const,
    icon: FolderOpen,
  },
  {
    title: '自定义域名',
    value: '456',
    change: '+23%',
    trend: 'up' as const,
    icon: Globe,
  },
  {
    title: '月访问量',
    value: '89.2K',
    change: '-3%',
    trend: 'down' as const,
    icon: BarChart3,
  },
]

const recentActivities = [
  {
    id: 1,
    user: '张三',
    action: '创建了新项目',
    target: '企业官网',
    time: '2分钟前',
  },
  {
    id: 2,
    user: '李四',
    action: '发布了网站',
    target: '个人博客',
    time: '5分钟前',
  },
  {
    id: 3,
    user: '王五',
    action: '绑定了域名',
    target: 'example.com',
    time: '10分钟前',
  },
  {
    id: 4,
    user: '赵六',
    action: '升级了套餐',
    target: '专业版',
    time: '15分钟前',
  },
]

export default function AdminDashboard() {
  return (
    <div>
      <AdminHeader 
        title="概览" 
        description="平台运营数据和系统状态总览"
      />
      
      <div className="p-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <div className="flex items-center text-xs text-gray-600 mt-1">
                  {stat.trend === 'up' ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                    {stat.change}
                  </span>
                  <span className="ml-1">较上月</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>
                常用的管理操作
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button className="h-20 flex-col space-y-2" variant="outline">
                  <FolderOpen className="h-6 w-6" />
                  <span>创建项目</span>
                </Button>
                <Button className="h-20 flex-col space-y-2" variant="outline">
                  <Users className="h-6 w-6" />
                  <span>用户管理</span>
                </Button>
                <Button className="h-20 flex-col space-y-2" variant="outline">
                  <Globe className="h-6 w-6" />
                  <span>域名管理</span>
                </Button>
                <Button className="h-20 flex-col space-y-2" variant="outline">
                  <BarChart3 className="h-6 w-6" />
                  <span>数据分析</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 最近活动 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                最近活动
              </CardTitle>
              <CardDescription>
                平台上的最新用户活动
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">{activity.user}</span>
                        {' '}{activity.action}{' '}
                        <span className="font-medium">{activity.target}</span>
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" className="w-full">
                  查看所有活动
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 系统状态 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>系统状态</CardTitle>
            <CardDescription>
              服务器和系统组件运行状态
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <div className="font-medium">Web 服务器</div>
                  <div className="text-sm text-gray-500">运行正常</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <div className="font-medium">数据库</div>
                  <div className="text-sm text-gray-500">运行正常</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div>
                  <div className="font-medium">CDN</div>
                  <div className="text-sm text-gray-500">部分节点延迟</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}