<script lang="ts">
	import type { HTMLAttributes } from 'svelte/elements'
	import type { WithElementRef } from 'bits-ui'
	import { cn } from '$lib/utils.ts'

	let { ref = $bindable(null), class: className, children, ...restProps }: WithElementRef<HTMLAttributes<HTMLElement>> = $props()
</script>

<div bind:this={ref} data-sidebar="group" class={cn('relative flex w-full min-w-0 flex-col p-2', className)} {...restProps}>
	{@render children?.()}
</div>
