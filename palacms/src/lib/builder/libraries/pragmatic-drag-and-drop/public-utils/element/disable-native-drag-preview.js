// In order to disable the native drag preview you can
// use `event.dataTransfer.setDragImage()` to set a small
// invisible image as the drag preview.
// There are alternative techniques,
// (eg setting opacity to in onGenerateDragPreview and then 1 in onDragStart)
// but the technique in this file worked best across browsers and platforms

// Here we are preloading the image so that it is ready for the first drag.
// Even though the image is base64 encoded, the browser queues an async task
// to decode the image. The image needs to be decoded before it can be used
var tinyTransparentImage = function () {
  // SSR safe
  if (typeof window === 'undefined') {
    return null;
  }

  // Image generated by: https://png-pixel.com/
  // It is a 1x1 transparent gif
  // It is the smallest possible transparent image we could find that works on all platforms
  // Note: using an encoded SVG would be nicer code, but it doesn't work on iOS
  var img = new Image();
  img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
  return img;
}();
export function disableNativeDragPreview(_ref) {
  var nativeSetDragImage = _ref.nativeSetDragImage;
  if (nativeSetDragImage && tinyTransparentImage) {
    nativeSetDragImage(tinyTransparentImage, 0, 0);
  }
}