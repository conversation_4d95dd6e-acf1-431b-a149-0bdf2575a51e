"use strict";var we=(e,n)=>()=>(n||e((n={exports:{}}).exports,n),n.exports);var Qe=we((gg,eu)=>{var rr=function(e){return e&&e.Math==Math&&e};eu.exports=rr(typeof globalThis=="object"&&globalThis)||rr(typeof window=="object"&&window)||rr(typeof self=="object"&&self)||rr(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var dt=we((yg,tu)=>{tu.exports=function(e){try{return!!e()}catch{return!0}}});var Ft=we((hg,ru)=>{var co=dt();ru.exports=!co(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var nr=we((vg,nu)=>{var po=dt();nu.exports=!po(function(){var e=function(){}.bind();return typeof e!="function"||e.hasOwnProperty("prototype")})});var At=we((Cg,uu)=>{var fo=nr(),ur=Function.prototype.call;uu.exports=fo?ur.bind(ur):function(){return ur.apply(ur,arguments)}});var ou=we(au=>{"use strict";var su={}.propertyIsEnumerable,iu=Object.getOwnPropertyDescriptor,Do=iu&&!su.call({1:2},1);au.f=Do?function(n){var t=iu(this,n);return!!t&&t.enumerable}:su});var sr=we((Fg,lu)=>{lu.exports=function(e,n){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:n}}});var ct=we((Ag,fu)=>{var cu=nr(),pu=Function.prototype,mo=pu.bind,Or=pu.call,go=cu&&mo.bind(Or,Or);fu.exports=cu?function(e){return e&&go(e)}:function(e){return e&&function(){return Or.apply(e,arguments)}}});var ir=we((Sg,mu)=>{var Du=ct(),yo=Du({}.toString),ho=Du("".slice);mu.exports=function(e){return ho(yo(e),8,-1)}});var gu=we((xg,du)=>{var vo=Qe(),Co=ct(),Eo=dt(),Fo=ir(),qr=vo.Object,Ao=Co("".split);du.exports=Eo(function(){return!qr("z").propertyIsEnumerable(0)})?function(e){return Fo(e)=="String"?Ao(e,""):qr(e)}:qr});var Mr=we((bg,yu)=>{var So=Qe(),xo=So.TypeError;yu.exports=function(e){if(e==null)throw xo("Can't call method on "+e);return e}});var ar=we((Tg,hu)=>{var bo=gu(),To=Mr();hu.exports=function(e){return bo(To(e))}});var pt=we((Bg,vu)=>{vu.exports=function(e){return typeof e=="function"}});var St=we((Ng,Cu)=>{var Bo=pt();Cu.exports=function(e){return typeof e=="object"?e!==null:Bo(e)}});var Mt=we((wg,Eu)=>{var Rr=Qe(),No=pt(),wo=function(e){return No(e)?e:void 0};Eu.exports=function(e,n){return arguments.length<2?wo(Rr[e]):Rr[e]&&Rr[e][n]}});var $r=we((_g,Fu)=>{var _o=ct();Fu.exports=_o({}.isPrototypeOf)});var Su=we((Pg,Au)=>{var Po=Mt();Au.exports=Po("navigator","userAgent")||""});var _u=we((kg,wu)=>{var Nu=Qe(),Vr=Su(),xu=Nu.process,bu=Nu.Deno,Tu=xu&&xu.versions||bu&&bu.version,Bu=Tu&&Tu.v8,mt,or;Bu&&(mt=Bu.split("."),or=mt[0]>0&&mt[0]<4?1:+(mt[0]+mt[1]));!or&&Vr&&(mt=Vr.match(/Edge\/(\d+)/),(!mt||mt[1]>=74)&&(mt=Vr.match(/Chrome\/(\d+)/),mt&&(or=+mt[1])));wu.exports=or});var Wr=we((Ig,ku)=>{var Pu=_u(),ko=dt();ku.exports=!!Object.getOwnPropertySymbols&&!ko(function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Pu&&Pu<41})});var Hr=we((Lg,Iu)=>{var Io=Wr();Iu.exports=Io&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Gr=we((jg,Lu)=>{var Lo=Qe(),jo=Mt(),Oo=pt(),qo=$r(),Mo=Hr(),Ro=Lo.Object;Lu.exports=Mo?function(e){return typeof e=="symbol"}:function(e){var n=jo("Symbol");return Oo(n)&&qo(n.prototype,Ro(e))}});var lr=we((Og,ju)=>{var $o=Qe(),Vo=$o.String;ju.exports=function(e){try{return Vo(e)}catch{return"Object"}}});var Rt=we((qg,Ou)=>{var Wo=Qe(),Ho=pt(),Go=lr(),Uo=Wo.TypeError;Ou.exports=function(e){if(Ho(e))return e;throw Uo(Go(e)+" is not a function")}});var cr=we((Mg,qu)=>{var Jo=Rt();qu.exports=function(e,n){var t=e[n];return t==null?void 0:Jo(t)}});var Ru=we((Rg,Mu)=>{var zo=Qe(),Ur=At(),Jr=pt(),zr=St(),Xo=zo.TypeError;Mu.exports=function(e,n){var t,s;if(n==="string"&&Jr(t=e.toString)&&!zr(s=Ur(t,e))||Jr(t=e.valueOf)&&!zr(s=Ur(t,e))||n!=="string"&&Jr(t=e.toString)&&!zr(s=Ur(t,e)))return s;throw Xo("Can't convert object to primitive value")}});var Vu=we(($g,$u)=>{$u.exports=!1});var pr=we((Vg,Hu)=>{var Wu=Qe(),Ko=Object.defineProperty;Hu.exports=function(e,n){try{Ko(Wu,e,{value:n,configurable:!0,writable:!0})}catch{Wu[e]=n}return n}});var fr=we((Wg,Uu)=>{var Yo=Qe(),Qo=pr(),Gu="__core-js_shared__",Zo=Yo[Gu]||Qo(Gu,{});Uu.exports=Zo});var Xr=we((Hg,zu)=>{var el=Vu(),Ju=fr();(zu.exports=function(e,n){return Ju[e]||(Ju[e]=n!==void 0?n:{})})("versions",[]).push({version:"3.22.2",mode:el?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.2/LICENSE",source:"https://github.com/zloirock/core-js"})});var Dr=we((Gg,Xu)=>{var tl=Qe(),rl=Mr(),nl=tl.Object;Xu.exports=function(e){return nl(rl(e))}});var ht=we((Ug,Ku)=>{var ul=ct(),sl=Dr(),il=ul({}.hasOwnProperty);Ku.exports=Object.hasOwn||function(n,t){return il(sl(n),t)}});var Kr=we((Jg,Yu)=>{var al=ct(),ol=0,ll=Math.random(),cl=al(1 .toString);Yu.exports=function(e){return"Symbol("+(e===void 0?"":e)+")_"+cl(++ol+ll,36)}});var bt=we((zg,rs)=>{var pl=Qe(),fl=Xr(),Qu=ht(),Dl=Kr(),Zu=Wr(),ts=Hr(),wt=fl("wks"),xt=pl.Symbol,es=xt&&xt.for,ml=ts?xt:xt&&xt.withoutSetter||Dl;rs.exports=function(e){if(!Qu(wt,e)||!(Zu||typeof wt[e]=="string")){var n="Symbol."+e;Zu&&Qu(xt,e)?wt[e]=xt[e]:ts&&es?wt[e]=es(n):wt[e]=ml(n)}return wt[e]}});var is=we((Xg,ss)=>{var dl=Qe(),gl=At(),ns=St(),us=Gr(),yl=cr(),hl=Ru(),vl=bt(),Cl=dl.TypeError,El=vl("toPrimitive");ss.exports=function(e,n){if(!ns(e)||us(e))return e;var t=yl(e,El),s;if(t){if(n===void 0&&(n="default"),s=gl(t,e,n),!ns(s)||us(s))return s;throw Cl("Can't convert object to primitive value")}return n===void 0&&(n="number"),hl(e,n)}});var mr=we((Kg,as)=>{var Fl=is(),Al=Gr();as.exports=function(e){var n=Fl(e,"string");return Al(n)?n:n+""}});var cs=we((Yg,ls)=>{var Sl=Qe(),os=St(),Yr=Sl.document,xl=os(Yr)&&os(Yr.createElement);ls.exports=function(e){return xl?Yr.createElement(e):{}}});var Qr=we((Qg,ps)=>{var bl=Ft(),Tl=dt(),Bl=cs();ps.exports=!bl&&!Tl(function(){return Object.defineProperty(Bl("div"),"a",{get:function(){return 7}}).a!=7})});var Zr=we(Ds=>{var Nl=Ft(),wl=At(),_l=ou(),Pl=sr(),kl=ar(),Il=mr(),Ll=ht(),jl=Qr(),fs=Object.getOwnPropertyDescriptor;Ds.f=Nl?fs:function(n,t){if(n=kl(n),t=Il(t),jl)try{return fs(n,t)}catch{}if(Ll(n,t))return Pl(!wl(_l.f,n,t),n[t])}});var ds=we((e0,ms)=>{var Ol=Ft(),ql=dt();ms.exports=Ol&&ql(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var _t=we((t0,ys)=>{var gs=Qe(),Ml=St(),Rl=gs.String,$l=gs.TypeError;ys.exports=function(e){if(Ml(e))return e;throw $l(Rl(e)+" is not an object")}});var gr=we(vs=>{var Vl=Qe(),Wl=Ft(),Hl=Qr(),Gl=ds(),dr=_t(),hs=mr(),Ul=Vl.TypeError,en=Object.defineProperty,Jl=Object.getOwnPropertyDescriptor,tn="enumerable",rn="configurable",nn="writable";vs.f=Wl?Gl?function(n,t,s){if(dr(n),t=hs(t),dr(s),typeof n=="function"&&t==="prototype"&&"value"in s&&nn in s&&!s[nn]){var a=Jl(n,t);a&&a[nn]&&(n[t]=s.value,s={configurable:rn in s?s[rn]:a[rn],enumerable:tn in s?s[tn]:a[tn],writable:!1})}return en(n,t,s)}:en:function(n,t,s){if(dr(n),t=hs(t),dr(s),Hl)try{return en(n,t,s)}catch{}if("get"in s||"set"in s)throw Ul("Accessors not supported");return"value"in s&&(n[t]=s.value),n}});var yr=we((n0,Cs)=>{var zl=Ft(),Xl=gr(),Kl=sr();Cs.exports=zl?function(e,n,t){return Xl.f(e,n,Kl(1,t))}:function(e,n,t){return e[n]=t,e}});var hr=we((u0,Es)=>{var Yl=ct(),Ql=pt(),un=fr(),Zl=Yl(Function.toString);Ql(un.inspectSource)||(un.inspectSource=function(e){return Zl(e)});Es.exports=un.inspectSource});var Ss=we((s0,As)=>{var ec=Qe(),tc=pt(),rc=hr(),Fs=ec.WeakMap;As.exports=tc(Fs)&&/native code/.test(rc(Fs))});var Ts=we((i0,bs)=>{var nc=Xr(),uc=Kr(),xs=nc("keys");bs.exports=function(e){return xs[e]||(xs[e]=uc(e))}});var sn=we((a0,Bs)=>{Bs.exports={}});var Is=we((o0,ks)=>{var sc=Ss(),Ps=Qe(),an=ct(),ic=St(),ac=yr(),on=ht(),ln=fr(),oc=Ts(),lc=sn(),Ns="Object already initialized",pn=Ps.TypeError,cc=Ps.WeakMap,vr,$t,Cr,pc=function(e){return Cr(e)?$t(e):vr(e,{})},fc=function(e){return function(n){var t;if(!ic(n)||(t=$t(n)).type!==e)throw pn("Incompatible receiver, "+e+" required");return t}};sc||ln.state?(vt=ln.state||(ln.state=new cc),ws=an(vt.get),cn=an(vt.has),_s=an(vt.set),vr=function(e,n){if(cn(vt,e))throw new pn(Ns);return n.facade=e,_s(vt,e,n),n},$t=function(e){return ws(vt,e)||{}},Cr=function(e){return cn(vt,e)}):(Tt=oc("state"),lc[Tt]=!0,vr=function(e,n){if(on(e,Tt))throw new pn(Ns);return n.facade=e,ac(e,Tt,n),n},$t=function(e){return on(e,Tt)?e[Tt]:{}},Cr=function(e){return on(e,Tt)});var vt,ws,cn,_s,Tt;ks.exports={set:vr,get:$t,has:Cr,enforce:pc,getterFor:fc}});var Os=we((l0,js)=>{var fn=Ft(),Dc=ht(),Ls=Function.prototype,mc=fn&&Object.getOwnPropertyDescriptor,Dn=Dc(Ls,"name"),dc=Dn&&function(){}.name==="something",gc=Dn&&(!fn||fn&&mc(Ls,"name").configurable);js.exports={EXISTS:Dn,PROPER:dc,CONFIGURABLE:gc}});var Vs=we((c0,$s)=>{var yc=Qe(),qs=pt(),hc=ht(),Ms=yr(),vc=pr(),Cc=hr(),Rs=Is(),Ec=Os().CONFIGURABLE,Fc=Rs.get,Ac=Rs.enforce,Sc=String(String).split("String");($s.exports=function(e,n,t,s){var a=s?!!s.unsafe:!1,r=s?!!s.enumerable:!1,u=s?!!s.noTargetGet:!1,i=s&&s.name!==void 0?s.name:n,o;if(qs(t)&&(String(i).slice(0,7)==="Symbol("&&(i="["+String(i).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!hc(t,"name")||Ec&&t.name!==i)&&Ms(t,"name",i),o=Ac(t),o.source||(o.source=Sc.join(typeof i=="string"?i:""))),e===yc){r?e[n]=t:vc(n,t);return}else a?!u&&e[n]&&(r=!0):delete e[n];r?e[n]=t:Ms(e,n,t)})(Function.prototype,"toString",function(){return qs(this)&&Fc(this).source||Cc(this)})});var Er=we((p0,Ws)=>{var xc=Math.ceil,bc=Math.floor;Ws.exports=function(e){var n=+e;return n!==n||n===0?0:(n>0?bc:xc)(n)}});var Gs=we((f0,Hs)=>{var Tc=Er(),Bc=Math.max,Nc=Math.min;Hs.exports=function(e,n){var t=Tc(e);return t<0?Bc(t+n,0):Nc(t,n)}});var Js=we((D0,Us)=>{var wc=Er(),_c=Math.min;Us.exports=function(e){return e>0?_c(wc(e),9007199254740991):0}});var Pt=we((m0,zs)=>{var Pc=Js();zs.exports=function(e){return Pc(e.length)}});var Ys=we((d0,Ks)=>{var kc=ar(),Ic=Gs(),Lc=Pt(),Xs=function(e){return function(n,t,s){var a=kc(n),r=Lc(a),u=Ic(s,r),i;if(e&&t!=t){for(;r>u;)if(i=a[u++],i!=i)return!0}else for(;r>u;u++)if((e||u in a)&&a[u]===t)return e||u||0;return!e&&-1}};Ks.exports={includes:Xs(!0),indexOf:Xs(!1)}});var ei=we((g0,Zs)=>{var jc=ct(),mn=ht(),Oc=ar(),qc=Ys().indexOf,Mc=sn(),Qs=jc([].push);Zs.exports=function(e,n){var t=Oc(e),s=0,a=[],r;for(r in t)!mn(Mc,r)&&mn(t,r)&&Qs(a,r);for(;n.length>s;)mn(t,r=n[s++])&&(~qc(a,r)||Qs(a,r));return a}});var ri=we((y0,ti)=>{ti.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var ui=we(ni=>{var Rc=ei(),$c=ri(),Vc=$c.concat("length","prototype");ni.f=Object.getOwnPropertyNames||function(n){return Rc(n,Vc)}});var ii=we(si=>{si.f=Object.getOwnPropertySymbols});var oi=we((C0,ai)=>{var Wc=Mt(),Hc=ct(),Gc=ui(),Uc=ii(),Jc=_t(),zc=Hc([].concat);ai.exports=Wc("Reflect","ownKeys")||function(n){var t=Gc.f(Jc(n)),s=Uc.f;return s?zc(t,s(n)):t}});var pi=we((E0,ci)=>{var li=ht(),Xc=oi(),Kc=Zr(),Yc=gr();ci.exports=function(e,n,t){for(var s=Xc(n),a=Yc.f,r=Kc.f,u=0;u<s.length;u++){var i=s[u];!li(e,i)&&!(t&&li(t,i))&&a(e,i,r(n,i))}}});var Di=we((F0,fi)=>{var Qc=dt(),Zc=pt(),ep=/#|\.prototype\./,Vt=function(e,n){var t=rp[tp(e)];return t==up?!0:t==np?!1:Zc(n)?Qc(n):!!n},tp=Vt.normalize=function(e){return String(e).replace(ep,".").toLowerCase()},rp=Vt.data={},np=Vt.NATIVE="N",up=Vt.POLYFILL="P";fi.exports=Vt});var Wt=we((A0,mi)=>{var dn=Qe(),sp=Zr().f,ip=yr(),ap=Vs(),op=pr(),lp=pi(),cp=Di();mi.exports=function(e,n){var t=e.target,s=e.global,a=e.stat,r,u,i,o,c,y;if(s?u=dn:a?u=dn[t]||op(t,{}):u=(dn[t]||{}).prototype,u)for(i in n){if(c=n[i],e.noTargetGet?(y=sp(u,i),o=y&&y.value):o=u[i],r=cp(s?i:t+(a?".":"#")+i,e.forced),!r&&o!==void 0){if(typeof c==typeof o)continue;lp(c,o)}(e.sham||o&&o.sham)&&ip(c,"sham",!0),ap(u,i,c,e)}}});var gn=we((S0,di)=>{var pp=ir();di.exports=Array.isArray||function(n){return pp(n)=="Array"}});var yn=we((x0,yi)=>{var gi=ct(),fp=Rt(),Dp=nr(),mp=gi(gi.bind);yi.exports=function(e,n){return fp(e),n===void 0?e:Dp?mp(e,n):function(){return e.apply(n,arguments)}}});var hn=we((b0,vi)=>{"use strict";var dp=Qe(),gp=gn(),yp=Pt(),hp=yn(),vp=dp.TypeError,hi=function(e,n,t,s,a,r,u,i){for(var o=a,c=0,y=u?hp(u,i):!1,h,g;c<s;){if(c in t){if(h=y?y(t[c],c,n):t[c],r>0&&gp(h))g=yp(h),o=hi(e,n,h,g,o,r-1)-1;else{if(o>=9007199254740991)throw vp("Exceed the acceptable array length");e[o]=h}o++}c++}return o};vi.exports=hi});var Fi=we((T0,Ei)=>{var Cp=bt(),Ep=Cp("toStringTag"),Ci={};Ci[Ep]="z";Ei.exports=String(Ci)==="[object z]"});var vn=we((B0,Ai)=>{var Fp=Qe(),Ap=Fi(),Sp=pt(),Fr=ir(),xp=bt(),bp=xp("toStringTag"),Tp=Fp.Object,Bp=Fr(function(){return arguments}())=="Arguments",Np=function(e,n){try{return e[n]}catch{}};Ai.exports=Ap?Fr:function(e){var n,t,s;return e===void 0?"Undefined":e===null?"Null":typeof(t=Np(n=Tp(e),bp))=="string"?t:Bp?Fr(n):(s=Fr(n))=="Object"&&Sp(n.callee)?"Arguments":s}});var Ni=we((N0,Bi)=>{var wp=ct(),_p=dt(),Si=pt(),Pp=vn(),kp=Mt(),Ip=hr(),xi=function(){},Lp=[],bi=kp("Reflect","construct"),Cn=/^\s*(?:class|function)\b/,jp=wp(Cn.exec),Op=!Cn.exec(xi),Ht=function(n){if(!Si(n))return!1;try{return bi(xi,Lp,n),!0}catch{return!1}},Ti=function(n){if(!Si(n))return!1;switch(Pp(n)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Op||!!jp(Cn,Ip(n))}catch{return!0}};Ti.sham=!0;Bi.exports=!bi||_p(function(){var e;return Ht(Ht.call)||!Ht(Object)||!Ht(function(){e=!0})||e})?Ti:Ht});var ki=we((w0,Pi)=>{var qp=Qe(),wi=gn(),Mp=Ni(),Rp=St(),$p=bt(),Vp=$p("species"),_i=qp.Array;Pi.exports=function(e){var n;return wi(e)&&(n=e.constructor,Mp(n)&&(n===_i||wi(n.prototype))?n=void 0:Rp(n)&&(n=n[Vp],n===null&&(n=void 0))),n===void 0?_i:n}});var En=we((_0,Ii)=>{var Wp=ki();Ii.exports=function(e,n){return new(Wp(e))(n===0?0:n)}});var Fn=we((P0,Li)=>{Li.exports={}});var Oi=we((k0,ji)=>{var Kp=bt(),Yp=Fn(),Qp=Kp("iterator"),Zp=Array.prototype;ji.exports=function(e){return e!==void 0&&(Yp.Array===e||Zp[Qp]===e)}});var An=we((I0,Mi)=>{var ef=vn(),qi=cr(),tf=Fn(),rf=bt(),nf=rf("iterator");Mi.exports=function(e){if(e!=null)return qi(e,nf)||qi(e,"@@iterator")||tf[ef(e)]}});var $i=we((L0,Ri)=>{var uf=Qe(),sf=At(),af=Rt(),of=_t(),lf=lr(),cf=An(),pf=uf.TypeError;Ri.exports=function(e,n){var t=arguments.length<2?cf(e):n;if(af(t))return of(sf(t,e));throw pf(lf(e)+" is not iterable")}});var Hi=we((j0,Wi)=>{var ff=At(),Vi=_t(),Df=cr();Wi.exports=function(e,n,t){var s,a;Vi(e);try{if(s=Df(e,"return"),!s){if(n==="throw")throw t;return t}s=ff(s,e)}catch(r){a=!0,s=r}if(n==="throw")throw t;if(a)throw s;return Vi(s),t}});var Xi=we((O0,zi)=>{var mf=Qe(),df=yn(),gf=At(),yf=_t(),hf=lr(),vf=Oi(),Cf=Pt(),Gi=$r(),Ef=$i(),Ff=An(),Ui=Hi(),Af=mf.TypeError,Ar=function(e,n){this.stopped=e,this.result=n},Ji=Ar.prototype;zi.exports=function(e,n,t){var s=t&&t.that,a=!!(t&&t.AS_ENTRIES),r=!!(t&&t.IS_ITERATOR),u=!!(t&&t.INTERRUPTED),i=df(n,s),o,c,y,h,g,p,D,v=function(T){return o&&Ui(o,"normal",T),new Ar(!0,T)},w=function(T){return a?(yf(T),u?i(T[0],T[1],v):i(T[0],T[1])):u?i(T,v):i(T)};if(r)o=e;else{if(c=Ff(e),!c)throw Af(hf(e)+" is not iterable");if(vf(c)){for(y=0,h=Cf(e);h>y;y++)if(g=w(e[y]),g&&Gi(Ji,g))return g;return new Ar(!1)}o=Ef(e,c)}for(p=o.next;!(D=gf(p,o)).done;){try{g=w(D.value)}catch(T){Ui(o,"throw",T)}if(typeof g=="object"&&g&&Gi(Ji,g))return g}return new Ar(!1)}});var Yi=we((q0,Ki)=>{"use strict";var Sf=mr(),xf=gr(),bf=sr();Ki.exports=function(e,n,t){var s=Sf(n);s in e?xf.f(e,s,bf(0,t)):e[s]=t}});var Qi=we(()=>{var wf=Wt(),_f=Qe();wf({global:!0},{globalThis:_f})});var Hp=Wt(),Gp=hn(),Up=Rt(),Jp=Dr(),zp=Pt(),Xp=En();Hp({target:"Array",proto:!0},{flatMap:function(n){var t=Jp(this),s=zp(t),a;return Up(n),a=Xp(t,0),a.length=Gp(a,t,t,s,0,1,n,arguments.length>1?arguments[1]:void 0),a}});var Tf=Wt(),Bf=Xi(),Nf=Yi();Tf({target:"Object",stat:!0},{fromEntries:function(n){var t={};return Bf(n,function(s,a){Nf(t,s,a)},{AS_ENTRIES:!0}),t}});Qi();var Pf=Wt(),kf=hn(),If=Dr(),Lf=Pt(),jf=Er(),Of=En();Pf({target:"Array",proto:!0},{flat:function(){var n=arguments.length?arguments[0]:void 0,t=If(this),s=Lf(t),a=Of(t,0);return a.length=kf(a,t,t,s,0,n===void 0?1:jf(n)),a}});var qf=["cliName","cliCategory","cliDescription"],Mf=["_"],Rf=["languageId"];function Pn(e,n){if(e==null)return{};var t=$f(e,n),s,a;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)s=r[a],!(n.indexOf(s)>=0)&&(!Object.prototype.propertyIsEnumerable.call(e,s)||(t[s]=e[s]))}return t}function $f(e,n){if(e==null)return{};var t={},s=Object.keys(e),a,r;for(r=0;r<s.length;r++)a=s[r],!(n.indexOf(a)>=0)&&(t[a]=e[a]);return t}var Vf=Object.create,Br=Object.defineProperty,Wf=Object.getOwnPropertyDescriptor,kn=Object.getOwnPropertyNames,Hf=Object.getPrototypeOf,Gf=Object.prototype.hasOwnProperty,gt=(e,n)=>function(){return e&&(n=(0,e[kn(e)[0]])(e=0)),n},ee=(e,n)=>function(){return n||(0,e[kn(e)[0]])((n={exports:{}}).exports,n),n.exports},Ut=(e,n)=>{for(var t in n)Br(e,t,{get:n[t],enumerable:!0})},ua=(e,n,t,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let a of kn(n))!Gf.call(e,a)&&a!==t&&Br(e,a,{get:()=>n[a],enumerable:!(s=Wf(n,a))||s.enumerable});return e},Uf=(e,n,t)=>(t=e!=null?Vf(Hf(e)):{},ua(n||!e||!e.__esModule?Br(t,"default",{value:e,enumerable:!0}):t,e)),ft=e=>ua(Br({},"__esModule",{value:!0}),e),Zi,ea,Bt,re=gt({"<define:process>"(){Zi={},ea=[],Bt={env:Zi,argv:ea}}}),sa=ee({"package.json"(e,n){n.exports={version:"2.8.0"}}}),Jf=ee({"node_modules/diff/lib/diff/base.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.default=n;function n(){}n.prototype={diff:function(r,u){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=i.callback;typeof i=="function"&&(o=i,i={}),this.options=i;var c=this;function y(A){return o?(setTimeout(function(){o(void 0,A)},0),!0):A}r=this.castInput(r),u=this.castInput(u),r=this.removeEmpty(this.tokenize(r)),u=this.removeEmpty(this.tokenize(u));var h=u.length,g=r.length,p=1,D=h+g,v=[{newPos:-1,components:[]}],w=this.extractCommon(v[0],u,r,0);if(v[0].newPos+1>=h&&w+1>=g)return y([{value:this.join(u),count:u.length}]);function T(){for(var A=-1*p;A<=p;A+=2){var B=void 0,k=v[A-1],P=v[A+1],R=(P?P.newPos:0)-A;k&&(v[A-1]=void 0);var f=k&&k.newPos+1<h,x=P&&0<=R&&R<g;if(!f&&!x){v[A]=void 0;continue}if(!f||x&&k.newPos<P.newPos?(B=s(P),c.pushComponent(B.components,void 0,!0)):(B=k,B.newPos++,c.pushComponent(B.components,!0,void 0)),R=c.extractCommon(B,u,r,A),B.newPos+1>=h&&R+1>=g)return y(t(c,B.components,u,r,c.useLongestToken));v[A]=B}p++}if(o)(function A(){setTimeout(function(){if(p>D)return o();T()||A()},0)})();else for(;p<=D;){var F=T();if(F)return F}},pushComponent:function(r,u,i){var o=r[r.length-1];o&&o.added===u&&o.removed===i?r[r.length-1]={count:o.count+1,added:u,removed:i}:r.push({count:1,added:u,removed:i})},extractCommon:function(r,u,i,o){for(var c=u.length,y=i.length,h=r.newPos,g=h-o,p=0;h+1<c&&g+1<y&&this.equals(u[h+1],i[g+1]);)h++,g++,p++;return p&&r.components.push({count:p}),r.newPos=h,g},equals:function(r,u){return this.options.comparator?this.options.comparator(r,u):r===u||this.options.ignoreCase&&r.toLowerCase()===u.toLowerCase()},removeEmpty:function(r){for(var u=[],i=0;i<r.length;i++)r[i]&&u.push(r[i]);return u},castInput:function(r){return r},tokenize:function(r){return r.split("")},join:function(r){return r.join("")}};function t(a,r,u,i,o){for(var c=0,y=r.length,h=0,g=0;c<y;c++){var p=r[c];if(p.removed){if(p.value=a.join(i.slice(g,g+p.count)),g+=p.count,c&&r[c-1].added){var v=r[c-1];r[c-1]=r[c],r[c]=v}}else{if(!p.added&&o){var D=u.slice(h,h+p.count);D=D.map(function(T,F){var A=i[g+F];return A.length>T.length?A:T}),p.value=a.join(D)}else p.value=a.join(u.slice(h,h+p.count));h+=p.count,p.added||(g+=p.count)}}var w=r[y-1];return y>1&&typeof w.value=="string"&&(w.added||w.removed)&&a.equals("",w.value)&&(r[y-2].value+=w.value,r.pop()),r}function s(a){return{newPos:a.newPos,components:a.components.slice(0)}}}}),zf=ee({"node_modules/diff/lib/diff/array.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.diffArrays=a,e.arrayDiff=void 0;var n=t(Jf());function t(r){return r&&r.__esModule?r:{default:r}}var s=new n.default;e.arrayDiff=s,s.tokenize=function(r){return r.slice()},s.join=s.removeEmpty=function(r){return r};function a(r,u,i){return s.diff(r,u,i)}}}),In=ee({"src/document/doc-builders.js"(e,n){"use strict";re();function t(E){return{type:"concat",parts:E}}function s(E){return{type:"indent",contents:E}}function a(E,l){return{type:"align",contents:l,n:E}}function r(E){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return{type:"group",id:l.id,contents:E,break:Boolean(l.shouldBreak),expandedStates:l.expandedStates}}function u(E){return a(Number.NEGATIVE_INFINITY,E)}function i(E){return a({type:"root"},E)}function o(E){return a(-1,E)}function c(E,l){return r(E[0],Object.assign(Object.assign({},l),{},{expandedStates:E}))}function y(E){return{type:"fill",parts:E}}function h(E,l){let d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return{type:"if-break",breakContents:E,flatContents:l,groupId:d.groupId}}function g(E,l){return{type:"indent-if-break",contents:E,groupId:l.groupId,negate:l.negate}}function p(E){return{type:"line-suffix",contents:E}}var D={type:"line-suffix-boundary"},v={type:"break-parent"},w={type:"trim"},T={type:"line",hard:!0},F={type:"line",hard:!0,literal:!0},A={type:"line"},B={type:"line",soft:!0},k=t([T,v]),P=t([F,v]),R={type:"cursor",placeholder:Symbol("cursor")};function f(E,l){let d=[];for(let C=0;C<l.length;C++)C!==0&&d.push(E),d.push(l[C]);return t(d)}function x(E,l,d){let C=E;if(l>0){for(let _=0;_<Math.floor(l/d);++_)C=s(C);C=a(l%d,C),C=a(Number.NEGATIVE_INFINITY,C)}return C}function m(E,l){return{type:"label",label:E,contents:l}}n.exports={concat:t,join:f,line:A,softline:B,hardline:k,literalline:P,group:r,conditionalGroup:c,fill:y,lineSuffix:p,lineSuffixBoundary:D,cursor:R,breakParent:v,ifBreak:h,trim:w,indent:s,indentIfBreak:g,align:a,addAlignmentToDoc:x,markAsRoot:i,dedentToRoot:u,dedent:o,hardlineWithoutBreakParent:T,literallineWithoutBreakParent:F,label:m}}}),Ln=ee({"src/common/end-of-line.js"(e,n){"use strict";re();function t(u){let i=u.indexOf("\r");return i>=0?u.charAt(i+1)===`
`?"crlf":"cr":"lf"}function s(u){switch(u){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function a(u,i){let o;switch(i){case`
`:o=/\n/g;break;case"\r":o=/\r/g;break;case`\r
`:o=/\r\n/g;break;default:throw new Error(`Unexpected "eol" ${JSON.stringify(i)}.`)}let c=u.match(o);return c?c.length:0}function r(u){return u.replace(/\r\n?/g,`
`)}n.exports={guessEndOfLine:t,convertEndOfLineToChars:s,countEndOfLineChars:a,normalizeEndOfLine:r}}}),lt=ee({"src/utils/get-last.js"(e,n){"use strict";re();var t=s=>s[s.length-1];n.exports=t}});function Xf(){let{onlyFirst:e=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(n,e?void 0:"g")}var Kf=gt({"node_modules/strip-ansi/node_modules/ansi-regex/index.js"(){re()}});function Yf(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(Xf(),"")}var Qf=gt({"node_modules/strip-ansi/index.js"(){re(),Kf()}});function Zf(e){return Number.isInteger(e)?e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141):!1}var eD=gt({"node_modules/is-fullwidth-code-point/index.js"(){re()}}),tD=ee({"node_modules/emoji-regex/index.js"(e,n){"use strict";re(),n.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}}}),ia={};Ut(ia,{default:()=>rD});function rD(e){if(typeof e!="string"||e.length===0||(e=Yf(e),e.length===0))return 0;e=e.replace((0,aa.default)(),"  ");let n=0;for(let t=0;t<e.length;t++){let s=e.codePointAt(t);s<=31||s>=127&&s<=159||s>=768&&s<=879||(s>65535&&t++,n+=Zf(s)?2:1)}return n}var aa,nD=gt({"node_modules/string-width/index.js"(){re(),Qf(),eD(),aa=Uf(tD())}}),oa=ee({"src/utils/get-string-width.js"(e,n){"use strict";re();var t=(nD(),ft(ia)).default,s=/[^\x20-\x7F]/;function a(r){return r?s.test(r)?t(r):r.length:0}n.exports=a}}),Jt=ee({"src/document/doc-utils.js"(e,n){"use strict";re();var t=lt(),{literalline:s,join:a}=In(),r=l=>Array.isArray(l)||l&&l.type==="concat",u=l=>{if(Array.isArray(l))return l;if(l.type!=="concat"&&l.type!=="fill")throw new Error("Expect doc type to be `concat` or `fill`.");return l.parts},i={};function o(l,d,C,_){let b=[l];for(;b.length>0;){let N=b.pop();if(N===i){C(b.pop());continue}if(C&&b.push(N,i),!d||d(N)!==!1)if(r(N)||N.type==="fill"){let I=u(N);for(let $=I.length,M=$-1;M>=0;--M)b.push(I[M])}else if(N.type==="if-break")N.flatContents&&b.push(N.flatContents),N.breakContents&&b.push(N.breakContents);else if(N.type==="group"&&N.expandedStates)if(_)for(let I=N.expandedStates.length,$=I-1;$>=0;--$)b.push(N.expandedStates[$]);else b.push(N.contents);else N.contents&&b.push(N.contents)}}function c(l,d){let C=new Map;return _(l);function _(N){if(C.has(N))return C.get(N);let I=b(N);return C.set(N,I),I}function b(N){if(Array.isArray(N))return d(N.map(_));if(N.type==="concat"||N.type==="fill"){let I=N.parts.map(_);return d(Object.assign(Object.assign({},N),{},{parts:I}))}if(N.type==="if-break"){let I=N.breakContents&&_(N.breakContents),$=N.flatContents&&_(N.flatContents);return d(Object.assign(Object.assign({},N),{},{breakContents:I,flatContents:$}))}if(N.type==="group"&&N.expandedStates){let I=N.expandedStates.map(_),$=I[0];return d(Object.assign(Object.assign({},N),{},{contents:$,expandedStates:I}))}if(N.contents){let I=_(N.contents);return d(Object.assign(Object.assign({},N),{},{contents:I}))}return d(N)}}function y(l,d,C){let _=C,b=!1;function N(I){let $=d(I);if($!==void 0&&(b=!0,_=$),b)return!1}return o(l,N),_}function h(l){if(l.type==="group"&&l.break||l.type==="line"&&l.hard||l.type==="break-parent")return!0}function g(l){return y(l,h,!1)}function p(l){if(l.length>0){let d=t(l);!d.expandedStates&&!d.break&&(d.break="propagated")}return null}function D(l){let d=new Set,C=[];function _(N){if(N.type==="break-parent"&&p(C),N.type==="group"){if(C.push(N),d.has(N))return!1;d.add(N)}}function b(N){N.type==="group"&&C.pop().break&&p(C)}o(l,_,b,!0)}function v(l){return l.type==="line"&&!l.hard?l.soft?"":" ":l.type==="if-break"?l.flatContents||"":l}function w(l){return c(l,v)}var T=(l,d)=>l&&l.type==="line"&&l.hard&&d&&d.type==="break-parent";function F(l){if(!l)return l;if(r(l)||l.type==="fill"){let d=u(l);for(;d.length>1&&T(...d.slice(-2));)d.length-=2;if(d.length>0){let C=F(t(d));d[d.length-1]=C}return Array.isArray(l)?d:Object.assign(Object.assign({},l),{},{parts:d})}switch(l.type){case"align":case"indent":case"indent-if-break":case"group":case"line-suffix":case"label":{let d=F(l.contents);return Object.assign(Object.assign({},l),{},{contents:d})}case"if-break":{let d=F(l.breakContents),C=F(l.flatContents);return Object.assign(Object.assign({},l),{},{breakContents:d,flatContents:C})}}return l}function A(l){return F(k(l))}function B(l){switch(l.type){case"fill":if(l.parts.every(C=>C===""))return"";break;case"group":if(!l.contents&&!l.id&&!l.break&&!l.expandedStates)return"";if(l.contents.type==="group"&&l.contents.id===l.id&&l.contents.break===l.break&&l.contents.expandedStates===l.expandedStates)return l.contents;break;case"align":case"indent":case"indent-if-break":case"line-suffix":if(!l.contents)return"";break;case"if-break":if(!l.flatContents&&!l.breakContents)return"";break}if(!r(l))return l;let d=[];for(let C of u(l)){if(!C)continue;let[_,...b]=r(C)?u(C):[C];typeof _=="string"&&typeof t(d)=="string"?d[d.length-1]+=_:d.push(_),d.push(...b)}return d.length===0?"":d.length===1?d[0]:Array.isArray(l)?d:Object.assign(Object.assign({},l),{},{parts:d})}function k(l){return c(l,d=>B(d))}function P(l){let d=[],C=l.filter(Boolean);for(;C.length>0;){let _=C.shift();if(!!_){if(r(_)){C.unshift(...u(_));continue}if(d.length>0&&typeof t(d)=="string"&&typeof _=="string"){d[d.length-1]+=_;continue}d.push(_)}}return d}function R(l){return c(l,d=>Array.isArray(d)?P(d):d.parts?Object.assign(Object.assign({},d),{},{parts:P(d.parts)}):d)}function f(l){return c(l,d=>typeof d=="string"&&d.includes(`
`)?x(d):d)}function x(l){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s;return a(d,l.split(`
`)).parts}function m(l){if(l.type==="line")return!0}function E(l){return y(l,m,!1)}n.exports={isConcat:r,getDocParts:u,willBreak:g,traverseDoc:o,findInDoc:y,mapDoc:c,propagateBreaks:D,removeLines:w,stripTrailingHardline:A,normalizeParts:P,normalizeDoc:R,cleanDoc:k,replaceTextEndOfLine:x,replaceEndOfLine:f,canBreak:E}}}),uD=ee({"src/document/doc-printer.js"(e,n){"use strict";re();var{convertEndOfLineToChars:t}=Ln(),s=lt(),a=oa(),{fill:r,cursor:u,indent:i}=In(),{isConcat:o,getDocParts:c}=Jt(),y,h=1,g=2;function p(){return{value:"",length:0,queue:[]}}function D(B,k){return w(B,{type:"indent"},k)}function v(B,k,P){return k===Number.NEGATIVE_INFINITY?B.root||p():k<0?w(B,{type:"dedent"},P):k?k.type==="root"?Object.assign(Object.assign({},B),{},{root:B}):w(B,{type:typeof k=="string"?"stringAlign":"numberAlign",n:k},P):B}function w(B,k,P){let R=k.type==="dedent"?B.queue.slice(0,-1):[...B.queue,k],f="",x=0,m=0,E=0;for(let I of R)switch(I.type){case"indent":C(),P.useTabs?l(1):d(P.tabWidth);break;case"stringAlign":C(),f+=I.n,x+=I.n.length;break;case"numberAlign":m+=1,E+=I.n;break;default:throw new Error(`Unexpected type '${I.type}'`)}return b(),Object.assign(Object.assign({},B),{},{value:f,length:x,queue:R});function l(I){f+="	".repeat(I),x+=P.tabWidth*I}function d(I){f+=" ".repeat(I),x+=I}function C(){P.useTabs?_():b()}function _(){m>0&&l(m),N()}function b(){E>0&&d(E),N()}function N(){m=0,E=0}}function T(B){if(B.length===0)return 0;let k=0;for(;B.length>0&&typeof s(B)=="string"&&/^[\t ]*$/.test(s(B));)k+=B.pop().length;if(B.length>0&&typeof s(B)=="string"){let P=s(B).replace(/[\t ]*$/,"");k+=s(B).length-P.length,B[B.length-1]=P}return k}function F(B,k,P,R,f){let x=k.length,m=[B],E=[];for(;P>=0;){if(m.length===0){if(x===0)return!0;m.push(k[--x]);continue}let{mode:l,doc:d}=m.pop();if(typeof d=="string")E.push(d),P-=a(d);else if(o(d)||d.type==="fill"){let C=c(d);for(let _=C.length-1;_>=0;_--)m.push({mode:l,doc:C[_]})}else switch(d.type){case"indent":case"align":case"indent-if-break":case"label":m.push({mode:l,doc:d.contents});break;case"trim":P+=T(E);break;case"group":{if(f&&d.break)return!1;let C=d.break?h:l,_=d.expandedStates&&C===h?s(d.expandedStates):d.contents;m.push({mode:C,doc:_});break}case"if-break":{let _=(d.groupId?y[d.groupId]||g:l)===h?d.breakContents:d.flatContents;_&&m.push({mode:l,doc:_});break}case"line":if(l===h||d.hard)return!0;d.soft||(E.push(" "),P--);break;case"line-suffix":R=!0;break;case"line-suffix-boundary":if(R)return!1;break}}return!1}function A(B,k){y={};let P=k.printWidth,R=t(k.endOfLine),f=0,x=[{ind:p(),mode:h,doc:B}],m=[],E=!1,l=[];for(;x.length>0;){let{ind:C,mode:_,doc:b}=x.pop();if(typeof b=="string"){let N=R!==`
`?b.replace(/\n/g,R):b;m.push(N),f+=a(N)}else if(o(b)){let N=c(b);for(let I=N.length-1;I>=0;I--)x.push({ind:C,mode:_,doc:N[I]})}else switch(b.type){case"cursor":m.push(u.placeholder);break;case"indent":x.push({ind:D(C,k),mode:_,doc:b.contents});break;case"align":x.push({ind:v(C,b.n,k),mode:_,doc:b.contents});break;case"trim":f-=T(m);break;case"group":switch(_){case g:if(!E){x.push({ind:C,mode:b.break?h:g,doc:b.contents});break}case h:{E=!1;let N={ind:C,mode:g,doc:b.contents},I=P-f,$=l.length>0;if(!b.break&&F(N,x,I,$))x.push(N);else if(b.expandedStates){let M=s(b.expandedStates);if(b.break){x.push({ind:C,mode:h,doc:M});break}else for(let q=1;q<b.expandedStates.length+1;q++)if(q>=b.expandedStates.length){x.push({ind:C,mode:h,doc:M});break}else{let J=b.expandedStates[q],L={ind:C,mode:g,doc:J};if(F(L,x,I,$)){x.push(L);break}}}else x.push({ind:C,mode:h,doc:b.contents});break}}b.id&&(y[b.id]=s(x).mode);break;case"fill":{let N=P-f,{parts:I}=b;if(I.length===0)break;let[$,M]=I,q={ind:C,mode:g,doc:$},J={ind:C,mode:h,doc:$},L=F(q,[],N,l.length>0,!0);if(I.length===1){L?x.push(q):x.push(J);break}let Y={ind:C,mode:g,doc:M},V={ind:C,mode:h,doc:M};if(I.length===2){L?x.push(Y,q):x.push(V,J);break}I.splice(0,2);let O={ind:C,mode:_,doc:r(I)},K=I[0];F({ind:C,mode:g,doc:[$,M,K]},[],N,l.length>0,!0)?x.push(O,Y,q):L?x.push(O,V,q):x.push(O,V,J);break}case"if-break":case"indent-if-break":{let N=b.groupId?y[b.groupId]:_;if(N===h){let I=b.type==="if-break"?b.breakContents:b.negate?b.contents:i(b.contents);I&&x.push({ind:C,mode:_,doc:I})}if(N===g){let I=b.type==="if-break"?b.flatContents:b.negate?i(b.contents):b.contents;I&&x.push({ind:C,mode:_,doc:I})}break}case"line-suffix":l.push({ind:C,mode:_,doc:b.contents});break;case"line-suffix-boundary":l.length>0&&x.push({ind:C,mode:_,doc:{type:"line",hard:!0}});break;case"line":switch(_){case g:if(b.hard)E=!0;else{b.soft||(m.push(" "),f+=1);break}case h:if(l.length>0){x.push({ind:C,mode:_,doc:b},...l.reverse()),l.length=0;break}b.literal?C.root?(m.push(R,C.root.value),f=C.root.length):(m.push(R),f=0):(f-=T(m),m.push(R+C.value),f=C.length);break}break;case"label":x.push({ind:C,mode:_,doc:b.contents});break;default:}x.length===0&&l.length>0&&(x.push(...l.reverse()),l.length=0)}let d=m.indexOf(u.placeholder);if(d!==-1){let C=m.indexOf(u.placeholder,d+1),_=m.slice(0,d).join(""),b=m.slice(d+1,C).join(""),N=m.slice(C+1).join("");return{formatted:_+b+N,cursorNodeStart:_.length,cursorNodeText:b}}return{formatted:m.join("")}}n.exports={printDocToString:A}}}),sD=ee({"src/document/doc-debug.js"(e,n){"use strict";re();var{isConcat:t,getDocParts:s}=Jt();function a(u){if(!u)return"";if(t(u)){let i=[];for(let o of s(u))if(t(o))i.push(...a(o).parts);else{let c=a(o);c!==""&&i.push(c)}return{type:"concat",parts:i}}return u.type==="if-break"?Object.assign(Object.assign({},u),{},{breakContents:a(u.breakContents),flatContents:a(u.flatContents)}):u.type==="group"?Object.assign(Object.assign({},u),{},{contents:a(u.contents),expandedStates:u.expandedStates&&u.expandedStates.map(a)}):u.type==="fill"?{type:"fill",parts:u.parts.map(a)}:u.contents?Object.assign(Object.assign({},u),{},{contents:a(u.contents)}):u}function r(u){let i=Object.create(null),o=new Set;return c(a(u));function c(h,g,p){if(typeof h=="string")return JSON.stringify(h);if(t(h)){let D=s(h).map(c).filter(Boolean);return D.length===1?D[0]:`[${D.join(", ")}]`}if(h.type==="line"){let D=Array.isArray(p)&&p[g+1]&&p[g+1].type==="break-parent";return h.literal?D?"literalline":"literallineWithoutBreakParent":h.hard?D?"hardline":"hardlineWithoutBreakParent":h.soft?"softline":"line"}if(h.type==="break-parent")return Array.isArray(p)&&p[g-1]&&p[g-1].type==="line"&&p[g-1].hard?void 0:"breakParent";if(h.type==="trim")return"trim";if(h.type==="indent")return"indent("+c(h.contents)+")";if(h.type==="align")return h.n===Number.NEGATIVE_INFINITY?"dedentToRoot("+c(h.contents)+")":h.n<0?"dedent("+c(h.contents)+")":h.n.type==="root"?"markAsRoot("+c(h.contents)+")":"align("+JSON.stringify(h.n)+", "+c(h.contents)+")";if(h.type==="if-break")return"ifBreak("+c(h.breakContents)+(h.flatContents?", "+c(h.flatContents):"")+(h.groupId?(h.flatContents?"":', ""')+`, { groupId: ${y(h.groupId)} }`:"")+")";if(h.type==="indent-if-break"){let D=[];h.negate&&D.push("negate: true"),h.groupId&&D.push(`groupId: ${y(h.groupId)}`);let v=D.length>0?`, { ${D.join(", ")} }`:"";return`indentIfBreak(${c(h.contents)}${v})`}if(h.type==="group"){let D=[];h.break&&h.break!=="propagated"&&D.push("shouldBreak: true"),h.id&&D.push(`id: ${y(h.id)}`);let v=D.length>0?`, { ${D.join(", ")} }`:"";return h.expandedStates?`conditionalGroup([${h.expandedStates.map(w=>c(w)).join(",")}]${v})`:`group(${c(h.contents)}${v})`}if(h.type==="fill")return`fill([${h.parts.map(D=>c(D)).join(", ")}])`;if(h.type==="line-suffix")return"lineSuffix("+c(h.contents)+")";if(h.type==="line-suffix-boundary")return"lineSuffixBoundary";if(h.type==="label")return`label(${JSON.stringify(h.label)}, ${c(h.contents)})`;throw new Error("Unknown doc type "+h.type)}function y(h){if(typeof h!="symbol")return JSON.stringify(String(h));if(h in i)return i[h];let g=String(h).slice(7,-1)||"symbol";for(let p=0;;p++){let D=g+(p>0?` #${p}`:"");if(!o.has(D))return o.add(D),i[h]=`Symbol.for(${JSON.stringify(D)})`}}}n.exports={printDocToDebug:r}}}),qe=ee({"src/document/index.js"(e,n){"use strict";re(),n.exports={builders:In(),printer:uD(),utils:Jt(),debug:sD()}}}),la={};Ut(la,{default:()=>iD});function iD(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var aD=gt({"node_modules/escape-string-regexp/index.js"(){re()}}),ca=ee({"node_modules/semver/internal/debug.js"(e,n){re();var t=typeof Bt=="object"&&Bt.env&&Bt.env.NODE_DEBUG&&/\bsemver\b/i.test(Bt.env.NODE_DEBUG)?function(){for(var s=arguments.length,a=new Array(s),r=0;r<s;r++)a[r]=arguments[r];return console.error("SEMVER",...a)}:()=>{};n.exports=t}}),pa=ee({"node_modules/semver/internal/constants.js"(e,n){re();var t="2.0.0",s=256,a=Number.MAX_SAFE_INTEGER||9007199254740991,r=16;n.exports={SEMVER_SPEC_VERSION:t,MAX_LENGTH:s,MAX_SAFE_INTEGER:a,MAX_SAFE_COMPONENT_LENGTH:r}}}),oD=ee({"node_modules/semver/internal/re.js"(e,n){re();var{MAX_SAFE_COMPONENT_LENGTH:t}=pa(),s=ca();e=n.exports={};var a=e.re=[],r=e.src=[],u=e.t={},i=0,o=(c,y,h)=>{let g=i++;s(c,g,y),u[c]=g,r[g]=y,a[g]=new RegExp(y,h?"g":void 0)};o("NUMERICIDENTIFIER","0|[1-9]\\d*"),o("NUMERICIDENTIFIERLOOSE","[0-9]+"),o("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),o("MAINVERSION",`(${r[u.NUMERICIDENTIFIER]})\\.(${r[u.NUMERICIDENTIFIER]})\\.(${r[u.NUMERICIDENTIFIER]})`),o("MAINVERSIONLOOSE",`(${r[u.NUMERICIDENTIFIERLOOSE]})\\.(${r[u.NUMERICIDENTIFIERLOOSE]})\\.(${r[u.NUMERICIDENTIFIERLOOSE]})`),o("PRERELEASEIDENTIFIER",`(?:${r[u.NUMERICIDENTIFIER]}|${r[u.NONNUMERICIDENTIFIER]})`),o("PRERELEASEIDENTIFIERLOOSE",`(?:${r[u.NUMERICIDENTIFIERLOOSE]}|${r[u.NONNUMERICIDENTIFIER]})`),o("PRERELEASE",`(?:-(${r[u.PRERELEASEIDENTIFIER]}(?:\\.${r[u.PRERELEASEIDENTIFIER]})*))`),o("PRERELEASELOOSE",`(?:-?(${r[u.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${r[u.PRERELEASEIDENTIFIERLOOSE]})*))`),o("BUILDIDENTIFIER","[0-9A-Za-z-]+"),o("BUILD",`(?:\\+(${r[u.BUILDIDENTIFIER]}(?:\\.${r[u.BUILDIDENTIFIER]})*))`),o("FULLPLAIN",`v?${r[u.MAINVERSION]}${r[u.PRERELEASE]}?${r[u.BUILD]}?`),o("FULL",`^${r[u.FULLPLAIN]}$`),o("LOOSEPLAIN",`[v=\\s]*${r[u.MAINVERSIONLOOSE]}${r[u.PRERELEASELOOSE]}?${r[u.BUILD]}?`),o("LOOSE",`^${r[u.LOOSEPLAIN]}$`),o("GTLT","((?:<|>)?=?)"),o("XRANGEIDENTIFIERLOOSE",`${r[u.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),o("XRANGEIDENTIFIER",`${r[u.NUMERICIDENTIFIER]}|x|X|\\*`),o("XRANGEPLAIN",`[v=\\s]*(${r[u.XRANGEIDENTIFIER]})(?:\\.(${r[u.XRANGEIDENTIFIER]})(?:\\.(${r[u.XRANGEIDENTIFIER]})(?:${r[u.PRERELEASE]})?${r[u.BUILD]}?)?)?`),o("XRANGEPLAINLOOSE",`[v=\\s]*(${r[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${r[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${r[u.XRANGEIDENTIFIERLOOSE]})(?:${r[u.PRERELEASELOOSE]})?${r[u.BUILD]}?)?)?`),o("XRANGE",`^${r[u.GTLT]}\\s*${r[u.XRANGEPLAIN]}$`),o("XRANGELOOSE",`^${r[u.GTLT]}\\s*${r[u.XRANGEPLAINLOOSE]}$`),o("COERCE",`(^|[^\\d])(\\d{1,${t}})(?:\\.(\\d{1,${t}}))?(?:\\.(\\d{1,${t}}))?(?:$|[^\\d])`),o("COERCERTL",r[u.COERCE],!0),o("LONETILDE","(?:~>?)"),o("TILDETRIM",`(\\s*)${r[u.LONETILDE]}\\s+`,!0),e.tildeTrimReplace="$1~",o("TILDE",`^${r[u.LONETILDE]}${r[u.XRANGEPLAIN]}$`),o("TILDELOOSE",`^${r[u.LONETILDE]}${r[u.XRANGEPLAINLOOSE]}$`),o("LONECARET","(?:\\^)"),o("CARETTRIM",`(\\s*)${r[u.LONECARET]}\\s+`,!0),e.caretTrimReplace="$1^",o("CARET",`^${r[u.LONECARET]}${r[u.XRANGEPLAIN]}$`),o("CARETLOOSE",`^${r[u.LONECARET]}${r[u.XRANGEPLAINLOOSE]}$`),o("COMPARATORLOOSE",`^${r[u.GTLT]}\\s*(${r[u.LOOSEPLAIN]})$|^$`),o("COMPARATOR",`^${r[u.GTLT]}\\s*(${r[u.FULLPLAIN]})$|^$`),o("COMPARATORTRIM",`(\\s*)${r[u.GTLT]}\\s*(${r[u.LOOSEPLAIN]}|${r[u.XRANGEPLAIN]})`,!0),e.comparatorTrimReplace="$1$2$3",o("HYPHENRANGE",`^\\s*(${r[u.XRANGEPLAIN]})\\s+-\\s+(${r[u.XRANGEPLAIN]})\\s*$`),o("HYPHENRANGELOOSE",`^\\s*(${r[u.XRANGEPLAINLOOSE]})\\s+-\\s+(${r[u.XRANGEPLAINLOOSE]})\\s*$`),o("STAR","(<|>)?=?\\s*\\*"),o("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),o("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}}),lD=ee({"node_modules/semver/internal/parse-options.js"(e,n){re();var t=["includePrerelease","loose","rtl"],s=a=>a?typeof a!="object"?{loose:!0}:t.filter(r=>a[r]).reduce((r,u)=>(r[u]=!0,r),{}):{};n.exports=s}}),cD=ee({"node_modules/semver/internal/identifiers.js"(e,n){re();var t=/^[0-9]+$/,s=(r,u)=>{let i=t.test(r),o=t.test(u);return i&&o&&(r=+r,u=+u),r===u?0:i&&!o?-1:o&&!i?1:r<u?-1:1},a=(r,u)=>s(u,r);n.exports={compareIdentifiers:s,rcompareIdentifiers:a}}}),pD=ee({"node_modules/semver/classes/semver.js"(e,n){re();var t=ca(),{MAX_LENGTH:s,MAX_SAFE_INTEGER:a}=pa(),{re:r,t:u}=oD(),i=lD(),{compareIdentifiers:o}=cD(),c=class{constructor(y,h){if(h=i(h),y instanceof c){if(y.loose===!!h.loose&&y.includePrerelease===!!h.includePrerelease)return y;y=y.version}else if(typeof y!="string")throw new TypeError(`Invalid Version: ${y}`);if(y.length>s)throw new TypeError(`version is longer than ${s} characters`);t("SemVer",y,h),this.options=h,this.loose=!!h.loose,this.includePrerelease=!!h.includePrerelease;let g=y.trim().match(h.loose?r[u.LOOSE]:r[u.FULL]);if(!g)throw new TypeError(`Invalid Version: ${y}`);if(this.raw=y,this.major=+g[1],this.minor=+g[2],this.patch=+g[3],this.major>a||this.major<0)throw new TypeError("Invalid major version");if(this.minor>a||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>a||this.patch<0)throw new TypeError("Invalid patch version");g[4]?this.prerelease=g[4].split(".").map(p=>{if(/^[0-9]+$/.test(p)){let D=+p;if(D>=0&&D<a)return D}return p}):this.prerelease=[],this.build=g[5]?g[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(y){if(t("SemVer.compare",this.version,this.options,y),!(y instanceof c)){if(typeof y=="string"&&y===this.version)return 0;y=new c(y,this.options)}return y.version===this.version?0:this.compareMain(y)||this.comparePre(y)}compareMain(y){return y instanceof c||(y=new c(y,this.options)),o(this.major,y.major)||o(this.minor,y.minor)||o(this.patch,y.patch)}comparePre(y){if(y instanceof c||(y=new c(y,this.options)),this.prerelease.length&&!y.prerelease.length)return-1;if(!this.prerelease.length&&y.prerelease.length)return 1;if(!this.prerelease.length&&!y.prerelease.length)return 0;let h=0;do{let g=this.prerelease[h],p=y.prerelease[h];if(t("prerelease compare",h,g,p),g===void 0&&p===void 0)return 0;if(p===void 0)return 1;if(g===void 0)return-1;if(g===p)continue;return o(g,p)}while(++h)}compareBuild(y){y instanceof c||(y=new c(y,this.options));let h=0;do{let g=this.build[h],p=y.build[h];if(t("prerelease compare",h,g,p),g===void 0&&p===void 0)return 0;if(p===void 0)return 1;if(g===void 0)return-1;if(g===p)continue;return o(g,p)}while(++h)}inc(y,h){switch(y){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",h);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",h);break;case"prepatch":this.prerelease.length=0,this.inc("patch",h),this.inc("pre",h);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",h),this.inc("pre",h);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{let g=this.prerelease.length;for(;--g>=0;)typeof this.prerelease[g]=="number"&&(this.prerelease[g]++,g=-2);g===-1&&this.prerelease.push(0)}h&&(o(this.prerelease[0],h)===0?isNaN(this.prerelease[1])&&(this.prerelease=[h,0]):this.prerelease=[h,0]);break;default:throw new Error(`invalid increment argument: ${y}`)}return this.format(),this.raw=this.version,this}};n.exports=c}}),jn=ee({"node_modules/semver/functions/compare.js"(e,n){re();var t=pD(),s=(a,r,u)=>new t(a,u).compare(new t(r,u));n.exports=s}}),fD=ee({"node_modules/semver/functions/lt.js"(e,n){re();var t=jn(),s=(a,r,u)=>t(a,r,u)<0;n.exports=s}}),DD=ee({"node_modules/semver/functions/gte.js"(e,n){re();var t=jn(),s=(a,r,u)=>t(a,r,u)>=0;n.exports=s}}),mD=ee({"src/utils/arrayify.js"(e,n){"use strict";re(),n.exports=(t,s)=>Object.entries(t).map(a=>{let[r,u]=a;return Object.assign({[s]:r},u)})}}),dD=ee({"node_modules/outdent/lib/index.js"(e,n){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.outdent=void 0;function t(){for(var F=[],A=0;A<arguments.length;A++)F[A]=arguments[A]}function s(){return typeof WeakMap<"u"?new WeakMap:a()}function a(){return{add:t,delete:t,get:t,set:t,has:function(F){return!1}}}var r=Object.prototype.hasOwnProperty,u=function(F,A){return r.call(F,A)};function i(F,A){for(var B in A)u(A,B)&&(F[B]=A[B]);return F}var o=/^[ \t]*(?:\r\n|\r|\n)/,c=/(?:\r\n|\r|\n)[ \t]*$/,y=/^(?:[\r\n]|$)/,h=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,g=/^[ \t]*[\r\n][ \t\r\n]*$/;function p(F,A,B){var k=0,P=F[0].match(h);P&&(k=P[1].length);var R="(\\r\\n|\\r|\\n).{0,"+k+"}",f=new RegExp(R,"g");A&&(F=F.slice(1));var x=B.newline,m=B.trimLeadingNewline,E=B.trimTrailingNewline,l=typeof x=="string",d=F.length,C=F.map(function(_,b){return _=_.replace(f,"$1"),b===0&&m&&(_=_.replace(o,"")),b===d-1&&E&&(_=_.replace(c,"")),l&&(_=_.replace(/\r\n|\n|\r/g,function(N){return x})),_});return C}function D(F,A){for(var B="",k=0,P=F.length;k<P;k++)B+=F[k],k<P-1&&(B+=A[k]);return B}function v(F){return u(F,"raw")&&u(F,"length")}function w(F){var A=s(),B=s();function k(R){for(var f=[],x=1;x<arguments.length;x++)f[x-1]=arguments[x];if(v(R)){var m=R,E=(f[0]===k||f[0]===T)&&g.test(m[0])&&y.test(m[1]),l=E?B:A,d=l.get(m);if(d||(d=p(m,E,F),l.set(m,d)),f.length===0)return d[0];var C=D(d,E?f.slice(1):f);return C}else return w(i(i({},F),R||{}))}var P=i(k,{string:function(R){return p([R],!1,F)[0]}});return P}var T=w({trimLeadingNewline:!0,trimTrailingNewline:!0});if(e.outdent=T,e.default=T,typeof n<"u")try{n.exports=T,Object.defineProperty(T,"__esModule",{value:!0}),T.default=T,T.outdent=T}catch{}}}),gD=ee({"src/main/core-options.js"(e,n){"use strict";re();var{outdent:t}=dD(),s="Config",a="Editor",r="Format",u="Other",i="Output",o="Global",c="Special",y={cursorOffset:{since:"1.4.0",category:c,type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:t`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:a},endOfLine:{since:"1.15.0",category:o,type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:t`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:c,type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:u,cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:c,type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:u},parser:{since:"0.0.10",category:o,type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:h=>typeof h=="string"||typeof h=="function",choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"acorn",since:"2.6.0",description:"JavaScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:o,description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:h=>typeof h=="string"||typeof h=="object",cliName:"plugin",cliCategory:s},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:o,description:t`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:h=>typeof h=="string"||typeof h=="object",cliName:"plugin-search-dir",cliCategory:s},printWidth:{since:"0.0.0",category:o,type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:c,type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:t`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:a},rangeStart:{since:"1.4.0",category:c,type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:t`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:a},requirePragma:{since:"1.7.0",category:c,type:"boolean",default:!1,description:t`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:u},tabWidth:{type:"int",category:o,default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:o,type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:o,type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};n.exports={CATEGORY_CONFIG:s,CATEGORY_EDITOR:a,CATEGORY_FORMAT:r,CATEGORY_OTHER:u,CATEGORY_OUTPUT:i,CATEGORY_GLOBAL:o,CATEGORY_SPECIAL:c,options:y}}}),On=ee({"src/main/support.js"(e,n){"use strict";re();var t={compare:jn(),lt:fD(),gte:DD()},s=mD(),a=sa().version,r=gD().options;function u(){let{plugins:o=[],showUnreleased:c=!1,showDeprecated:y=!1,showInternal:h=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},g=a.split("-",1)[0],p=o.flatMap(F=>F.languages||[]).filter(v),D=s(Object.assign({},...o.map(F=>{let{options:A}=F;return A}),r),"name").filter(F=>v(F)&&w(F)).sort((F,A)=>F.name===A.name?0:F.name<A.name?-1:1).map(T).map(F=>{F=Object.assign({},F),Array.isArray(F.default)&&(F.default=F.default.length===1?F.default[0].value:F.default.filter(v).sort((B,k)=>t.compare(k.since,B.since))[0].value),Array.isArray(F.choices)&&(F.choices=F.choices.filter(B=>v(B)&&w(B)),F.name==="parser"&&i(F,p,o));let A=Object.fromEntries(o.filter(B=>B.defaultOptions&&B.defaultOptions[F.name]!==void 0).map(B=>[B.name,B.defaultOptions[F.name]]));return Object.assign(Object.assign({},F),{},{pluginDefaults:A})});return{languages:p,options:D};function v(F){return c||!("since"in F)||F.since&&t.gte(g,F.since)}function w(F){return y||!("deprecated"in F)||F.deprecated&&t.lt(g,F.deprecated)}function T(F){if(h)return F;let{cliName:A,cliCategory:B,cliDescription:k}=F;return Pn(F,qf)}}function i(o,c,y){let h=new Set(o.choices.map(g=>g.value));for(let g of c)if(g.parsers){for(let p of g.parsers)if(!h.has(p)){h.add(p);let D=y.find(w=>w.parsers&&w.parsers[p]),v=g.name;D&&D.name&&(v+=` (plugin: ${D.name})`),o.choices.push({value:p,description:v})}}}n.exports={getSupportInfo:u}}}),qn=ee({"src/utils/is-non-empty-array.js"(e,n){"use strict";re();function t(s){return Array.isArray(s)&&s.length>0}n.exports=t}}),Nr=ee({"src/utils/text/skip.js"(e,n){"use strict";re();function t(i){return(o,c,y)=>{let h=y&&y.backwards;if(c===!1)return!1;let{length:g}=o,p=c;for(;p>=0&&p<g;){let D=o.charAt(p);if(i instanceof RegExp){if(!i.test(D))return p}else if(!i.includes(D))return p;h?p--:p++}return p===-1||p===g?p:!1}}var s=t(/\s/),a=t(" 	"),r=t(",; 	"),u=t(/[^\n\r]/);n.exports={skipWhitespace:s,skipSpaces:a,skipToLineEnd:r,skipEverythingButNewLine:u}}}),fa=ee({"src/utils/text/skip-inline-comment.js"(e,n){"use strict";re();function t(s,a){if(a===!1)return!1;if(s.charAt(a)==="/"&&s.charAt(a+1)==="*"){for(let r=a+2;r<s.length;++r)if(s.charAt(r)==="*"&&s.charAt(r+1)==="/")return r+2}return a}n.exports=t}}),Da=ee({"src/utils/text/skip-trailing-comment.js"(e,n){"use strict";re();var{skipEverythingButNewLine:t}=Nr();function s(a,r){return r===!1?!1:a.charAt(r)==="/"&&a.charAt(r+1)==="/"?t(a,r):r}n.exports=s}}),ma=ee({"src/utils/text/skip-newline.js"(e,n){"use strict";re();function t(s,a,r){let u=r&&r.backwards;if(a===!1)return!1;let i=s.charAt(a);if(u){if(s.charAt(a-1)==="\r"&&i===`
`)return a-2;if(i===`
`||i==="\r"||i==="\u2028"||i==="\u2029")return a-1}else{if(i==="\r"&&s.charAt(a+1)===`
`)return a+2;if(i===`
`||i==="\r"||i==="\u2028"||i==="\u2029")return a+1}return a}n.exports=t}}),yD=ee({"src/utils/text/get-next-non-space-non-comment-character-index-with-start-index.js"(e,n){"use strict";re();var t=fa(),s=ma(),a=Da(),{skipSpaces:r}=Nr();function u(i,o){let c=null,y=o;for(;y!==c;)c=y,y=r(i,y),y=t(i,y),y=a(i,y),y=s(i,y);return y}n.exports=u}}),Ge=ee({"src/common/util.js"(e,n){"use strict";re();var{default:t}=(aD(),ft(la)),s=lt(),{getSupportInfo:a}=On(),r=qn(),u=oa(),{skipWhitespace:i,skipSpaces:o,skipToLineEnd:c,skipEverythingButNewLine:y}=Nr(),h=fa(),g=Da(),p=ma(),D=yD(),v=V=>V[V.length-2];function w(V){return(O,K,se)=>{let Q=se&&se.backwards;if(K===!1)return!1;let{length:le}=O,W=K;for(;W>=0&&W<le;){let X=O.charAt(W);if(V instanceof RegExp){if(!V.test(X))return W}else if(!V.includes(X))return W;Q?W--:W++}return W===-1||W===le?W:!1}}function T(V,O){let K=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},se=o(V,K.backwards?O-1:O,K),Q=p(V,se,K);return se!==Q}function F(V,O,K){for(let se=O;se<K;++se)if(V.charAt(se)===`
`)return!0;return!1}function A(V,O,K){let se=K(O)-1;se=o(V,se,{backwards:!0}),se=p(V,se,{backwards:!0}),se=o(V,se,{backwards:!0});let Q=p(V,se,{backwards:!0});return se!==Q}function B(V,O){let K=null,se=O;for(;se!==K;)K=se,se=c(V,se),se=h(V,se),se=o(V,se);return se=g(V,se),se=p(V,se),se!==!1&&T(V,se)}function k(V,O,K){return B(V,K(O))}function P(V,O,K){return D(V,K(O))}function R(V,O,K){return V.charAt(P(V,O,K))}function f(V,O){let K=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return o(V,K.backwards?O-1:O,K)!==O}function x(V,O){let K=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,se=0;for(let Q=K;Q<V.length;++Q)V[Q]==="	"?se=se+O-se%O:se++;return se}function m(V,O){let K=V.lastIndexOf(`
`);return K===-1?0:x(V.slice(K+1).match(/^[\t ]*/)[0],O)}function E(V,O){let K={quote:'"',regex:/"/g,escaped:"&quot;"},se={quote:"'",regex:/'/g,escaped:"&apos;"},Q=O==="'"?se:K,le=Q===se?K:se,W=Q;if(V.includes(Q.quote)||V.includes(le.quote)){let X=(V.match(Q.regex)||[]).length,oe=(V.match(le.regex)||[]).length;W=X>oe?le:Q}return W}function l(V,O){let K=V.slice(1,-1),se=O.parser==="json"||O.parser==="json5"&&O.quoteProps==="preserve"&&!O.singleQuote?'"':O.__isInHtmlAttribute?"'":E(K,O.singleQuote?"'":'"').quote;return d(K,se,!(O.parser==="css"||O.parser==="less"||O.parser==="scss"||O.__embeddedInHtml))}function d(V,O,K){let se=O==='"'?"'":'"',Q=/\\(.)|(["'])/gs,le=V.replace(Q,(W,X,oe)=>X===se?X:oe===O?"\\"+oe:oe||(K&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(X)?X:"\\"+X));return O+le+O}function C(V){return V.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")}function _(V,O){let K=V.match(new RegExp(`(${t(O)})+`,"g"));return K===null?0:K.reduce((se,Q)=>Math.max(se,Q.length/O.length),0)}function b(V,O){let K=V.match(new RegExp(`(${t(O)})+`,"g"));if(K===null)return 0;let se=new Map,Q=0;for(let le of K){let W=le.length/O.length;se.set(W,!0),W>Q&&(Q=W)}for(let le=1;le<Q;le++)if(!se.get(le))return le;return Q+1}function N(V,O){(V.comments||(V.comments=[])).push(O),O.printed=!1,O.nodeDescription=Y(V)}function I(V,O){O.leading=!0,O.trailing=!1,N(V,O)}function $(V,O,K){O.leading=!1,O.trailing=!1,K&&(O.marker=K),N(V,O)}function M(V,O){O.leading=!1,O.trailing=!0,N(V,O)}function q(V,O){let{languages:K}=a({plugins:O.plugins}),se=K.find(Q=>{let{name:le}=Q;return le.toLowerCase()===V})||K.find(Q=>{let{aliases:le}=Q;return Array.isArray(le)&&le.includes(V)})||K.find(Q=>{let{extensions:le}=Q;return Array.isArray(le)&&le.includes(`.${V}`)});return se&&se.parsers[0]}function J(V){return V&&V.type==="front-matter"}function L(V){let O=new WeakMap;return function(K){return O.has(K)||O.set(K,Symbol(V)),O.get(K)}}function Y(V){let O=V.type||V.kind||"(unknown type)",K=String(V.name||V.id&&(typeof V.id=="object"?V.id.name:V.id)||V.key&&(typeof V.key=="object"?V.key.name:V.key)||V.value&&(typeof V.value=="object"?"":String(V.value))||V.operator||"");return K.length>20&&(K=K.slice(0,19)+"\u2026"),O+(K?" "+K:"")}n.exports={inferParserByLanguage:q,getStringWidth:u,getMaxContinuousCount:_,getMinNotPresentContinuousCount:b,getPenultimate:v,getLast:s,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:D,getNextNonSpaceNonCommentCharacterIndex:P,getNextNonSpaceNonCommentCharacter:R,skip:w,skipWhitespace:i,skipSpaces:o,skipToLineEnd:c,skipEverythingButNewLine:y,skipInlineComment:h,skipTrailingComment:g,skipNewline:p,isNextLineEmptyAfterIndex:B,isNextLineEmpty:k,isPreviousLineEmpty:A,hasNewline:T,hasNewlineInRange:F,hasSpaces:f,getAlignmentSize:x,getIndentSize:m,getPreferredQuote:E,printString:l,printNumber:C,makeString:d,addLeadingComment:I,addDanglingComment:$,addTrailingComment:M,isFrontMatterNode:J,isNonEmptyArray:r,createGroupIdMapper:L}}}),da={};Ut(da,{basename:()=>Ca,default:()=>Fa,delimiter:()=>Tn,dirname:()=>va,extname:()=>Ea,isAbsolute:()=>Rn,join:()=>ya,normalize:()=>Mn,relative:()=>ha,resolve:()=>Tr,sep:()=>bn});function ga(e,n){for(var t=0,s=e.length-1;s>=0;s--){var a=e[s];a==="."?e.splice(s,1):a===".."?(e.splice(s,1),t++):t&&(e.splice(s,1),t--)}if(n)for(;t--;t)e.unshift("..");return e}function Tr(){for(var e="",n=!1,t=arguments.length-1;t>=-1&&!n;t--){var s=t>=0?arguments[t]:"/";if(typeof s!="string")throw new TypeError("Arguments to path.resolve must be strings");if(!s)continue;e=s+"/"+e,n=s.charAt(0)==="/"}return e=ga($n(e.split("/"),function(a){return!!a}),!n).join("/"),(n?"/":"")+e||"."}function Mn(e){var n=Rn(e),t=Aa(e,-1)==="/";return e=ga($n(e.split("/"),function(s){return!!s}),!n).join("/"),!e&&!n&&(e="."),e&&t&&(e+="/"),(n?"/":"")+e}function Rn(e){return e.charAt(0)==="/"}function ya(){var e=Array.prototype.slice.call(arguments,0);return Mn($n(e,function(n,t){if(typeof n!="string")throw new TypeError("Arguments to path.join must be strings");return n}).join("/"))}function ha(e,n){e=Tr(e).substr(1),n=Tr(n).substr(1);function t(c){for(var y=0;y<c.length&&c[y]==="";y++);for(var h=c.length-1;h>=0&&c[h]==="";h--);return y>h?[]:c.slice(y,h-y+1)}for(var s=t(e.split("/")),a=t(n.split("/")),r=Math.min(s.length,a.length),u=r,i=0;i<r;i++)if(s[i]!==a[i]){u=i;break}for(var o=[],i=u;i<s.length;i++)o.push("..");return o=o.concat(a.slice(u)),o.join("/")}function va(e){var n=wr(e),t=n[0],s=n[1];return!t&&!s?".":(s&&(s=s.substr(0,s.length-1)),t+s)}function Ca(e,n){var t=wr(e)[2];return n&&t.substr(-1*n.length)===n&&(t=t.substr(0,t.length-n.length)),t}function Ea(e){return wr(e)[3]}function $n(e,n){if(e.filter)return e.filter(n);for(var t=[],s=0;s<e.length;s++)n(e[s],s,e)&&t.push(e[s]);return t}var ta,wr,bn,Tn,Fa,Aa,hD=gt({"node-modules-polyfills:path"(){re(),ta=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,wr=function(e){return ta.exec(e).slice(1)},bn="/",Tn=":",Fa={extname:Ea,basename:Ca,dirname:va,sep:bn,delimiter:Tn,relative:ha,join:ya,isAbsolute:Rn,normalize:Mn,resolve:Tr},Aa="ab".substr(-1)==="b"?function(e,n,t){return e.substr(n,t)}:function(e,n,t){return n<0&&(n=e.length+n),e.substr(n,t)}}}),vD=ee({"node-modules-polyfills-commonjs:path"(e,n){re();var t=(hD(),ft(da));if(t&&t.default){n.exports=t.default;for(let s in t)n.exports[s]=t[s]}else t&&(n.exports=t)}}),zt=ee({"src/common/errors.js"(e,n){"use strict";re();var t=class extends Error{},s=class extends Error{},a=class extends Error{},r=class extends Error{};n.exports={ConfigError:t,DebugError:s,UndefinedParserError:a,ArgExpansionBailout:r}}}),yt={};Ut(yt,{__assign:()=>br,__asyncDelegator:()=>PD,__asyncGenerator:()=>_D,__asyncValues:()=>kD,__await:()=>Gt,__awaiter:()=>xD,__classPrivateFieldGet:()=>OD,__classPrivateFieldSet:()=>qD,__createBinding:()=>TD,__decorate:()=>FD,__exportStar:()=>BD,__extends:()=>CD,__generator:()=>bD,__importDefault:()=>jD,__importStar:()=>LD,__makeTemplateObject:()=>ID,__metadata:()=>SD,__param:()=>AD,__read:()=>Sa,__rest:()=>ED,__spread:()=>ND,__spreadArrays:()=>wD,__values:()=>Bn});function CD(e,n){xr(e,n);function t(){this.constructor=e}e.prototype=n===null?Object.create(n):(t.prototype=n.prototype,new t)}function ED(e,n){var t={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,s=Object.getOwnPropertySymbols(e);a<s.length;a++)n.indexOf(s[a])<0&&Object.prototype.propertyIsEnumerable.call(e,s[a])&&(t[s[a]]=e[s[a]]);return t}function FD(e,n,t,s){var a=arguments.length,r=a<3?n:s===null?s=Object.getOwnPropertyDescriptor(n,t):s,u;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,n,t,s);else for(var i=e.length-1;i>=0;i--)(u=e[i])&&(r=(a<3?u(r):a>3?u(n,t,r):u(n,t))||r);return a>3&&r&&Object.defineProperty(n,t,r),r}function AD(e,n){return function(t,s){n(t,s,e)}}function SD(e,n){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,n)}function xD(e,n,t,s){function a(r){return r instanceof t?r:new t(function(u){u(r)})}return new(t||(t=Promise))(function(r,u){function i(y){try{c(s.next(y))}catch(h){u(h)}}function o(y){try{c(s.throw(y))}catch(h){u(h)}}function c(y){y.done?r(y.value):a(y.value).then(i,o)}c((s=s.apply(e,n||[])).next())})}function bD(e,n){var t={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},s,a,r,u;return u={next:i(0),throw:i(1),return:i(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function i(c){return function(y){return o([c,y])}}function o(c){if(s)throw new TypeError("Generator is already executing.");for(;t;)try{if(s=1,a&&(r=c[0]&2?a.return:c[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,c[1])).done)return r;switch(a=0,r&&(c=[c[0]&2,r.value]),c[0]){case 0:case 1:r=c;break;case 4:return t.label++,{value:c[1],done:!1};case 5:t.label++,a=c[1],c=[0];continue;case 7:c=t.ops.pop(),t.trys.pop();continue;default:if(r=t.trys,!(r=r.length>0&&r[r.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!r||c[1]>r[0]&&c[1]<r[3])){t.label=c[1];break}if(c[0]===6&&t.label<r[1]){t.label=r[1],r=c;break}if(r&&t.label<r[2]){t.label=r[2],t.ops.push(c);break}r[2]&&t.ops.pop(),t.trys.pop();continue}c=n.call(e,t)}catch(y){c=[6,y],a=0}finally{s=r=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function TD(e,n,t,s){s===void 0&&(s=t),e[s]=n[t]}function BD(e,n){for(var t in e)t!=="default"&&!n.hasOwnProperty(t)&&(n[t]=e[t])}function Bn(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],s=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&s>=e.length&&(e=void 0),{value:e&&e[s++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function Sa(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var s=t.call(e),a,r=[],u;try{for(;(n===void 0||n-- >0)&&!(a=s.next()).done;)r.push(a.value)}catch(i){u={error:i}}finally{try{a&&!a.done&&(t=s.return)&&t.call(s)}finally{if(u)throw u.error}}return r}function ND(){for(var e=[],n=0;n<arguments.length;n++)e=e.concat(Sa(arguments[n]));return e}function wD(){for(var e=0,n=0,t=arguments.length;n<t;n++)e+=arguments[n].length;for(var s=Array(e),a=0,n=0;n<t;n++)for(var r=arguments[n],u=0,i=r.length;u<i;u++,a++)s[a]=r[u];return s}function Gt(e){return this instanceof Gt?(this.v=e,this):new Gt(e)}function _D(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s=t.apply(e,n||[]),a,r=[];return a={},u("next"),u("throw"),u("return"),a[Symbol.asyncIterator]=function(){return this},a;function u(g){s[g]&&(a[g]=function(p){return new Promise(function(D,v){r.push([g,p,D,v])>1||i(g,p)})})}function i(g,p){try{o(s[g](p))}catch(D){h(r[0][3],D)}}function o(g){g.value instanceof Gt?Promise.resolve(g.value.v).then(c,y):h(r[0][2],g)}function c(g){i("next",g)}function y(g){i("throw",g)}function h(g,p){g(p),r.shift(),r.length&&i(r[0][0],r[0][1])}}function PD(e){var n,t;return n={},s("next"),s("throw",function(a){throw a}),s("return"),n[Symbol.iterator]=function(){return this},n;function s(a,r){n[a]=e[a]?function(u){return(t=!t)?{value:Gt(e[a](u)),done:a==="return"}:r?r(u):u}:r}}function kD(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Bn=="function"?Bn(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(r){t[r]=e[r]&&function(u){return new Promise(function(i,o){u=e[r](u),a(i,o,u.done,u.value)})}}function a(r,u,i,o){Promise.resolve(o).then(function(c){r({value:c,done:i})},u)}}function ID(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e}function LD(e){if(e&&e.__esModule)return e;var n={};if(e!=null)for(var t in e)Object.hasOwnProperty.call(e,t)&&(n[t]=e[t]);return n.default=e,n}function jD(e){return e&&e.__esModule?e:{default:e}}function OD(e,n){if(!n.has(e))throw new TypeError("attempted to get private field on non-instance");return n.get(e)}function qD(e,n,t){if(!n.has(e))throw new TypeError("attempted to set private field on non-instance");return n.set(e,t),t}var xr,br,Ct=gt({"node_modules/tslib/tslib.es6.js"(){re(),xr=function(e,n){return xr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,s){t.__proto__=s}||function(t,s){for(var a in s)s.hasOwnProperty(a)&&(t[a]=s[a])},xr(e,n)},br=function(){return br=Object.assign||function(n){for(var t,s=1,a=arguments.length;s<a;s++){t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},br.apply(this,arguments)}}}),xa=ee({"node_modules/vnopts/lib/descriptors/api.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.apiDescriptor={key:n=>/^[$_a-zA-Z][$_a-zA-Z0-9]*$/.test(n)?n:JSON.stringify(n),value(n){if(n===null||typeof n!="object")return JSON.stringify(n);if(Array.isArray(n))return`[${n.map(s=>e.apiDescriptor.value(s)).join(", ")}]`;let t=Object.keys(n);return t.length===0?"{}":`{ ${t.map(s=>`${e.apiDescriptor.key(s)}: ${e.apiDescriptor.value(n[s])}`).join(", ")} }`},pair:n=>{let{key:t,value:s}=n;return e.apiDescriptor.value({[t]:s})}}}}),MD=ee({"node_modules/vnopts/lib/descriptors/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(xa(),e)}}),_r=ee({"scripts/build/shims/chalk.cjs"(e,n){"use strict";re();var t=s=>s;t.grey=t,t.red=t,t.bold=t,t.yellow=t,t.blue=t,t.default=t,n.exports=t}}),ba=ee({"node_modules/vnopts/lib/handlers/deprecated/common.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=_r();e.commonDeprecatedHandler=(t,s,a)=>{let{descriptor:r}=a,u=[`${n.default.yellow(typeof t=="string"?r.key(t):r.pair(t))} is deprecated`];return s&&u.push(`we now treat it as ${n.default.blue(typeof s=="string"?r.key(s):r.pair(s))}`),u.join("; ")+"."}}}),RD=ee({"node_modules/vnopts/lib/handlers/deprecated/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(ba(),e)}}),$D=ee({"node_modules/vnopts/lib/handlers/invalid/common.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=_r();e.commonInvalidHandler=(t,s,a)=>[`Invalid ${n.default.red(a.descriptor.key(t))} value.`,`Expected ${n.default.blue(a.schemas[t].expected(a))},`,`but received ${n.default.red(a.descriptor.value(s))}.`].join(" ")}}),Ta=ee({"node_modules/vnopts/lib/handlers/invalid/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar($D(),e)}}),VD=ee({"node_modules/vnopts/node_modules/leven/index.js"(e,n){"use strict";re();var t=[],s=[];n.exports=function(a,r){if(a===r)return 0;var u=a;a.length>r.length&&(a=r,r=u);var i=a.length,o=r.length;if(i===0)return o;if(o===0)return i;for(;i>0&&a.charCodeAt(~-i)===r.charCodeAt(~-o);)i--,o--;if(i===0)return o;for(var c=0;c<i&&a.charCodeAt(c)===r.charCodeAt(c);)c++;if(i-=c,o-=c,i===0)return o;for(var y,h,g,p,D=0,v=0;D<i;)s[c+D]=a.charCodeAt(c+D),t[D]=++D;for(;v<o;)for(y=r.charCodeAt(c+v),g=v++,h=v,D=0;D<i;D++)p=y===s[c+D]?g:g+1,g=t[D],h=t[D]=g>h?p>h?h+1:p:p>g?g+1:p;return h}}}),Ba=ee({"node_modules/vnopts/lib/handlers/unknown/leven.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=_r(),t=VD();e.levenUnknownHandler=(s,a,r)=>{let{descriptor:u,logger:i,schemas:o}=r,c=[`Ignored unknown option ${n.default.yellow(u.pair({key:s,value:a}))}.`],y=Object.keys(o).sort().find(h=>t(s,h)<3);y&&c.push(`Did you mean ${n.default.blue(u.key(y))}?`),i.warn(c.join(" "))}}}),WD=ee({"node_modules/vnopts/lib/handlers/unknown/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(Ba(),e)}}),HD=ee({"node_modules/vnopts/lib/handlers/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(RD(),e),n.__exportStar(Ta(),e),n.__exportStar(WD(),e)}}),Et=ee({"node_modules/vnopts/lib/schema.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=["default","expected","validate","deprecated","forward","redirect","overlap","preprocess","postprocess"];function t(r,u){let i=new r(u),o=Object.create(i);for(let c of n)c in u&&(o[c]=a(u[c],i,s.prototype[c].length));return o}e.createSchema=t;var s=class{constructor(r){this.name=r.name}static create(r){return t(this,r)}default(r){}expected(r){return"nothing"}validate(r,u){return!1}deprecated(r,u){return!1}forward(r,u){}redirect(r,u){}overlap(r,u,i){return r}preprocess(r,u){return r}postprocess(r,u){return r}};e.Schema=s;function a(r,u,i){return typeof r=="function"?function(){for(var o=arguments.length,c=new Array(o),y=0;y<o;y++)c[y]=arguments[y];return r(...c.slice(0,i-1),u,...c.slice(i-1))}:()=>r}}}),GD=ee({"node_modules/vnopts/lib/schemas/alias.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=class extends n.Schema{constructor(s){super(s),this._sourceName=s.sourceName}expected(s){return s.schemas[this._sourceName].expected(s)}validate(s,a){return a.schemas[this._sourceName].validate(s,a)}redirect(s,a){return this._sourceName}};e.AliasSchema=t}}),UD=ee({"node_modules/vnopts/lib/schemas/any.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=class extends n.Schema{expected(){return"anything"}validate(){return!0}};e.AnySchema=t}}),JD=ee({"node_modules/vnopts/lib/schemas/array.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt)),t=Et(),s=class extends t.Schema{constructor(r){var{valueSchema:u,name:i=u.name}=r,o=n.__rest(r,["valueSchema","name"]);super(Object.assign({},o,{name:i})),this._valueSchema=u}expected(r){return`an array of ${this._valueSchema.expected(r)}`}validate(r,u){if(!Array.isArray(r))return!1;let i=[];for(let o of r){let c=u.normalizeValidateResult(this._valueSchema.validate(o,u),o);c!==!0&&i.push(c.value)}return i.length===0?!0:{value:i}}deprecated(r,u){let i=[];for(let o of r){let c=u.normalizeDeprecatedResult(this._valueSchema.deprecated(o,u),o);c!==!1&&i.push(...c.map(y=>{let{value:h}=y;return{value:[h]}}))}return i}forward(r,u){let i=[];for(let o of r){let c=u.normalizeForwardResult(this._valueSchema.forward(o,u),o);i.push(...c.map(a))}return i}redirect(r,u){let i=[],o=[];for(let c of r){let y=u.normalizeRedirectResult(this._valueSchema.redirect(c,u),c);"remain"in y&&i.push(y.remain),o.push(...y.redirect.map(a))}return i.length===0?{redirect:o}:{redirect:o,remain:i}}overlap(r,u){return r.concat(u)}};e.ArraySchema=s;function a(r){let{from:u,to:i}=r;return{from:[u],to:i}}}}),zD=ee({"node_modules/vnopts/lib/schemas/boolean.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=class extends n.Schema{expected(){return"true or false"}validate(s){return typeof s=="boolean"}};e.BooleanSchema=t}}),Vn=ee({"node_modules/vnopts/lib/utils.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});function n(p,D){let v=Object.create(null);for(let w of p){let T=w[D];if(v[T])throw new Error(`Duplicate ${D} ${JSON.stringify(T)}`);v[T]=w}return v}e.recordFromArray=n;function t(p,D){let v=new Map;for(let w of p){let T=w[D];if(v.has(T))throw new Error(`Duplicate ${D} ${JSON.stringify(T)}`);v.set(T,w)}return v}e.mapFromArray=t;function s(){let p=Object.create(null);return D=>{let v=JSON.stringify(D);return p[v]?!0:(p[v]=!0,!1)}}e.createAutoChecklist=s;function a(p,D){let v=[],w=[];for(let T of p)D(T)?v.push(T):w.push(T);return[v,w]}e.partition=a;function r(p){return p===Math.floor(p)}e.isInt=r;function u(p,D){if(p===D)return 0;let v=typeof p,w=typeof D,T=["undefined","object","boolean","number","string"];return v!==w?T.indexOf(v)-T.indexOf(w):v!=="string"?Number(p)-Number(D):p.localeCompare(D)}e.comparePrimitive=u;function i(p){return p===void 0?{}:p}e.normalizeDefaultResult=i;function o(p,D){return p===!0?!0:p===!1?{value:D}:p}e.normalizeValidateResult=o;function c(p,D){let v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return p===!1?!1:p===!0?v?!0:[{value:D}]:"value"in p?[p]:p.length===0?!1:p}e.normalizeDeprecatedResult=c;function y(p,D){return typeof p=="string"||"key"in p?{from:D,to:p}:"from"in p?{from:p.from,to:p.to}:{from:D,to:p.to}}e.normalizeTransferResult=y;function h(p,D){return p===void 0?[]:Array.isArray(p)?p.map(v=>y(v,D)):[y(p,D)]}e.normalizeForwardResult=h;function g(p,D){let v=h(typeof p=="object"&&"redirect"in p?p.redirect:p,D);return v.length===0?{remain:D,redirect:v}:typeof p=="object"&&"remain"in p?{remain:p.remain,redirect:v}:{redirect:v}}e.normalizeRedirectResult=g}}),XD=ee({"node_modules/vnopts/lib/schemas/choice.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=Vn(),s=class extends n.Schema{constructor(a){super(a),this._choices=t.mapFromArray(a.choices.map(r=>r&&typeof r=="object"?r:{value:r}),"value")}expected(a){let{descriptor:r}=a,u=Array.from(this._choices.keys()).map(c=>this._choices.get(c)).filter(c=>!c.deprecated).map(c=>c.value).sort(t.comparePrimitive).map(r.value),i=u.slice(0,-2),o=u.slice(-2);return i.concat(o.join(" or ")).join(", ")}validate(a){return this._choices.has(a)}deprecated(a){let r=this._choices.get(a);return r&&r.deprecated?{value:a}:!1}forward(a){let r=this._choices.get(a);return r?r.forward:void 0}redirect(a){let r=this._choices.get(a);return r?r.redirect:void 0}};e.ChoiceSchema=s}}),Na=ee({"node_modules/vnopts/lib/schemas/number.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=class extends n.Schema{expected(){return"a number"}validate(s,a){return typeof s=="number"}};e.NumberSchema=t}}),KD=ee({"node_modules/vnopts/lib/schemas/integer.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Vn(),t=Na(),s=class extends t.NumberSchema{expected(){return"an integer"}validate(a,r){return r.normalizeValidateResult(super.validate(a,r),a)===!0&&n.isInt(a)}};e.IntegerSchema=s}}),YD=ee({"node_modules/vnopts/lib/schemas/string.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Et(),t=class extends n.Schema{expected(){return"a string"}validate(s){return typeof s=="string"}};e.StringSchema=t}}),QD=ee({"node_modules/vnopts/lib/schemas/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(GD(),e),n.__exportStar(UD(),e),n.__exportStar(JD(),e),n.__exportStar(zD(),e),n.__exportStar(XD(),e),n.__exportStar(KD(),e),n.__exportStar(Na(),e),n.__exportStar(YD(),e)}}),ZD=ee({"node_modules/vnopts/lib/defaults.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=xa(),t=ba(),s=Ta(),a=Ba();e.defaultDescriptor=n.apiDescriptor,e.defaultUnknownHandler=a.levenUnknownHandler,e.defaultInvalidHandler=s.commonInvalidHandler,e.defaultDeprecatedHandler=t.commonDeprecatedHandler}}),em=ee({"node_modules/vnopts/lib/normalize.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=ZD(),t=Vn();e.normalize=(a,r,u)=>new s(r,u).normalize(a);var s=class{constructor(a,r){let{logger:u=console,descriptor:i=n.defaultDescriptor,unknown:o=n.defaultUnknownHandler,invalid:c=n.defaultInvalidHandler,deprecated:y=n.defaultDeprecatedHandler}=r||{};this._utils={descriptor:i,logger:u||{warn:()=>{}},schemas:t.recordFromArray(a,"name"),normalizeDefaultResult:t.normalizeDefaultResult,normalizeDeprecatedResult:t.normalizeDeprecatedResult,normalizeForwardResult:t.normalizeForwardResult,normalizeRedirectResult:t.normalizeRedirectResult,normalizeValidateResult:t.normalizeValidateResult},this._unknownHandler=o,this._invalidHandler=c,this._deprecatedHandler=y,this.cleanHistory()}cleanHistory(){this._hasDeprecationWarned=t.createAutoChecklist()}normalize(a){let r={},u=[a],i=()=>{for(;u.length!==0;){let o=u.shift(),c=this._applyNormalization(o,r);u.push(...c)}};i();for(let o of Object.keys(this._utils.schemas)){let c=this._utils.schemas[o];if(!(o in r)){let y=t.normalizeDefaultResult(c.default(this._utils));"value"in y&&u.push({[o]:y.value})}}i();for(let o of Object.keys(this._utils.schemas)){let c=this._utils.schemas[o];o in r&&(r[o]=c.postprocess(r[o],this._utils))}return r}_applyNormalization(a,r){let u=[],[i,o]=t.partition(Object.keys(a),c=>c in this._utils.schemas);for(let c of i){let y=this._utils.schemas[c],h=y.preprocess(a[c],this._utils),g=t.normalizeValidateResult(y.validate(h,this._utils),h);if(g!==!0){let{value:T}=g,F=this._invalidHandler(c,T,this._utils);throw typeof F=="string"?new Error(F):F}let p=T=>{let{from:F,to:A}=T;u.push(typeof A=="string"?{[A]:F}:{[A.key]:A.value})},D=T=>{let{value:F,redirectTo:A}=T,B=t.normalizeDeprecatedResult(y.deprecated(F,this._utils),h,!0);if(B!==!1)if(B===!0)this._hasDeprecationWarned(c)||this._utils.logger.warn(this._deprecatedHandler(c,A,this._utils));else for(let{value:k}of B){let P={key:c,value:k};if(!this._hasDeprecationWarned(P)){let R=typeof A=="string"?{key:A,value:k}:A;this._utils.logger.warn(this._deprecatedHandler(P,R,this._utils))}}};t.normalizeForwardResult(y.forward(h,this._utils),h).forEach(p);let w=t.normalizeRedirectResult(y.redirect(h,this._utils),h);if(w.redirect.forEach(p),"remain"in w){let T=w.remain;r[c]=c in r?y.overlap(r[c],T,this._utils):T,D({value:T})}for(let{from:T,to:F}of w.redirect)D({value:T,redirectTo:F})}for(let c of o){let y=a[c],h=this._unknownHandler(c,y,this._utils);if(h)for(let g of Object.keys(h)){let p={[g]:h[g]};g in this._utils.schemas?u.push(p):Object.assign(r,p)}}return u}};e.Normalizer=s}}),tm=ee({"node_modules/vnopts/lib/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=(Ct(),ft(yt));n.__exportStar(MD(),e),n.__exportStar(HD(),e),n.__exportStar(QD(),e),n.__exportStar(em(),e),n.__exportStar(Et(),e)}}),rm=ee({"src/main/options-normalizer.js"(e,n){"use strict";re();var t=tm(),s=lt(),a={key:g=>g.length===1?`-${g}`:`--${g}`,value:g=>t.apiDescriptor.value(g),pair:g=>{let{key:p,value:D}=g;return D===!1?`--no-${p}`:D===!0?a.key(p):D===""?`${a.key(p)} without an argument`:`${a.key(p)}=${D}`}},r=g=>{let{colorsModule:p,levenshteinDistance:D}=g;return class extends t.ChoiceSchema{constructor(w){let{name:T,flags:F}=w;super({name:T,choices:F}),this._flags=[...F].sort()}preprocess(w,T){if(typeof w=="string"&&w.length>0&&!this._flags.includes(w)){let F=this._flags.find(A=>D(A,w)<3);if(F)return T.logger.warn([`Unknown flag ${p.yellow(T.descriptor.value(w))},`,`did you mean ${p.blue(T.descriptor.value(F))}?`].join(" ")),F}return w}expected(){return"a flag"}}},u;function i(g,p){let{logger:D=!1,isCLI:v=!1,passThrough:w=!1,colorsModule:T=null,levenshteinDistance:F=null}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},A=w?Array.isArray(w)?(x,m)=>w.includes(x)?{[x]:m}:void 0:(x,m)=>({[x]:m}):(x,m,E)=>{let l=E.schemas,{_:d}=l,C=Pn(l,Mf);return t.levenUnknownHandler(x,m,Object.assign(Object.assign({},E),{},{schemas:C}))},B=v?a:t.apiDescriptor,k=o(p,{isCLI:v,colorsModule:T,levenshteinDistance:F}),P=new t.Normalizer(k,{logger:D,unknown:A,descriptor:B}),R=D!==!1;R&&u&&(P._hasDeprecationWarned=u);let f=P.normalize(g);return R&&(u=P._hasDeprecationWarned),v&&f["plugin-search"]===!1&&(f["plugin-search-dir"]=!1),f}function o(g,p){let{isCLI:D,colorsModule:v,levenshteinDistance:w}=p,T=[];D&&T.push(t.AnySchema.create({name:"_"}));for(let F of g)T.push(c(F,{isCLI:D,optionInfos:g,colorsModule:v,levenshteinDistance:w})),F.alias&&D&&T.push(t.AliasSchema.create({name:F.alias,sourceName:F.name}));return T}function c(g,p){let{isCLI:D,optionInfos:v,colorsModule:w,levenshteinDistance:T}=p,{name:F}=g;if(F==="plugin-search-dir"||F==="pluginSearchDirs")return t.AnySchema.create({name:F,preprocess(P){return P===!1||(P=Array.isArray(P)?P:[P]),P},validate(P){return P===!1?!0:P.every(R=>typeof R=="string")},expected(){return"false or paths to plugin search dir"}});let A={name:F},B,k={};switch(g.type){case"int":B=t.IntegerSchema,D&&(A.preprocess=Number);break;case"string":B=t.StringSchema;break;case"choice":B=t.ChoiceSchema,A.choices=g.choices.map(P=>typeof P=="object"&&P.redirect?Object.assign(Object.assign({},P),{},{redirect:{to:{key:g.name,value:P.redirect}}}):P);break;case"boolean":B=t.BooleanSchema;break;case"flag":B=r({colorsModule:w,levenshteinDistance:T}),A.flags=v.flatMap(P=>[P.alias,P.description&&P.name,P.oppositeDescription&&`no-${P.name}`].filter(Boolean));break;case"path":B=t.StringSchema;break;default:throw new Error(`Unexpected type ${g.type}`)}if(g.exception?A.validate=(P,R,f)=>g.exception(P)||R.validate(P,f):A.validate=(P,R,f)=>P===void 0||R.validate(P,f),g.redirect&&(k.redirect=P=>P?{to:{key:g.redirect.option,value:g.redirect.value}}:void 0),g.deprecated&&(k.deprecated=!0),D&&!g.array){let P=A.preprocess||(R=>R);A.preprocess=(R,f,x)=>f.preprocess(P(Array.isArray(R)?s(R):R),x)}return g.array?t.ArraySchema.create(Object.assign(Object.assign(Object.assign({},D?{preprocess:P=>Array.isArray(P)?P:[P]}:{}),k),{},{valueSchema:B.create(A)})):B.create(Object.assign(Object.assign({},A),k))}function y(g,p,D){return i(g,p,D)}function h(g,p,D){return i(g,p,Object.assign({isCLI:!0},D))}n.exports={normalizeApiOptions:y,normalizeCliOptions:h}}}),it=ee({"src/language-js/loc.js"(e,n){"use strict";re();var t=qn();function s(o){var c,y;let h=o.range?o.range[0]:o.start,g=(c=(y=o.declaration)===null||y===void 0?void 0:y.decorators)!==null&&c!==void 0?c:o.decorators;return t(g)?Math.min(s(g[0]),h):h}function a(o){return o.range?o.range[1]:o.end}function r(o,c){let y=s(o);return Number.isInteger(y)&&y===s(c)}function u(o,c){let y=a(o);return Number.isInteger(y)&&y===a(c)}function i(o,c){return r(o,c)&&u(o,c)}n.exports={locStart:s,locEnd:a,hasSameLocStart:r,hasSameLoc:i}}}),nm=ee({"src/main/load-parser.js"(e,n){re(),n.exports=()=>{}}}),um=ee({"scripts/build/shims/babel-highlight.cjs"(e,n){"use strict";re();var t=_r(),s={shouldHighlight:()=>!1,getChalk:()=>t};n.exports=s}}),sm=ee({"node_modules/@babel/code-frame/lib/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.codeFrameColumns=u,e.default=i;var n=um(),t=!1;function s(o){return{gutter:o.grey,marker:o.red.bold,message:o.red.bold}}var a=/\r\n|[\n\r\u2028\u2029]/;function r(o,c,y){let h=Object.assign({column:0,line:-1},o.start),g=Object.assign({},h,o.end),{linesAbove:p=2,linesBelow:D=3}=y||{},v=h.line,w=h.column,T=g.line,F=g.column,A=Math.max(v-(p+1),0),B=Math.min(c.length,T+D);v===-1&&(A=0),T===-1&&(B=c.length);let k=T-v,P={};if(k)for(let R=0;R<=k;R++){let f=R+v;if(!w)P[f]=!0;else if(R===0){let x=c[f-1].length;P[f]=[w,x-w+1]}else if(R===k)P[f]=[0,F];else{let x=c[f-R].length;P[f]=[0,x]}}else w===F?w?P[v]=[w,0]:P[v]=!0:P[v]=[w,F-w];return{start:A,end:B,markerLines:P}}function u(o,c){let y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},h=(y.highlightCode||y.forceColor)&&(0,n.shouldHighlight)(y),g=(0,n.getChalk)(y),p=s(g),D=(R,f)=>h?R(f):f,v=o.split(a),{start:w,end:T,markerLines:F}=r(c,v,y),A=c.start&&typeof c.start.column=="number",B=String(T).length,P=(h?(0,n.default)(o,y):o).split(a,T).slice(w,T).map((R,f)=>{let x=w+1+f,E=` ${` ${x}`.slice(-B)} |`,l=F[x],d=!F[x+1];if(l){let C="";if(Array.isArray(l)){let _=R.slice(0,Math.max(l[0]-1,0)).replace(/[^\t]/g," "),b=l[1]||1;C=[`
 `,D(p.gutter,E.replace(/\d/g," "))," ",_,D(p.marker,"^").repeat(b)].join(""),d&&y.message&&(C+=" "+D(p.message,y.message))}return[D(p.marker,">"),D(p.gutter,E),R.length>0?` ${R}`:"",C].join("")}else return` ${D(p.gutter,E)}${R.length>0?` ${R}`:""}`}).join(`
`);return y.message&&!A&&(P=`${" ".repeat(B+1)}${y.message}
${P}`),h?g.reset(P):P}function i(o,c,y){let h=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};if(!t){t=!0;let p="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";if(Bt.emitWarning)Bt.emitWarning(p,"DeprecationWarning");else{let D=new Error(p);D.name="DeprecationWarning",console.warn(new Error(p))}}return y=Math.max(y,0),u(o,{start:{column:y,line:c}},h)}}}),Wn=ee({"src/main/parser.js"(e,n){"use strict";re();var{ConfigError:t}=zt(),s=it(),a=nm(),{locStart:r,locEnd:u}=s,i=Object.getOwnPropertyNames,o=Object.getOwnPropertyDescriptor;function c(g){let p={};for(let D of g.plugins)if(!!D.parsers)for(let v of i(D.parsers))Object.defineProperty(p,v,o(D.parsers,v));return p}function y(g){let p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c(g);if(typeof g.parser=="function")return{parse:g.parser,astFormat:"estree",locStart:r,locEnd:u};if(typeof g.parser=="string"){if(Object.prototype.hasOwnProperty.call(p,g.parser))return p[g.parser];throw new t(`Couldn't resolve parser "${g.parser}". Parsers must be explicitly added to the standalone bundle.`)}}function h(g,p){let D=c(p),v=Object.defineProperties({},Object.fromEntries(Object.keys(D).map(T=>[T,{enumerable:!0,get(){return D[T].parse}}]))),w=y(p,D);try{return w.preprocess&&(g=w.preprocess(g,p)),{text:g,ast:w.parse(g,v,p)}}catch(T){let{loc:F}=T;if(F){let{codeFrameColumns:A}=sm();throw T.codeFrame=A(g,F,{highlightCode:!0}),T.message+=`
`+T.codeFrame,T}throw T}}n.exports={parse:h,resolveParser:y}}}),wa=ee({"src/main/options.js"(e,n){"use strict";re();var t=vD(),{UndefinedParserError:s}=zt(),{getSupportInfo:a}=On(),r=rm(),{resolveParser:u}=Wn(),i={astFormat:"estree",printer:{},originalText:void 0,locStart:null,locEnd:null};function o(h){let g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},p=Object.assign({},h),D=a({plugins:h.plugins,showUnreleased:!0,showDeprecated:!0}).options,v=Object.assign(Object.assign({},i),Object.fromEntries(D.filter(B=>B.default!==void 0).map(B=>[B.name,B.default])));if(!p.parser){if(!p.filepath)(g.logger||console).warn("No parser and no filepath given, using 'babel' the parser now but this will throw an error in the future. Please specify a parser or a filepath so one can be inferred."),p.parser="babel";else if(p.parser=y(p.filepath,p.plugins),!p.parser)throw new s(`No parser could be inferred for file: ${p.filepath}`)}let w=u(r.normalizeApiOptions(p,[D.find(B=>B.name==="parser")],{passThrough:!0,logger:!1}));p.astFormat=w.astFormat,p.locEnd=w.locEnd,p.locStart=w.locStart;let T=c(p);p.printer=T.printers[p.astFormat];let F=Object.fromEntries(D.filter(B=>B.pluginDefaults&&B.pluginDefaults[T.name]!==void 0).map(B=>[B.name,B.pluginDefaults[T.name]])),A=Object.assign(Object.assign({},v),F);for(let[B,k]of Object.entries(A))(p[B]===null||p[B]===void 0)&&(p[B]=k);return p.parser==="json"&&(p.trailingComma="none"),r.normalizeApiOptions(p,D,Object.assign({passThrough:Object.keys(i)},g))}function c(h){let{astFormat:g}=h;if(!g)throw new Error("getPlugin() requires astFormat to be set");let p=h.plugins.find(D=>D.printers&&D.printers[g]);if(!p)throw new Error(`Couldn't find plugin for AST format "${g}"`);return p}function y(h,g){let p=t.basename(h).toLowerCase(),v=a({plugins:g}).languages.filter(w=>w.since!==null).find(w=>w.extensions&&w.extensions.some(T=>p.endsWith(T))||w.filenames&&w.filenames.some(T=>T.toLowerCase()===p));return v&&v.parsers[0]}n.exports={normalize:o,hiddenDefaults:i,inferParser:y}}}),im=ee({"src/main/massage-ast.js"(e,n){"use strict";re();function t(s,a,r){if(Array.isArray(s))return s.map(c=>t(c,a,r)).filter(Boolean);if(!s||typeof s!="object")return s;let u=a.printer.massageAstNode,i;u&&u.ignoredProperties?i=u.ignoredProperties:i=new Set;let o={};for(let[c,y]of Object.entries(s))!i.has(c)&&typeof y!="function"&&(o[c]=t(y,a,s));if(u){let c=u(s,o,r);if(c===null)return;if(c)return c}return o}n.exports=t}}),Xt=ee({"scripts/build/shims/assert.cjs"(e,n){"use strict";re();var t=()=>{};t.ok=t,t.strictEqual=t,n.exports=t}}),et=ee({"src/main/comments.js"(e,n){"use strict";re();var t=Xt(),{builders:{line:s,hardline:a,breakParent:r,indent:u,lineSuffix:i,join:o,cursor:c}}=qe(),{hasNewline:y,skipNewline:h,skipSpaces:g,isPreviousLineEmpty:p,addLeadingComment:D,addDanglingComment:v,addTrailingComment:w}=Ge(),T=new WeakMap;function F(I,$,M){if(!I)return;let{printer:q,locStart:J,locEnd:L}=$;if(M){if(q.canAttachComment&&q.canAttachComment(I)){let V;for(V=M.length-1;V>=0&&!(J(M[V])<=J(I)&&L(M[V])<=L(I));--V);M.splice(V+1,0,I);return}}else if(T.has(I))return T.get(I);let Y=q.getCommentChildNodes&&q.getCommentChildNodes(I,$)||typeof I=="object"&&Object.entries(I).filter(V=>{let[O]=V;return O!=="enclosingNode"&&O!=="precedingNode"&&O!=="followingNode"&&O!=="tokens"&&O!=="comments"&&O!=="parent"}).map(V=>{let[,O]=V;return O});if(!!Y){M||(M=[],T.set(I,M));for(let V of Y)F(V,$,M);return M}}function A(I,$,M,q){let{locStart:J,locEnd:L}=M,Y=J($),V=L($),O=F(I,M),K,se,Q=0,le=O.length;for(;Q<le;){let W=Q+le>>1,X=O[W],oe=J(X),ae=L(X);if(oe<=Y&&V<=ae)return A(X,$,M,X);if(ae<=Y){K=X,Q=W+1;continue}if(V<=oe){se=X,le=W;continue}throw new Error("Comment location overlaps with node location")}if(q&&q.type==="TemplateLiteral"){let{quasis:W}=q,X=E(W,$,M);K&&E(W,K,M)!==X&&(K=null),se&&E(W,se,M)!==X&&(se=null)}return{enclosingNode:q,precedingNode:K,followingNode:se}}var B=()=>!1;function k(I,$,M,q){if(!Array.isArray(I))return;let J=[],{locStart:L,locEnd:Y,printer:{handleComments:V={}}}=q,{avoidAstMutation:O,ownLine:K=B,endOfLine:se=B,remaining:Q=B}=V,le=I.map((W,X)=>Object.assign(Object.assign({},A($,W,q)),{},{comment:W,text:M,options:q,ast:$,isLastComment:I.length-1===X}));for(let[W,X]of le.entries()){let{comment:oe,precedingNode:ae,enclosingNode:Ae,followingNode:z,text:H,options:Z,ast:ne,isLastComment:fe}=X;if(Z.parser==="json"||Z.parser==="json5"||Z.parser==="__js_expression"||Z.parser==="__vue_expression"||Z.parser==="__vue_ts_expression"){if(L(oe)-L(ne)<=0){D(ne,oe);continue}if(Y(oe)-Y(ne)>=0){w(ne,oe);continue}}let ge;if(O?ge=[X]:(oe.enclosingNode=Ae,oe.precedingNode=ae,oe.followingNode=z,ge=[oe,H,Z,ne,fe]),R(H,Z,le,W))oe.placement="ownLine",K(...ge)||(z?D(z,oe):ae?w(ae,oe):v(Ae||ne,oe));else if(f(H,Z,le,W))oe.placement="endOfLine",se(...ge)||(ae?w(ae,oe):z?D(z,oe):v(Ae||ne,oe));else if(oe.placement="remaining",!Q(...ge))if(ae&&z){let Ce=J.length;Ce>0&&J[Ce-1].followingNode!==z&&x(J,H,Z),J.push(X)}else ae?w(ae,oe):z?D(z,oe):v(Ae||ne,oe)}if(x(J,M,q),!O)for(let W of I)delete W.precedingNode,delete W.enclosingNode,delete W.followingNode}var P=I=>!/[\S\n\u2028\u2029]/.test(I);function R(I,$,M,q){let{comment:J,precedingNode:L}=M[q],{locStart:Y,locEnd:V}=$,O=Y(J);if(L)for(let K=q-1;K>=0;K--){let{comment:se,precedingNode:Q}=M[K];if(Q!==L||!P(I.slice(V(se),O)))break;O=Y(se)}return y(I,O,{backwards:!0})}function f(I,$,M,q){let{comment:J,followingNode:L}=M[q],{locStart:Y,locEnd:V}=$,O=V(J);if(L)for(let K=q+1;K<M.length;K++){let{comment:se,followingNode:Q}=M[K];if(Q!==L||!P(I.slice(O,Y(se))))break;O=V(se)}return y(I,O)}function x(I,$,M){let q=I.length;if(q===0)return;let{precedingNode:J,followingNode:L,enclosingNode:Y}=I[0],V=M.printer.getGapRegex&&M.printer.getGapRegex(Y)||/^[\s(]*$/,O=M.locStart(L),K;for(K=q;K>0;--K){let{comment:se,precedingNode:Q,followingNode:le}=I[K-1];t.strictEqual(Q,J),t.strictEqual(le,L);let W=$.slice(M.locEnd(se),O);if(V.test(W))O=M.locStart(se);else break}for(let[se,{comment:Q}]of I.entries())se<K?w(J,Q):D(L,Q);for(let se of[J,L])se.comments&&se.comments.length>1&&se.comments.sort((Q,le)=>M.locStart(Q)-M.locStart(le));I.length=0}function m(I,$){let M=I.getValue();return M.printed=!0,$.printer.printComment(I,$)}function E(I,$,M){let q=M.locStart($)-1;for(let J=1;J<I.length;++J)if(q<M.locStart(I[J]))return J-1;return 0}function l(I,$){let M=I.getValue(),q=[m(I,$)],{printer:J,originalText:L,locStart:Y,locEnd:V}=$;if(J.isBlockComment&&J.isBlockComment(M)){let se=y(L,V(M))?y(L,Y(M),{backwards:!0})?a:s:" ";q.push(se)}else q.push(a);let K=h(L,g(L,V(M)));return K!==!1&&y(L,K)&&q.push(a),q}function d(I,$){let M=I.getValue(),q=m(I,$),{printer:J,originalText:L,locStart:Y}=$,V=J.isBlockComment&&J.isBlockComment(M);if(y(L,Y(M),{backwards:!0})){let K=p(L,M,Y);return i([a,K?a:"",q])}let O=[" ",q];return V||(O=[i(O),r]),O}function C(I,$,M,q){let J=[],L=I.getValue();return!L||!L.comments||(I.each(()=>{let Y=I.getValue();!Y.leading&&!Y.trailing&&(!q||q(Y))&&J.push(m(I,$))},"comments"),J.length===0)?"":M?o(a,J):u([a,o(a,J)])}function _(I,$,M){let q=I.getValue();if(!q)return{};let J=q.comments||[];M&&(J=J.filter(O=>!M.has(O)));let L=q===$.cursorNode;if(J.length===0){let O=L?c:"";return{leading:O,trailing:O}}let Y=[],V=[];return I.each(()=>{let O=I.getValue();if(M&&M.has(O))return;let{leading:K,trailing:se}=O;K?Y.push(l(I,$)):se&&V.push(d(I,$))},"comments"),L&&(Y.unshift(c),V.push(c)),{leading:Y,trailing:V}}function b(I,$,M,q){let{leading:J,trailing:L}=_(I,M,q);return!J&&!L?$:[J,$,L]}function N(I){if(!!I)for(let $ of I){if(!$.printed)throw new Error('Comment "'+$.value.trim()+'" was not printed. Please report this error!');delete $.printed}}n.exports={attach:k,printComments:b,printCommentsSeparately:_,printDanglingComments:C,getSortedChildNodes:F,ensureAllCommentsPrinted:N}}}),am=ee({"src/common/ast-path.js"(e,n){"use strict";re();var t=lt();function s(u,i){let o=a(u.stack,i);return o===-1?null:u.stack[o]}function a(u,i){for(let o=u.length-1;o>=0;o-=2){let c=u[o];if(c&&!Array.isArray(c)&&--i<0)return o}return-1}var r=class{constructor(u){this.stack=[u]}getName(){let{stack:u}=this,{length:i}=u;return i>1?u[i-2]:null}getValue(){return t(this.stack)}getNode(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return s(this,u)}getParentNode(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return s(this,u+1)}call(u){let{stack:i}=this,{length:o}=i,c=t(i);for(var y=arguments.length,h=new Array(y>1?y-1:0),g=1;g<y;g++)h[g-1]=arguments[g];for(let D of h)c=c[D],i.push(D,c);let p=u(this);return i.length=o,p}callParent(u){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,o=a(this.stack,i+1),c=this.stack.splice(o+1),y=u(this);return this.stack.push(...c),y}each(u){let{stack:i}=this,{length:o}=i,c=t(i);for(var y=arguments.length,h=new Array(y>1?y-1:0),g=1;g<y;g++)h[g-1]=arguments[g];for(let p of h)c=c[p],i.push(p,c);for(let p=0;p<c.length;++p)i.push(p,c[p]),u(this,p,c),i.length-=2;i.length=o}map(u){let i=[];for(var o=arguments.length,c=new Array(o>1?o-1:0),y=1;y<o;y++)c[y-1]=arguments[y];return this.each((h,g,p)=>{i[g]=u(h,g,p)},...c),i}try(u){let{stack:i}=this,o=[...i];try{return u()}finally{i.length=0,i.push(...o)}}match(){let u=this.stack.length-1,i=null,o=this.stack[u--];for(var c=arguments.length,y=new Array(c),h=0;h<c;h++)y[h]=arguments[h];for(let g of y){if(o===void 0)return!1;let p=null;if(typeof i=="number"&&(p=i,i=this.stack[u--],o=this.stack[u--]),g&&!g(o,i,p))return!1;i=this.stack[u--],o=this.stack[u--]}return!0}findAncestor(u){let i=this.stack.length-1,o=null,c=this.stack[i--];for(;c;){let y=null;if(typeof o=="number"&&(y=o,o=this.stack[i--],c=this.stack[i--]),o!==null&&u(c,o,y))return c;o=this.stack[i--],c=this.stack[i--]}}};n.exports=r}}),om=ee({"src/main/multiparser.js"(e,n){"use strict";re();var{utils:{stripTrailingHardline:t}}=qe(),{normalize:s}=wa(),a=et();function r(i,o,c,y){if(c.printer.embed&&c.embeddedLanguageFormatting==="auto")return c.printer.embed(i,o,(h,g,p)=>u(h,g,c,y,p),c)}function u(i,o,c,y){let{stripTrailingHardline:h=!1}=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{},g=s(Object.assign(Object.assign(Object.assign({},c),o),{},{parentParser:c.parser,originalText:i}),{passThrough:!0}),p=Wn().parse(i,g),{ast:D}=p;i=p.text;let v=D.comments;delete D.comments,a.attach(v,D,i,g),g[Symbol.for("comments")]=v||[],g[Symbol.for("tokens")]=D.tokens||[];let w=y(D,g);return a.ensureAllCommentsPrinted(v),h?typeof w=="string"?w.replace(/(?:\r?\n)*$/,""):t(w):w}n.exports={printSubtree:r}}}),lm=ee({"src/main/ast-to-doc.js"(e,n){"use strict";re();var t=am(),{builders:{hardline:s,addAlignmentToDoc:a},utils:{propagateBreaks:r}}=qe(),{printComments:u}=et(),i=om();function o(h,g){let p=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,{printer:D}=g;D.preprocess&&(h=D.preprocess(h,g));let v=new Map,w=new t(h),T=F();return p>0&&(T=a([s,T],p,g.tabWidth)),r(T),T;function F(B,k){return B===void 0||B===w?A(k):Array.isArray(B)?w.call(()=>A(k),...B):w.call(()=>A(k),B)}function A(B){let k=w.getValue(),P=k&&typeof k=="object"&&B===void 0;if(P&&v.has(k))return v.get(k);let R=y(w,g,F,B);return P&&v.set(k,R),R}}function c(h,g){let{originalText:p,[Symbol.for("comments")]:D,locStart:v,locEnd:w}=g,T=v(h),F=w(h),A=new Set;for(let B of D)v(B)>=T&&w(B)<=F&&(B.printed=!0,A.add(B));return{doc:p.slice(T,F),printedComments:A}}function y(h,g,p,D){let v=h.getValue(),{printer:w}=g,T,F;if(w.hasPrettierIgnore&&w.hasPrettierIgnore(h))({doc:T,printedComments:F}=c(v,g));else{if(v)try{T=i.printSubtree(h,p,g,o)}catch(A){if(globalThis.PRETTIER_DEBUG)throw A}T||(T=w.print(h,g,p,D))}return(!w.willPrintOwnComments||!w.willPrintOwnComments(h,g))&&(T=u(h,T,g,F)),T}n.exports=o}}),cm=ee({"src/main/range-util.js"(e,n){"use strict";re();var t=Xt(),s=et(),a=D=>{let{parser:v}=D;return v==="json"||v==="json5"||v==="json-stringify"};function r(D,v){let w=[D.node,...D.parentNodes],T=new Set([v.node,...v.parentNodes]);return w.find(F=>y.has(F.type)&&T.has(F))}function u(D){let v=D.length-1;for(;;){let w=D[v];if(w&&(w.type==="Program"||w.type==="File"))v--;else break}return D.slice(0,v+1)}function i(D,v,w){let{locStart:T,locEnd:F}=w,A=D.node,B=v.node;if(A===B)return{startNode:A,endNode:B};let k=T(D.node);for(let R of u(v.parentNodes))if(T(R)>=k)B=R;else break;let P=F(v.node);for(let R of u(D.parentNodes)){if(F(R)<=P)A=R;else break;if(A===B)break}return{startNode:A,endNode:B}}function o(D,v,w,T){let F=arguments.length>4&&arguments[4]!==void 0?arguments[4]:[],A=arguments.length>5?arguments[5]:void 0,{locStart:B,locEnd:k}=w,P=B(D),R=k(D);if(!(v>R||v<P||A==="rangeEnd"&&v===P||A==="rangeStart"&&v===R)){for(let f of s.getSortedChildNodes(D,w)){let x=o(f,v,w,T,[D,...F],A);if(x)return x}if(!T||T(D,F[0]))return{node:D,parentNodes:F}}}function c(D,v){return v!=="DeclareExportDeclaration"&&D!=="TypeParameterDeclaration"&&(D==="Directive"||D==="TypeAlias"||D==="TSExportAssignment"||D.startsWith("Declare")||D.startsWith("TSDeclare")||D.endsWith("Statement")||D.endsWith("Declaration"))}var y=new Set(["ObjectExpression","ArrayExpression","StringLiteral","NumericLiteral","BooleanLiteral","NullLiteral","UnaryExpression","TemplateLiteral"]),h=new Set(["OperationDefinition","FragmentDefinition","VariableDefinition","TypeExtensionDefinition","ObjectTypeDefinition","FieldDefinition","DirectiveDefinition","EnumTypeDefinition","EnumValueDefinition","InputValueDefinition","InputObjectTypeDefinition","SchemaDefinition","OperationTypeDefinition","InterfaceTypeDefinition","UnionTypeDefinition","ScalarTypeDefinition"]);function g(D,v,w){if(!v)return!1;switch(D.parser){case"flow":case"babel":case"babel-flow":case"babel-ts":case"typescript":case"acorn":case"espree":case"meriyah":case"__babel_estree":return c(v.type,w&&w.type);case"json":case"json5":case"json-stringify":return y.has(v.type);case"graphql":return h.has(v.kind);case"vue":return v.tag!=="root"}return!1}function p(D,v,w){let{rangeStart:T,rangeEnd:F,locStart:A,locEnd:B}=v;t.ok(F>T);let k=D.slice(T,F).search(/\S/),P=k===-1;if(!P)for(T+=k;F>T&&!/\S/.test(D[F-1]);--F);let R=o(w,T,v,(E,l)=>g(v,E,l),[],"rangeStart"),f=P?R:o(w,F,v,E=>g(v,E),[],"rangeEnd");if(!R||!f)return{rangeStart:0,rangeEnd:0};let x,m;if(a(v)){let E=r(R,f);x=E,m=E}else({startNode:x,endNode:m}=i(R,f,v));return{rangeStart:Math.min(A(x),A(m)),rangeEnd:Math.max(B(x),B(m))}}n.exports={calculateRange:p,findNodeAtOffset:o}}}),pm=ee({"src/main/core.js"(e,n){"use strict";re();var{diffArrays:t}=zf(),{printer:{printDocToString:s},debug:{printDocToDebug:a}}=qe(),{getAlignmentSize:r}=Ge(),{guessEndOfLine:u,convertEndOfLineToChars:i,countEndOfLineChars:o,normalizeEndOfLine:c}=Ln(),y=wa().normalize,h=im(),g=et(),p=Wn(),D=lm(),v=cm(),w="\uFEFF",T=Symbol("cursor");function F(m,E,l){let d=E.comments;return d&&(delete E.comments,g.attach(d,E,m,l)),l[Symbol.for("comments")]=d||[],l[Symbol.for("tokens")]=E.tokens||[],l.originalText=m,d}function A(m,E){let l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;if(!m||m.trim().length===0)return{formatted:"",cursorOffset:-1,comments:[]};let{ast:d,text:C}=p.parse(m,E);if(E.cursorOffset>=0){let I=v.findNodeAtOffset(d,E.cursorOffset,E);I&&I.node&&(E.cursorNode=I.node)}let _=F(C,d,E),b=D(d,E,l),N=s(b,E);if(g.ensureAllCommentsPrinted(_),l>0){let I=N.formatted.trim();N.cursorNodeStart!==void 0&&(N.cursorNodeStart-=N.formatted.indexOf(I)),N.formatted=I+i(E.endOfLine)}if(E.cursorOffset>=0){let I,$,M,q,J;if(E.cursorNode&&N.cursorNodeText?(I=E.locStart(E.cursorNode),$=C.slice(I,E.locEnd(E.cursorNode)),M=E.cursorOffset-I,q=N.cursorNodeStart,J=N.cursorNodeText):(I=0,$=C,M=E.cursorOffset,q=0,J=N.formatted),$===J)return{formatted:N.formatted,cursorOffset:q+M,comments:_};let L=[...$];L.splice(M,0,T);let Y=[...J],V=t(L,Y),O=q;for(let K of V)if(K.removed){if(K.value.includes(T))break}else O+=K.count;return{formatted:N.formatted,cursorOffset:O,comments:_}}return{formatted:N.formatted,cursorOffset:-1,comments:_}}function B(m,E){let{ast:l,text:d}=p.parse(m,E),{rangeStart:C,rangeEnd:_}=v.calculateRange(d,E,l),b=d.slice(C,_),N=Math.min(C,d.lastIndexOf(`
`,C)+1),I=d.slice(N,C).match(/^\s*/)[0],$=r(I,E.tabWidth),M=A(b,Object.assign(Object.assign({},E),{},{rangeStart:0,rangeEnd:Number.POSITIVE_INFINITY,cursorOffset:E.cursorOffset>C&&E.cursorOffset<=_?E.cursorOffset-C:-1,endOfLine:"lf"}),$),q=M.formatted.trimEnd(),{cursorOffset:J}=E;J>_?J+=q.length-b.length:M.cursorOffset>=0&&(J=M.cursorOffset+C);let L=d.slice(0,C)+q+d.slice(_);if(E.endOfLine!=="lf"){let Y=i(E.endOfLine);J>=0&&Y===`\r
`&&(J+=o(L.slice(0,J),`
`)),L=L.replace(/\n/g,Y)}return{formatted:L,cursorOffset:J,comments:M.comments}}function k(m,E,l){return typeof E!="number"||Number.isNaN(E)||E<0||E>m.length?l:E}function P(m,E){let{cursorOffset:l,rangeStart:d,rangeEnd:C}=E;return l=k(m,l,-1),d=k(m,d,0),C=k(m,C,m.length),Object.assign(Object.assign({},E),{},{cursorOffset:l,rangeStart:d,rangeEnd:C})}function R(m,E){let{cursorOffset:l,rangeStart:d,rangeEnd:C,endOfLine:_}=P(m,E),b=m.charAt(0)===w;if(b&&(m=m.slice(1),l--,d--,C--),_==="auto"&&(_=u(m)),m.includes("\r")){let N=I=>o(m.slice(0,Math.max(I,0)),`\r
`);l-=N(l),d-=N(d),C-=N(C),m=c(m)}return{hasBOM:b,text:m,options:P(m,Object.assign(Object.assign({},E),{},{cursorOffset:l,rangeStart:d,rangeEnd:C,endOfLine:_}))}}function f(m,E){let l=p.resolveParser(E);return!l.hasPragma||l.hasPragma(m)}function x(m,E){let{hasBOM:l,text:d,options:C}=R(m,y(E));if(C.rangeStart>=C.rangeEnd&&d!==""||C.requirePragma&&!f(d,C))return{formatted:m,cursorOffset:E.cursorOffset,comments:[]};let _;return C.rangeStart>0||C.rangeEnd<d.length?_=B(d,C):(!C.requirePragma&&C.insertPragma&&C.printer.insertPragma&&!f(d,C)&&(d=C.printer.insertPragma(d)),_=A(d,C)),l&&(_.formatted=w+_.formatted,_.cursorOffset>=0&&_.cursorOffset++),_}n.exports={formatWithCursor:x,parse(m,E,l){let{text:d,options:C}=R(m,y(E)),_=p.parse(d,C);return l&&(_.ast=h(_.ast,C)),_},formatAST(m,E){E=y(E);let l=D(m,E);return s(l,E)},formatDoc(m,E){return x(a(m),Object.assign(Object.assign({},E),{},{parser:"__js_expression"})).formatted},printToDoc(m,E){E=y(E);let{ast:l,text:d}=p.parse(m,E);return F(d,l,E),D(l,E)},printDocToString(m,E){return s(m,y(E))}}}}),fm=ee({"src/common/util-shared.js"(e,n){"use strict";re();var{getMaxContinuousCount:t,getStringWidth:s,getAlignmentSize:a,getIndentSize:r,skip:u,skipWhitespace:i,skipSpaces:o,skipNewline:c,skipToLineEnd:y,skipEverythingButNewLine:h,skipInlineComment:g,skipTrailingComment:p,hasNewline:D,hasNewlineInRange:v,hasSpaces:w,isNextLineEmpty:T,isNextLineEmptyAfterIndex:F,isPreviousLineEmpty:A,getNextNonSpaceNonCommentCharacterIndex:B,makeString:k,addLeadingComment:P,addDanglingComment:R,addTrailingComment:f}=Ge();n.exports={getMaxContinuousCount:t,getStringWidth:s,getAlignmentSize:a,getIndentSize:r,skip:u,skipWhitespace:i,skipSpaces:o,skipNewline:c,skipToLineEnd:y,skipEverythingButNewLine:h,skipInlineComment:g,skipTrailingComment:p,hasNewline:D,hasNewlineInRange:v,hasSpaces:w,isNextLineEmpty:T,isNextLineEmptyAfterIndex:F,isPreviousLineEmpty:A,getNextNonSpaceNonCommentCharacterIndex:B,makeString:k,addLeadingComment:P,addDanglingComment:R,addTrailingComment:f}}}),Nt=ee({"src/utils/create-language.js"(e,n){"use strict";re(),n.exports=function(t,s){let{languageId:a}=t,r=Pn(t,Rf);return Object.assign(Object.assign({linguistLanguageId:a},r),s(t))}}}),Dm=ee({"node_modules/esutils/lib/ast.js"(e,n){re(),function(){"use strict";function t(o){if(o==null)return!1;switch(o.type){case"ArrayExpression":case"AssignmentExpression":case"BinaryExpression":case"CallExpression":case"ConditionalExpression":case"FunctionExpression":case"Identifier":case"Literal":case"LogicalExpression":case"MemberExpression":case"NewExpression":case"ObjectExpression":case"SequenceExpression":case"ThisExpression":case"UnaryExpression":case"UpdateExpression":return!0}return!1}function s(o){if(o==null)return!1;switch(o.type){case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"WhileStatement":return!0}return!1}function a(o){if(o==null)return!1;switch(o.type){case"BlockStatement":case"BreakStatement":case"ContinueStatement":case"DebuggerStatement":case"DoWhileStatement":case"EmptyStatement":case"ExpressionStatement":case"ForInStatement":case"ForStatement":case"IfStatement":case"LabeledStatement":case"ReturnStatement":case"SwitchStatement":case"ThrowStatement":case"TryStatement":case"VariableDeclaration":case"WhileStatement":case"WithStatement":return!0}return!1}function r(o){return a(o)||o!=null&&o.type==="FunctionDeclaration"}function u(o){switch(o.type){case"IfStatement":return o.alternate!=null?o.alternate:o.consequent;case"LabeledStatement":case"ForStatement":case"ForInStatement":case"WhileStatement":case"WithStatement":return o.body}return null}function i(o){var c;if(o.type!=="IfStatement"||o.alternate==null)return!1;c=o.consequent;do{if(c.type==="IfStatement"&&c.alternate==null)return!0;c=u(c)}while(c);return!1}n.exports={isExpression:t,isStatement:a,isIterationStatement:s,isSourceElement:r,isProblematicIfStatement:i,trailingStatement:u}}()}}),_a=ee({"node_modules/esutils/lib/code.js"(e,n){re(),function(){"use strict";var t,s,a,r,u,i;s={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,NonAsciiIdentifierPart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/},t={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};function o(F){return 48<=F&&F<=57}function c(F){return 48<=F&&F<=57||97<=F&&F<=102||65<=F&&F<=70}function y(F){return F>=48&&F<=55}a=[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279];function h(F){return F===32||F===9||F===11||F===12||F===160||F>=5760&&a.indexOf(F)>=0}function g(F){return F===10||F===13||F===8232||F===8233}function p(F){if(F<=65535)return String.fromCharCode(F);var A=String.fromCharCode(Math.floor((F-65536)/1024)+55296),B=String.fromCharCode((F-65536)%1024+56320);return A+B}for(r=new Array(128),i=0;i<128;++i)r[i]=i>=97&&i<=122||i>=65&&i<=90||i===36||i===95;for(u=new Array(128),i=0;i<128;++i)u[i]=i>=97&&i<=122||i>=65&&i<=90||i>=48&&i<=57||i===36||i===95;function D(F){return F<128?r[F]:s.NonAsciiIdentifierStart.test(p(F))}function v(F){return F<128?u[F]:s.NonAsciiIdentifierPart.test(p(F))}function w(F){return F<128?r[F]:t.NonAsciiIdentifierStart.test(p(F))}function T(F){return F<128?u[F]:t.NonAsciiIdentifierPart.test(p(F))}n.exports={isDecimalDigit:o,isHexDigit:c,isOctalDigit:y,isWhiteSpace:h,isLineTerminator:g,isIdentifierStartES5:D,isIdentifierPartES5:v,isIdentifierStartES6:w,isIdentifierPartES6:T}}()}}),mm=ee({"node_modules/esutils/lib/keyword.js"(e,n){re(),function(){"use strict";var t=_a();function s(D){switch(D){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"let":return!0;default:return!1}}function a(D,v){return!v&&D==="yield"?!1:r(D,v)}function r(D,v){if(v&&s(D))return!0;switch(D.length){case 2:return D==="if"||D==="in"||D==="do";case 3:return D==="var"||D==="for"||D==="new"||D==="try";case 4:return D==="this"||D==="else"||D==="case"||D==="void"||D==="with"||D==="enum";case 5:return D==="while"||D==="break"||D==="catch"||D==="throw"||D==="const"||D==="yield"||D==="class"||D==="super";case 6:return D==="return"||D==="typeof"||D==="delete"||D==="switch"||D==="export"||D==="import";case 7:return D==="default"||D==="finally"||D==="extends";case 8:return D==="function"||D==="continue"||D==="debugger";case 10:return D==="instanceof";default:return!1}}function u(D,v){return D==="null"||D==="true"||D==="false"||a(D,v)}function i(D,v){return D==="null"||D==="true"||D==="false"||r(D,v)}function o(D){return D==="eval"||D==="arguments"}function c(D){var v,w,T;if(D.length===0||(T=D.charCodeAt(0),!t.isIdentifierStartES5(T)))return!1;for(v=1,w=D.length;v<w;++v)if(T=D.charCodeAt(v),!t.isIdentifierPartES5(T))return!1;return!0}function y(D,v){return(D-55296)*1024+(v-56320)+65536}function h(D){var v,w,T,F,A;if(D.length===0)return!1;for(A=t.isIdentifierStartES6,v=0,w=D.length;v<w;++v){if(T=D.charCodeAt(v),55296<=T&&T<=56319){if(++v,v>=w||(F=D.charCodeAt(v),!(56320<=F&&F<=57343)))return!1;T=y(T,F)}if(!A(T))return!1;A=t.isIdentifierPartES6}return!0}function g(D,v){return c(D)&&!u(D,v)}function p(D,v){return h(D)&&!i(D,v)}n.exports={isKeywordES5:a,isKeywordES6:r,isReservedWordES5:u,isReservedWordES6:i,isRestrictedWord:o,isIdentifierNameES5:c,isIdentifierNameES6:h,isIdentifierES5:g,isIdentifierES6:p}}()}}),dm=ee({"node_modules/esutils/lib/utils.js"(e){re(),function(){"use strict";e.ast=Dm(),e.code=_a(),e.keyword=mm()}()}}),kt=ee({"src/language-js/utils/is-block-comment.js"(e,n){"use strict";re();var t=new Set(["Block","CommentBlock","MultiLine"]),s=a=>t.has(a==null?void 0:a.type);n.exports=s}}),gm=ee({"src/language-js/utils/is-node-matches.js"(e,n){"use strict";re();function t(a,r){let u=r.split(".");for(let i=u.length-1;i>=0;i--){let o=u[i];if(i===0)return a.type==="Identifier"&&a.name===o;if(a.type!=="MemberExpression"||a.optional||a.computed||a.property.type!=="Identifier"||a.property.name!==o)return!1;a=a.object}}function s(a,r){return r.some(u=>t(a,u))}n.exports=s}}),Ke=ee({"src/language-js/utils/index.js"(e,n){"use strict";re();var t=dm().keyword.isIdentifierNameES5,{getLast:s,hasNewline:a,skipWhitespace:r,isNonEmptyArray:u,isNextLineEmptyAfterIndex:i,getStringWidth:o}=Ge(),{locStart:c,locEnd:y,hasSameLocStart:h}=it(),g=kt(),p=gm(),D="(?:(?=.)\\s)",v=new RegExp(`^${D}*:`),w=new RegExp(`^${D}*::`);function T(j){var me,ke;return((me=j.extra)===null||me===void 0?void 0:me.parenthesized)&&g((ke=j.trailingComments)===null||ke===void 0?void 0:ke[0])&&v.test(j.trailingComments[0].value)}function F(j){let me=j==null?void 0:j[0];return g(me)&&w.test(me.value)}function A(j,me){if(!j||typeof j!="object")return!1;if(Array.isArray(j))return j.some(je=>A(je,me));let ke=me(j);return typeof ke=="boolean"?ke:Object.values(j).some(je=>A(je,me))}function B(j){return j.type==="AssignmentExpression"||j.type==="BinaryExpression"||j.type==="LogicalExpression"||j.type==="NGPipeExpression"||j.type==="ConditionalExpression"||oe(j)||ae(j)||j.type==="SequenceExpression"||j.type==="TaggedTemplateExpression"||j.type==="BindExpression"||j.type==="UpdateExpression"&&!j.prefix||nt(j)||j.type==="TSNonNullExpression"}function k(j){var me,ke,je,Ye,ut,ze;return j.expressions?j.expressions[0]:(me=(ke=(je=(Ye=(ut=(ze=j.left)!==null&&ze!==void 0?ze:j.test)!==null&&ut!==void 0?ut:j.callee)!==null&&Ye!==void 0?Ye:j.object)!==null&&je!==void 0?je:j.tag)!==null&&ke!==void 0?ke:j.argument)!==null&&me!==void 0?me:j.expression}function P(j,me){if(me.expressions)return["expressions",0];if(me.left)return["left"];if(me.test)return["test"];if(me.object)return["object"];if(me.callee)return["callee"];if(me.tag)return["tag"];if(me.argument)return["argument"];if(me.expression)return["expression"];throw new Error("Unexpected node has no left side.")}function R(j){return j=new Set(j),me=>j.has(me==null?void 0:me.type)}var f=R(["Line","CommentLine","SingleLine","HashbangComment","HTMLOpen","HTMLClose"]),x=R(["ExportDefaultDeclaration","ExportDefaultSpecifier","DeclareExportDeclaration","ExportNamedDeclaration","ExportAllDeclaration"]);function m(j){let me=j.getParentNode();return j.getName()==="declaration"&&x(me)?me:null}var E=R(["BooleanLiteral","DirectiveLiteral","Literal","NullLiteral","NumericLiteral","BigIntLiteral","DecimalLiteral","RegExpLiteral","StringLiteral","TemplateLiteral","TSTypeLiteral","JSXText"]);function l(j){return j.type==="NumericLiteral"||j.type==="Literal"&&typeof j.value=="number"}function d(j){return j.type==="UnaryExpression"&&(j.operator==="+"||j.operator==="-")&&l(j.argument)}function C(j){return j.type==="StringLiteral"||j.type==="Literal"&&typeof j.value=="string"}var _=R(["ObjectTypeAnnotation","TSTypeLiteral","TSMappedType"]),b=R(["FunctionExpression","ArrowFunctionExpression"]);function N(j){return j.type==="FunctionExpression"||j.type==="ArrowFunctionExpression"&&j.body.type==="BlockStatement"}function I(j){return oe(j)&&j.callee.type==="Identifier"&&["async","inject","fakeAsync","waitForAsync"].includes(j.callee.name)}var $=R(["JSXElement","JSXFragment"]);function M(j,me){if(j.parentParser!=="markdown"&&j.parentParser!=="mdx")return!1;let ke=me.getNode();if(!ke.expression||!$(ke.expression))return!1;let je=me.getParentNode();return je.type==="Program"&&je.body.length===1}function q(j){return j.kind==="get"||j.kind==="set"}function J(j){return q(j)||h(j,j.value)}function L(j){return(j.type==="ObjectTypeProperty"||j.type==="ObjectTypeInternalSlot")&&j.value.type==="FunctionTypeAnnotation"&&!j.static&&!J(j)}function Y(j){return(j.type==="TypeAnnotation"||j.type==="TSTypeAnnotation")&&j.typeAnnotation.type==="FunctionTypeAnnotation"&&!j.static&&!h(j,j.typeAnnotation)}var V=R(["BinaryExpression","LogicalExpression","NGPipeExpression"]);function O(j){return ae(j)||j.type==="BindExpression"&&Boolean(j.object)}var K=new Set(["AnyTypeAnnotation","TSAnyKeyword","NullLiteralTypeAnnotation","TSNullKeyword","ThisTypeAnnotation","TSThisType","NumberTypeAnnotation","TSNumberKeyword","VoidTypeAnnotation","TSVoidKeyword","BooleanTypeAnnotation","TSBooleanKeyword","BigIntTypeAnnotation","TSBigIntKeyword","SymbolTypeAnnotation","TSSymbolKeyword","StringTypeAnnotation","TSStringKeyword","BooleanLiteralTypeAnnotation","StringLiteralTypeAnnotation","BigIntLiteralTypeAnnotation","NumberLiteralTypeAnnotation","TSLiteralType","TSTemplateLiteralType","EmptyTypeAnnotation","MixedTypeAnnotation","TSNeverKeyword","TSObjectKeyword","TSUndefinedKeyword","TSUnknownKeyword"]);function se(j){return j?!!((j.type==="GenericTypeAnnotation"||j.type==="TSTypeReference")&&!j.typeParameters||K.has(j.type)):!1}function Q(j){let me=/^(?:before|after)(?:Each|All)$/;return j.callee.type==="Identifier"&&me.test(j.callee.name)&&j.arguments.length===1}var le=["it","it.only","it.skip","describe","describe.only","describe.skip","test","test.only","test.skip","test.step","test.describe","test.describe.only","test.describe.parallel","test.describe.parallel.only","test.describe.serial","test.describe.serial.only","skip","xit","xdescribe","xtest","fit","fdescribe","ftest"];function W(j){return p(j,le)}function X(j,me){if(j.type!=="CallExpression")return!1;if(j.arguments.length===1){if(I(j)&&me&&X(me))return b(j.arguments[0]);if(Q(j))return I(j.arguments[0])}else if((j.arguments.length===2||j.arguments.length===3)&&(j.arguments[0].type==="TemplateLiteral"||C(j.arguments[0]))&&W(j.callee))return j.arguments[2]&&!l(j.arguments[2])?!1:(j.arguments.length===2?b(j.arguments[1]):N(j.arguments[1])&&Fe(j.arguments[1]).length<=1)||I(j.arguments[1]);return!1}var oe=R(["CallExpression","OptionalCallExpression"]),ae=R(["MemberExpression","OptionalMemberExpression"]);function Ae(j){let me="expressions";j.type==="TSTemplateLiteralType"&&(me="types");let ke=j[me];return ke.length===0?!1:ke.every(je=>{if(ue(je))return!1;if(je.type==="Identifier"||je.type==="ThisExpression")return!0;if(ae(je)){let Ye=je;for(;ae(Ye);)if(Ye.property.type!=="Identifier"&&Ye.property.type!=="Literal"&&Ye.property.type!=="StringLiteral"&&Ye.property.type!=="NumericLiteral"||(Ye=Ye.object,ue(Ye)))return!1;return Ye.type==="Identifier"||Ye.type==="ThisExpression"}return!1})}function z(j,me){return j==="+"||j==="-"?j+me:me}function H(j,me){let ke=c(me),je=r(j,y(me));return je!==!1&&j.slice(ke,ke+2)==="/*"&&j.slice(je,je+2)==="*/"}function Z(j,me){return $(me)?Ve(me):ue(me,Ie.Leading,ke=>a(j,y(ke)))}function ne(j,me){return me.parser!=="json"&&C(j.key)&&ce(j.key).slice(1,-1)===j.key.value&&(t(j.key.value)&&!(me.parser==="babel-ts"&&j.type==="ClassProperty"||me.parser==="typescript"&&j.type==="PropertyDefinition")||fe(j.key.value)&&String(Number(j.key.value))===j.key.value&&(me.parser==="babel"||me.parser==="acorn"||me.parser==="espree"||me.parser==="meriyah"||me.parser==="__babel_estree"))}function fe(j){return/^(?:\d+|\d+\.\d+)$/.test(j)}function ge(j,me){let ke=/^[fx]?(?:describe|it|test)$/;return me.type==="TaggedTemplateExpression"&&me.quasi===j&&me.tag.type==="MemberExpression"&&me.tag.property.type==="Identifier"&&me.tag.property.name==="each"&&(me.tag.object.type==="Identifier"&&ke.test(me.tag.object.name)||me.tag.object.type==="MemberExpression"&&me.tag.object.property.type==="Identifier"&&(me.tag.object.property.name==="only"||me.tag.object.property.name==="skip")&&me.tag.object.object.type==="Identifier"&&ke.test(me.tag.object.object.name))}function Ce(j){return j.quasis.some(me=>me.value.raw.includes(`
`))}function _e(j,me){return(j.type==="TemplateLiteral"&&Ce(j)||j.type==="TaggedTemplateExpression"&&Ce(j.quasi))&&!a(me,c(j),{backwards:!0})}function Oe(j){if(!ue(j))return!1;let me=s(st(j,Ie.Dangling));return me&&!g(me)}function pe(j){if(j.length<=1)return!1;let me=0;for(let ke of j)if(b(ke)){if(me+=1,me>1)return!0}else if(oe(ke)){for(let je of ke.arguments)if(b(je))return!0}return!1}function ie(j){let me=j.getValue(),ke=j.getParentNode();return oe(me)&&oe(ke)&&ke.callee===me&&me.arguments.length>ke.arguments.length&&ke.arguments.length>0}function ve(j,me){if(me>=2)return!1;let ke=ze=>ve(ze,me+1),je=j.type==="Literal"&&"regex"in j&&j.regex.pattern||j.type==="RegExpLiteral"&&j.pattern;if(je&&o(je)>5)return!1;if(j.type==="Literal"||j.type==="BigIntLiteral"||j.type==="DecimalLiteral"||j.type==="BooleanLiteral"||j.type==="NullLiteral"||j.type==="NumericLiteral"||j.type==="RegExpLiteral"||j.type==="StringLiteral"||j.type==="Identifier"||j.type==="ThisExpression"||j.type==="Super"||j.type==="PrivateName"||j.type==="PrivateIdentifier"||j.type==="ArgumentPlaceholder"||j.type==="Import")return!0;if(j.type==="TemplateLiteral")return j.quasis.every(ze=>!ze.value.raw.includes(`
`))&&j.expressions.every(ke);if(j.type==="ObjectExpression")return j.properties.every(ze=>!ze.computed&&(ze.shorthand||ze.value&&ke(ze.value)));if(j.type==="ArrayExpression")return j.elements.every(ze=>ze===null||ke(ze));if(He(j))return(j.type==="ImportExpression"||ve(j.callee,me))&&Je(j).every(ke);if(ae(j))return ve(j.object,me)&&ve(j.property,me);let Ye={"!":!0,"-":!0,"+":!0,"~":!0};if(j.type==="UnaryExpression"&&Ye[j.operator])return ve(j.argument,me);let ut={"++":!0,"--":!0};return j.type==="UpdateExpression"&&ut[j.operator]?ve(j.argument,me):j.type==="TSNonNullExpression"?ve(j.expression,me):!1}function ce(j){var me,ke;return(me=(ke=j.extra)===null||ke===void 0?void 0:ke.raw)!==null&&me!==void 0?me:j.raw}function U(j){return j}function de(j){return j.filepath&&/\.tsx$/i.test(j.filepath)}function De(j){let me=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"es5";return j.trailingComma==="es5"&&me==="es5"||j.trailingComma==="all"&&(me==="all"||me==="es5")}function he(j,me){switch(j=Ee(j),j.type){case"FunctionExpression":case"ClassExpression":case"DoExpression":return me;case"ObjectExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return he(j.object,me);case"TaggedTemplateExpression":return j.tag.type==="FunctionExpression"?!1:he(j.tag,me);case"CallExpression":case"OptionalCallExpression":return j.callee.type==="FunctionExpression"?!1:he(j.callee,me);case"ConditionalExpression":return he(j.test,me);case"UpdateExpression":return!j.prefix&&he(j.argument,me);case"BindExpression":return j.object&&he(j.object,me);case"SequenceExpression":return he(j.expressions[0],me);case"TSSatisfiesExpression":case"TSAsExpression":case"TSNonNullExpression":return he(j.expression,me);default:return!1}}var Be={"==":!0,"!=":!0,"===":!0,"!==":!0},Se={"*":!0,"/":!0,"%":!0},ye={">>":!0,">>>":!0,"<<":!0};function S(j,me){return!(te(me)!==te(j)||j==="**"||Be[j]&&Be[me]||me==="%"&&Se[j]||j==="%"&&Se[me]||me!==j&&Se[me]&&Se[j]||ye[j]&&ye[me])}var G=new Map([["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].flatMap((j,me)=>j.map(ke=>[ke,me])));function te(j){return G.get(j)}function Ee(j){for(;j.left;)j=j.left;return j}function Re(j){return Boolean(ye[j])||j==="|"||j==="^"||j==="&"}function Te(j){var me;if(j.rest)return!0;let ke=Fe(j);return((me=s(ke))===null||me===void 0?void 0:me.type)==="RestElement"}var Pe=new WeakMap;function Fe(j){if(Pe.has(j))return Pe.get(j);let me=[];return j.this&&me.push(j.this),Array.isArray(j.parameters)?me.push(...j.parameters):Array.isArray(j.params)&&me.push(...j.params),j.rest&&me.push(j.rest),Pe.set(j,me),me}function Ze(j,me){let ke=j.getValue(),je=0,Ye=ut=>me(ut,je++);ke.this&&j.call(Ye,"this"),Array.isArray(ke.parameters)?j.each(Ye,"parameters"):Array.isArray(ke.params)&&j.each(Ye,"params"),ke.rest&&j.call(Ye,"rest")}var xe=new WeakMap;function Je(j){if(xe.has(j))return xe.get(j);let me=j.arguments;return j.type==="ImportExpression"&&(me=[j.source],j.attributes&&me.push(j.attributes)),xe.set(j,me),me}function Ne(j,me){let ke=j.getValue();ke.type==="ImportExpression"?(j.call(je=>me(je,0),"source"),ke.attributes&&j.call(je=>me(je,1),"attributes")):j.each(me,"arguments")}function Le(j){return j.value.trim()==="prettier-ignore"&&!j.unignore}function Ve(j){return j&&(j.prettierIgnore||ue(j,Ie.PrettierIgnore))}function be(j){let me=j.getValue();return Ve(me)}var Ie={Leading:1<<1,Trailing:1<<2,Dangling:1<<3,Block:1<<4,Line:1<<5,PrettierIgnore:1<<6,First:1<<7,Last:1<<8},Me=(j,me)=>{if(typeof j=="function"&&(me=j,j=0),j||me)return(ke,je,Ye)=>!(j&Ie.Leading&&!ke.leading||j&Ie.Trailing&&!ke.trailing||j&Ie.Dangling&&(ke.leading||ke.trailing)||j&Ie.Block&&!g(ke)||j&Ie.Line&&!f(ke)||j&Ie.First&&je!==0||j&Ie.Last&&je!==Ye.length-1||j&Ie.PrettierIgnore&&!Le(ke)||me&&!me(ke))};function ue(j,me,ke){if(!u(j==null?void 0:j.comments))return!1;let je=Me(me,ke);return je?j.comments.some(je):!0}function st(j,me,ke){if(!Array.isArray(j==null?void 0:j.comments))return[];let je=Me(me,ke);return je?j.comments.filter(je):j.comments}var rt=(j,me)=>{let{originalText:ke}=me;return i(ke,y(j))};function He(j){return oe(j)||j.type==="NewExpression"||j.type==="ImportExpression"}function Ue(j){return j&&(j.type==="ObjectProperty"||j.type==="Property"&&!j.method&&j.kind==="init")}function Xe(j){return Boolean(j.__isUsingHackPipeline)}var at=Symbol("ifWithoutBlockAndSameLineComment");function nt(j){return j.type==="TSAsExpression"||j.type==="TSSatisfiesExpression"}n.exports={getFunctionParameters:Fe,iterateFunctionParametersPath:Ze,getCallArguments:Je,iterateCallArgumentsPath:Ne,hasRestParameter:Te,getLeftSide:k,getLeftSidePathName:P,getParentExportDeclaration:m,getTypeScriptMappedTypeModifier:z,hasFlowAnnotationComment:F,hasFlowShorthandAnnotationComment:T,hasLeadingOwnLineComment:Z,hasNakedLeftSide:B,hasNode:A,hasIgnoreComment:be,hasNodeIgnoreComment:Ve,identity:U,isBinaryish:V,isCallLikeExpression:He,isEnabledHackPipeline:Xe,isLineComment:f,isPrettierIgnoreComment:Le,isCallExpression:oe,isMemberExpression:ae,isExportDeclaration:x,isFlowAnnotationComment:H,isFunctionCompositionArgs:pe,isFunctionNotation:J,isFunctionOrArrowExpression:b,isGetterOrSetter:q,isJestEachTemplateLiteral:ge,isJsxNode:$,isLiteral:E,isLongCurriedCallExpression:ie,isSimpleCallArgument:ve,isMemberish:O,isNumericLiteral:l,isSignedNumericLiteral:d,isObjectProperty:Ue,isObjectType:_,isObjectTypePropertyAFunction:L,isSimpleType:se,isSimpleNumber:fe,isSimpleTemplateLiteral:Ae,isStringLiteral:C,isStringPropSafeToUnquote:ne,isTemplateOnItsOwnLine:_e,isTestCall:X,isTheOnlyJsxElementInMarkdown:M,isTSXFile:de,isTypeAnnotationAFunction:Y,isNextLineEmpty:rt,needsHardlineAfterDanglingComment:Oe,rawText:ce,shouldPrintComma:De,isBitwiseOperator:Re,shouldFlatten:S,startsWithNoLookaheadToken:he,getPrecedence:te,hasComment:ue,getComments:st,CommentCheckFlags:Ie,markerForIfWithoutBlockAndSameLineComment:at,isTSTypeExpression:nt}}}),It=ee({"src/language-js/print/template-literal.js"(e,n){"use strict";re();var t=lt(),{getStringWidth:s,getIndentSize:a}=Ge(),{builders:{join:r,hardline:u,softline:i,group:o,indent:c,align:y,lineSuffixBoundary:h,addAlignmentToDoc:g},printer:{printDocToString:p},utils:{mapDoc:D}}=qe(),{isBinaryish:v,isJestEachTemplateLiteral:w,isSimpleTemplateLiteral:T,hasComment:F,isMemberExpression:A,isTSTypeExpression:B}=Ke();function k(E,l,d){let C=E.getValue();if(C.type==="TemplateLiteral"&&w(C,E.getParentNode())){let M=P(E,d,l);if(M)return M}let b="expressions";C.type==="TSTemplateLiteralType"&&(b="types");let N=[],I=E.map(l,b),$=T(C);return $&&(I=I.map(M=>p(M,Object.assign(Object.assign({},d),{},{printWidth:Number.POSITIVE_INFINITY})).formatted)),N.push(h,"`"),E.each(M=>{let q=M.getName();if(N.push(l()),q<I.length){let{tabWidth:J}=d,L=M.getValue(),Y=a(L.value.raw,J),V=I[q];if(!$){let K=C[b][q];(F(K)||A(K)||K.type==="ConditionalExpression"||K.type==="SequenceExpression"||B(K)||v(K))&&(V=[c([i,V]),i])}let O=Y===0&&L.value.raw.endsWith(`
`)?y(Number.NEGATIVE_INFINITY,V):g(V,Y,J);N.push(o(["${",O,h,"}"]))}},"quasis"),N.push("`"),N}function P(E,l,d){let C=E.getNode(),_=C.quasis[0].value.raw.trim().split(/\s*\|\s*/);if(_.length>1||_.some(b=>b.length>0)){l.__inJestEach=!0;let b=E.map(d,"expressions");l.__inJestEach=!1;let N=[],I=b.map(L=>"${"+p(L,Object.assign(Object.assign({},l),{},{printWidth:Number.POSITIVE_INFINITY,endOfLine:"lf"})).formatted+"}"),$=[{hasLineBreak:!1,cells:[]}];for(let L=1;L<C.quasis.length;L++){let Y=t($),V=I[L-1];Y.cells.push(V),V.includes(`
`)&&(Y.hasLineBreak=!0),C.quasis[L].value.raw.includes(`
`)&&$.push({hasLineBreak:!1,cells:[]})}let M=Math.max(_.length,...$.map(L=>L.cells.length)),q=Array.from({length:M}).fill(0),J=[{cells:_},...$.filter(L=>L.cells.length>0)];for(let{cells:L}of J.filter(Y=>!Y.hasLineBreak))for(let[Y,V]of L.entries())q[Y]=Math.max(q[Y],s(V));return N.push(h,"`",c([u,r(u,J.map(L=>r(" | ",L.cells.map((Y,V)=>L.hasLineBreak?Y:Y+" ".repeat(q[V]-s(Y))))))]),u,"`"),N}}function R(E,l){let d=E.getValue(),C=l();return F(d)&&(C=o([c([i,C]),i])),["${",C,h,"}"]}function f(E,l){return E.map(d=>R(d,l),"expressions")}function x(E,l){return D(E,d=>typeof d=="string"?l?d.replace(/(\\*)`/g,"$1$1\\`"):m(d):d)}function m(E){return E.replace(/([\\`]|\${)/g,"\\$1")}n.exports={printTemplateLiteral:k,printTemplateExpressions:f,escapeTemplateCharacters:x,uncookTemplateElementValue:m}}}),ym=ee({"src/language-js/embed/markdown.js"(e,n){"use strict";re();var{builders:{indent:t,softline:s,literalline:a,dedentToRoot:r}}=qe(),{escapeTemplateCharacters:u}=It();function i(c,y,h){let p=c.getValue().quasis[0].value.raw.replace(/((?:\\\\)*)\\`/g,(T,F)=>"\\".repeat(F.length/2)+"`"),D=o(p),v=D!=="";v&&(p=p.replace(new RegExp(`^${D}`,"gm"),""));let w=u(h(p,{parser:"markdown",__inJsTemplate:!0},{stripTrailingHardline:!0}),!0);return["`",v?t([s,w]):[a,r(w)],s,"`"]}function o(c){let y=c.match(/^([^\S\n]*)\S/m);return y===null?"":y[1]}n.exports=i}}),hm=ee({"src/language-js/embed/css.js"(e,n){"use strict";re();var{isNonEmptyArray:t}=Ge(),{builders:{indent:s,hardline:a,softline:r},utils:{mapDoc:u,replaceEndOfLine:i,cleanDoc:o}}=qe(),{printTemplateExpressions:c}=It();function y(p,D,v){let w=p.getValue(),T=w.quasis.map(P=>P.value.raw),F=0,A=T.reduce((P,R,f)=>f===0?R:P+"@prettier-placeholder-"+F+++"-id"+R,""),B=v(A,{parser:"scss"},{stripTrailingHardline:!0}),k=c(p,D);return h(B,w,k)}function h(p,D,v){if(D.quasis.length===1&&!D.quasis[0].value.raw.trim())return"``";let T=g(p,v);if(!T)throw new Error("Couldn't insert all the expressions");return["`",s([a,T]),r,"`"]}function g(p,D){if(!t(D))return p;let v=0,w=u(o(p),T=>typeof T!="string"||!T.includes("@prettier-placeholder")?T:T.split(/@prettier-placeholder-(\d+)-id/).map((F,A)=>A%2===0?i(F):(v++,D[F])));return D.length===v?w:null}n.exports=y}}),vm=ee({"src/language-js/embed/graphql.js"(e,n){"use strict";re();var{builders:{indent:t,join:s,hardline:a}}=qe(),{escapeTemplateCharacters:r,printTemplateExpressions:u}=It();function i(c,y,h){let g=c.getValue(),p=g.quasis.length;if(p===1&&g.quasis[0].value.raw.trim()==="")return"``";let D=u(c,y),v=[];for(let w=0;w<p;w++){let T=g.quasis[w],F=w===0,A=w===p-1,B=T.value.cooked,k=B.split(`
`),P=k.length,R=D[w],f=P>2&&k[0].trim()===""&&k[1].trim()==="",x=P>2&&k[P-1].trim()===""&&k[P-2].trim()==="",m=k.every(l=>/^\s*(?:#[^\n\r]*)?$/.test(l));if(!A&&/#[^\n\r]*$/.test(k[P-1]))return null;let E=null;m?E=o(k):E=h(B,{parser:"graphql"},{stripTrailingHardline:!0}),E?(E=r(E,!1),!F&&f&&v.push(""),v.push(E),!A&&x&&v.push("")):!F&&!A&&f&&v.push(""),R&&v.push(R)}return["`",t([a,s(a,v)]),a,"`"]}function o(c){let y=[],h=!1,g=c.map(p=>p.trim());for(let[p,D]of g.entries())D!==""&&(g[p-1]===""&&h?y.push([a,D]):y.push(D),h=!0);return y.length===0?null:s(a,y)}n.exports=i}}),Cm=ee({"src/language-js/embed/html.js"(e,n){"use strict";re();var{builders:{indent:t,line:s,hardline:a,group:r},utils:{mapDoc:u}}=qe(),{printTemplateExpressions:i,uncookTemplateElementValue:o}=It(),c=0;function y(h,g,p,D,v){let{parser:w}=v,T=h.getValue(),F=c;c=c+1>>>0;let A=d=>`PRETTIER_HTML_PLACEHOLDER_${d}_${F}_IN_JS`,B=T.quasis.map((d,C,_)=>C===_.length-1?d.value.cooked:d.value.cooked+A(C)).join(""),k=i(h,g);if(k.length===0&&B.trim().length===0)return"``";let P=new RegExp(A("(\\d+)"),"g"),R=0,f=p(B,{parser:w,__onHtmlRoot(d){R=d.children.length}},{stripTrailingHardline:!0}),x=u(f,d=>{if(typeof d!="string")return d;let C=[],_=d.split(P);for(let b=0;b<_.length;b++){let N=_[b];if(b%2===0){N&&(N=o(N),D.__embeddedInHtml&&(N=N.replace(/<\/(script)\b/gi,"<\\/$1")),C.push(N));continue}let I=Number(N);C.push(k[I])}return C}),m=/^\s/.test(B)?" ":"",E=/\s$/.test(B)?" ":"",l=D.htmlWhitespaceSensitivity==="ignore"?a:m&&E?s:null;return r(l?["`",t([l,r(x)]),l,"`"]:["`",m,R>1?t(r(x)):r(x),E,"`"])}n.exports=y}}),Em=ee({"src/language-js/embed.js"(e,n){"use strict";re();var{hasComment:t,CommentCheckFlags:s,isObjectProperty:a}=Ke(),r=ym(),u=hm(),i=vm(),o=Cm();function c(f){if(g(f)||w(f)||T(f)||p(f))return"css";if(B(f))return"graphql";if(P(f))return"html";if(D(f))return"angular";if(h(f))return"markdown"}function y(f,x,m,E){let l=f.getValue();if(l.type!=="TemplateLiteral"||R(l))return;let d=c(f);if(!!d){if(d==="markdown")return r(f,x,m);if(d==="css")return u(f,x,m);if(d==="graphql")return i(f,x,m);if(d==="html"||d==="angular")return o(f,x,m,E,{parser:d})}}function h(f){let x=f.getValue(),m=f.getParentNode();return m&&m.type==="TaggedTemplateExpression"&&x.quasis.length===1&&m.tag.type==="Identifier"&&(m.tag.name==="md"||m.tag.name==="markdown")}function g(f){let x=f.getValue(),m=f.getParentNode(),E=f.getParentNode(1);return E&&x.quasis&&m.type==="JSXExpressionContainer"&&E.type==="JSXElement"&&E.openingElement.name.name==="style"&&E.openingElement.attributes.some(l=>l.name.name==="jsx")||m&&m.type==="TaggedTemplateExpression"&&m.tag.type==="Identifier"&&m.tag.name==="css"||m&&m.type==="TaggedTemplateExpression"&&m.tag.type==="MemberExpression"&&m.tag.object.name==="css"&&(m.tag.property.name==="global"||m.tag.property.name==="resolve")}function p(f){return f.match(x=>x.type==="TemplateLiteral",(x,m)=>x.type==="ArrayExpression"&&m==="elements",(x,m)=>a(x)&&x.key.type==="Identifier"&&x.key.name==="styles"&&m==="value",...v)}function D(f){return f.match(x=>x.type==="TemplateLiteral",(x,m)=>a(x)&&x.key.type==="Identifier"&&x.key.name==="template"&&m==="value",...v)}var v=[(f,x)=>f.type==="ObjectExpression"&&x==="properties",(f,x)=>f.type==="CallExpression"&&f.callee.type==="Identifier"&&f.callee.name==="Component"&&x==="arguments",(f,x)=>f.type==="Decorator"&&x==="expression"];function w(f){let x=f.getParentNode();if(!x||x.type!=="TaggedTemplateExpression")return!1;let m=x.tag.type==="ParenthesizedExpression"?x.tag.expression:x.tag;switch(m.type){case"MemberExpression":return F(m.object)||A(m);case"CallExpression":return F(m.callee)||m.callee.type==="MemberExpression"&&(m.callee.object.type==="MemberExpression"&&(F(m.callee.object.object)||A(m.callee.object))||m.callee.object.type==="CallExpression"&&F(m.callee.object.callee));case"Identifier":return m.name==="css";default:return!1}}function T(f){let x=f.getParentNode(),m=f.getParentNode(1);return m&&x.type==="JSXExpressionContainer"&&m.type==="JSXAttribute"&&m.name.type==="JSXIdentifier"&&m.name.name==="css"}function F(f){return f.type==="Identifier"&&f.name==="styled"}function A(f){return/^[A-Z]/.test(f.object.name)&&f.property.name==="extend"}function B(f){let x=f.getValue(),m=f.getParentNode();return k(x,"GraphQL")||m&&(m.type==="TaggedTemplateExpression"&&(m.tag.type==="MemberExpression"&&m.tag.object.name==="graphql"&&m.tag.property.name==="experimental"||m.tag.type==="Identifier"&&(m.tag.name==="gql"||m.tag.name==="graphql"))||m.type==="CallExpression"&&m.callee.type==="Identifier"&&m.callee.name==="graphql")}function k(f,x){return t(f,s.Block|s.Leading,m=>{let{value:E}=m;return E===` ${x} `})}function P(f){return k(f.getValue(),"HTML")||f.match(x=>x.type==="TemplateLiteral",(x,m)=>x.type==="TaggedTemplateExpression"&&x.tag.type==="Identifier"&&x.tag.name==="html"&&m==="quasi")}function R(f){let{quasis:x}=f;return x.some(m=>{let{value:{cooked:E}}=m;return E===null})}n.exports=y}}),Fm=ee({"src/language-js/clean.js"(e,n){"use strict";re();var t=kt(),s=new Set(["range","raw","comments","leadingComments","trailingComments","innerComments","extra","start","end","loc","flags","errors","tokens"]),a=u=>{for(let i of u.quasis)delete i.value};function r(u,i,o){if(u.type==="Program"&&delete i.sourceType,(u.type==="BigIntLiteral"||u.type==="BigIntLiteralTypeAnnotation")&&i.value&&(i.value=i.value.toLowerCase()),(u.type==="BigIntLiteral"||u.type==="Literal")&&i.bigint&&(i.bigint=i.bigint.toLowerCase()),u.type==="DecimalLiteral"&&(i.value=Number(i.value)),u.type==="Literal"&&i.decimal&&(i.decimal=Number(i.decimal)),u.type==="EmptyStatement"||u.type==="JSXText"||u.type==="JSXExpressionContainer"&&(u.expression.type==="Literal"||u.expression.type==="StringLiteral")&&u.expression.value===" ")return null;if((u.type==="Property"||u.type==="ObjectProperty"||u.type==="MethodDefinition"||u.type==="ClassProperty"||u.type==="ClassMethod"||u.type==="PropertyDefinition"||u.type==="TSDeclareMethod"||u.type==="TSPropertySignature"||u.type==="ObjectTypeProperty")&&typeof u.key=="object"&&u.key&&(u.key.type==="Literal"||u.key.type==="NumericLiteral"||u.key.type==="StringLiteral"||u.key.type==="Identifier")&&delete i.key,u.type==="JSXElement"&&u.openingElement.name.name==="style"&&u.openingElement.attributes.some(h=>h.name.name==="jsx"))for(let{type:h,expression:g}of i.children)h==="JSXExpressionContainer"&&g.type==="TemplateLiteral"&&a(g);u.type==="JSXAttribute"&&u.name.name==="css"&&u.value.type==="JSXExpressionContainer"&&u.value.expression.type==="TemplateLiteral"&&a(i.value.expression),u.type==="JSXAttribute"&&u.value&&u.value.type==="Literal"&&/["']|&quot;|&apos;/.test(u.value.value)&&(i.value.value=i.value.value.replace(/["']|&quot;|&apos;/g,'"'));let c=u.expression||u.callee;if(u.type==="Decorator"&&c.type==="CallExpression"&&c.callee.name==="Component"&&c.arguments.length===1){let h=u.expression.arguments[0].properties;for(let[g,p]of i.expression.arguments[0].properties.entries())switch(h[g].key.name){case"styles":p.value.type==="ArrayExpression"&&a(p.value.elements[0]);break;case"template":p.value.type==="TemplateLiteral"&&a(p.value);break}}if(u.type==="TaggedTemplateExpression"&&(u.tag.type==="MemberExpression"||u.tag.type==="Identifier"&&(u.tag.name==="gql"||u.tag.name==="graphql"||u.tag.name==="css"||u.tag.name==="md"||u.tag.name==="markdown"||u.tag.name==="html")||u.tag.type==="CallExpression")&&a(i.quasi),u.type==="TemplateLiteral"){var y;(((y=u.leadingComments)===null||y===void 0?void 0:y.some(g=>t(g)&&["GraphQL","HTML"].some(p=>g.value===` ${p} `)))||o.type==="CallExpression"&&o.callee.name==="graphql"||!u.leadingComments)&&a(i)}if(u.type==="InterpreterDirective"&&(i.value=i.value.trimEnd()),(u.type==="TSIntersectionType"||u.type==="TSUnionType")&&u.types.length===1)return i.types[0]}r.ignoredProperties=s,n.exports=r}}),Pa={};Ut(Pa,{EOL:()=>_n,arch:()=>Am,cpus:()=>Ma,default:()=>Ha,endianness:()=>ka,freemem:()=>Oa,getNetworkInterfaces:()=>Wa,hostname:()=>Ia,loadavg:()=>La,networkInterfaces:()=>Va,platform:()=>Sm,release:()=>$a,tmpDir:()=>Nn,tmpdir:()=>wn,totalmem:()=>qa,type:()=>Ra,uptime:()=>ja});function ka(){if(typeof Sr>"u"){var e=new ArrayBuffer(2),n=new Uint8Array(e),t=new Uint16Array(e);if(n[0]=1,n[1]=2,t[0]===258)Sr="BE";else if(t[0]===513)Sr="LE";else throw new Error("unable to figure out endianess")}return Sr}function Ia(){return typeof globalThis.location<"u"?globalThis.location.hostname:""}function La(){return[]}function ja(){return 0}function Oa(){return Number.MAX_VALUE}function qa(){return Number.MAX_VALUE}function Ma(){return[]}function Ra(){return"Browser"}function $a(){return typeof globalThis.navigator<"u"?globalThis.navigator.appVersion:""}function Va(){}function Wa(){}function Am(){return"javascript"}function Sm(){return"browser"}function Nn(){return"/tmp"}var Sr,wn,_n,Ha,xm=gt({"node-modules-polyfills:os"(){re(),wn=Nn,_n=`
`,Ha={EOL:_n,tmpdir:wn,tmpDir:Nn,networkInterfaces:Va,getNetworkInterfaces:Wa,release:$a,type:Ra,cpus:Ma,totalmem:qa,freemem:Oa,uptime:ja,loadavg:La,hostname:Ia,endianness:ka}}}),bm=ee({"node-modules-polyfills-commonjs:os"(e,n){re();var t=(xm(),ft(Pa));if(t&&t.default){n.exports=t.default;for(let s in t)n.exports[s]=t[s]}else t&&(n.exports=t)}}),Tm=ee({"node_modules/detect-newline/index.js"(e,n){"use strict";re();var t=s=>{if(typeof s!="string")throw new TypeError("Expected a string");let a=s.match(/(?:\r?\n)/g)||[];if(a.length===0)return;let r=a.filter(i=>i===`\r
`).length,u=a.length-r;return r>u?`\r
`:`
`};n.exports=t,n.exports.graceful=s=>typeof s=="string"&&t(s)||`
`}}),Bm=ee({"node_modules/jest-docblock/build/index.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.extract=p,e.parse=v,e.parseWithComments=w,e.print=T,e.strip=D;function n(){let A=bm();return n=function(){return A},A}function t(){let A=s(Tm());return t=function(){return A},A}function s(A){return A&&A.__esModule?A:{default:A}}var a=/\*\/$/,r=/^\/\*\*?/,u=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,i=/(^|\s+)\/\/([^\r\n]*)/g,o=/^(\r?\n)+/,c=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,y=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,h=/(\r?\n|^) *\* ?/g,g=[];function p(A){let B=A.match(u);return B?B[0].trimLeft():""}function D(A){let B=A.match(u);return B&&B[0]?A.substring(B[0].length):A}function v(A){return w(A).pragmas}function w(A){let B=(0,t().default)(A)||n().EOL;A=A.replace(r,"").replace(a,"").replace(h,"$1");let k="";for(;k!==A;)k=A,A=A.replace(c,`${B}$1 $2${B}`);A=A.replace(o,"").trimRight();let P=Object.create(null),R=A.replace(y,"").replace(o,"").trimRight(),f;for(;f=y.exec(A);){let x=f[2].replace(i,"");typeof P[f[1]]=="string"||Array.isArray(P[f[1]])?P[f[1]]=g.concat(P[f[1]],x):P[f[1]]=x}return{comments:R,pragmas:P}}function T(A){let{comments:B="",pragmas:k={}}=A,P=(0,t().default)(B)||n().EOL,R="/**",f=" *",x=" */",m=Object.keys(k),E=m.map(d=>F(d,k[d])).reduce((d,C)=>d.concat(C),[]).map(d=>`${f} ${d}${P}`).join("");if(!B){if(m.length===0)return"";if(m.length===1&&!Array.isArray(k[m[0]])){let d=k[m[0]];return`${R} ${F(m[0],d)[0]}${x}`}}let l=B.split(P).map(d=>`${f} ${d}`).join(P)+P;return R+P+(B?l:"")+(B&&m.length?f+P:"")+E+x}function F(A,B){return g.concat(B).map(k=>`@${A} ${k}`.trim())}}}),Nm=ee({"src/language-js/utils/get-shebang.js"(e,n){"use strict";re();function t(s){if(!s.startsWith("#!"))return"";let a=s.indexOf(`
`);return a===-1?s:s.slice(0,a)}n.exports=t}}),Ga=ee({"src/language-js/pragma.js"(e,n){"use strict";re();var{parseWithComments:t,strip:s,extract:a,print:r}=Bm(),{normalizeEndOfLine:u}=Ln(),i=Nm();function o(h){let g=i(h);g&&(h=h.slice(g.length+1));let p=a(h),{pragmas:D,comments:v}=t(p);return{shebang:g,text:h,pragmas:D,comments:v}}function c(h){let g=Object.keys(o(h).pragmas);return g.includes("prettier")||g.includes("format")}function y(h){let{shebang:g,text:p,pragmas:D,comments:v}=o(h),w=s(p),T=r({pragmas:Object.assign({format:""},D),comments:v.trimStart()});return(g?`${g}
`:"")+u(T)+(w.startsWith(`
`)?`
`:`

`)+w}n.exports={hasPragma:c,insertPragma:y}}}),Ua=ee({"src/language-js/comments.js"(e,n){"use strict";re();var{getLast:t,hasNewline:s,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:a,getNextNonSpaceNonCommentCharacter:r,hasNewlineInRange:u,addLeadingComment:i,addTrailingComment:o,addDanglingComment:c,getNextNonSpaceNonCommentCharacterIndex:y,isNonEmptyArray:h}=Ge(),{getFunctionParameters:g,isPrettierIgnoreComment:p,isJsxNode:D,hasFlowShorthandAnnotationComment:v,hasFlowAnnotationComment:w,hasIgnoreComment:T,isCallLikeExpression:F,getCallArguments:A,isCallExpression:B,isMemberExpression:k,isObjectProperty:P,isLineComment:R,getComments:f,CommentCheckFlags:x,markerForIfWithoutBlockAndSameLineComment:m}=Ke(),{locStart:E,locEnd:l}=it(),d=kt();function C(ye){return[ce,ae,L,M,q,J,K,ge,Z,fe,Ce,_e,Q,Ae,z].some(S=>S(ye))}function _(ye){return[$,ae,Y,Ce,M,q,J,K,Ae,H,ne,fe,ie,z,de].some(S=>S(ye))}function b(ye){return[ce,M,q,V,oe,Q,fe,X,W,U,z,ve].some(S=>S(ye))}function N(ye,S){let G=(ye.body||ye.properties).find(te=>{let{type:Ee}=te;return Ee!=="EmptyStatement"});G?i(G,S):c(ye,S)}function I(ye,S){ye.type==="BlockStatement"?N(ye,S):i(ye,S)}function $(ye){let{comment:S,followingNode:G}=ye;return G&&Be(S)?(i(G,S),!0):!1}function M(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee,text:Re}=ye;if((te==null?void 0:te.type)!=="IfStatement"||!Ee)return!1;if(r(Re,S,l)===")")return o(G,S),!0;if(G===te.consequent&&Ee===te.alternate){if(G.type==="BlockStatement")o(G,S);else{let Pe=S.type==="SingleLine"||S.loc.start.line===S.loc.end.line,Fe=S.loc.start.line===G.loc.start.line;Pe&&Fe?c(G,S,m):c(te,S)}return!0}return Ee.type==="BlockStatement"?(N(Ee,S),!0):Ee.type==="IfStatement"?(I(Ee.consequent,S),!0):te.consequent===Ee?(i(Ee,S),!0):!1}function q(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee,text:Re}=ye;return(te==null?void 0:te.type)!=="WhileStatement"||!Ee?!1:r(Re,S,l)===")"?(o(G,S),!0):Ee.type==="BlockStatement"?(N(Ee,S),!0):te.body===Ee?(i(Ee,S),!0):!1}function J(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee}=ye;return(te==null?void 0:te.type)!=="TryStatement"&&(te==null?void 0:te.type)!=="CatchClause"||!Ee?!1:te.type==="CatchClause"&&G?(o(G,S),!0):Ee.type==="BlockStatement"?(N(Ee,S),!0):Ee.type==="TryStatement"?(I(Ee.finalizer,S),!0):Ee.type==="CatchClause"?(I(Ee.body,S),!0):!1}function L(ye){let{comment:S,enclosingNode:G,followingNode:te}=ye;return k(G)&&(te==null?void 0:te.type)==="Identifier"?(i(G,S),!0):!1}function Y(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee,text:Re}=ye,Te=G&&!u(Re,l(G),E(S));return(!G||!Te)&&((te==null?void 0:te.type)==="ConditionalExpression"||(te==null?void 0:te.type)==="TSConditionalType")&&Ee?(i(Ee,S),!0):!1}function V(ye){let{comment:S,precedingNode:G,enclosingNode:te}=ye;return P(te)&&te.shorthand&&te.key===G&&te.value.type==="AssignmentPattern"?(o(te.value.left,S),!0):!1}var O=new Set(["ClassDeclaration","ClassExpression","DeclareClass","DeclareInterface","InterfaceDeclaration","TSInterfaceDeclaration"]);function K(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee}=ye;if(O.has(te==null?void 0:te.type)){if(h(te.decorators)&&!(Ee&&Ee.type==="Decorator"))return o(t(te.decorators),S),!0;if(te.body&&Ee===te.body)return N(te.body,S),!0;if(Ee){if(te.superClass&&Ee===te.superClass&&G&&(G===te.id||G===te.typeParameters))return o(G,S),!0;for(let Re of["implements","extends","mixins"])if(te[Re]&&Ee===te[Re][0])return G&&(G===te.id||G===te.typeParameters||G===te.superClass)?o(G,S):c(te,S,Re),!0}}return!1}var se=new Set(["ClassMethod","ClassProperty","PropertyDefinition","TSAbstractPropertyDefinition","TSAbstractMethodDefinition","TSDeclareMethod","MethodDefinition"]);function Q(ye){let{comment:S,precedingNode:G,enclosingNode:te,text:Ee}=ye;return te&&G&&r(Ee,S,l)==="("&&(te.type==="Property"||te.type==="TSDeclareMethod"||te.type==="TSAbstractMethodDefinition")&&G.type==="Identifier"&&te.key===G&&r(Ee,G,l)!==":"||(G==null?void 0:G.type)==="Decorator"&&se.has(te==null?void 0:te.type)?(o(G,S),!0):!1}var le=new Set(["FunctionDeclaration","FunctionExpression","ClassMethod","MethodDefinition","ObjectMethod"]);function W(ye){let{comment:S,precedingNode:G,enclosingNode:te,text:Ee}=ye;return r(Ee,S,l)!=="("?!1:G&&le.has(te==null?void 0:te.type)?(o(G,S),!0):!1}function X(ye){let{comment:S,enclosingNode:G,text:te}=ye;if((G==null?void 0:G.type)!=="ArrowFunctionExpression")return!1;let Ee=y(te,S,l);return Ee!==!1&&te.slice(Ee,Ee+2)==="=>"?(c(G,S),!0):!1}function oe(ye){let{comment:S,enclosingNode:G,text:te}=ye;return r(te,S,l)!==")"?!1:G&&(De(G)&&g(G).length===0||F(G)&&A(G).length===0)?(c(G,S),!0):((G==null?void 0:G.type)==="MethodDefinition"||(G==null?void 0:G.type)==="TSAbstractMethodDefinition")&&g(G.value).length===0?(c(G.value,S),!0):!1}function ae(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee,text:Re}=ye;if((G==null?void 0:G.type)==="FunctionTypeParam"&&(te==null?void 0:te.type)==="FunctionTypeAnnotation"&&(Ee==null?void 0:Ee.type)!=="FunctionTypeParam"||((G==null?void 0:G.type)==="Identifier"||(G==null?void 0:G.type)==="AssignmentPattern")&&te&&De(te)&&r(Re,S,l)===")")return o(G,S),!0;if((te==null?void 0:te.type)==="FunctionDeclaration"&&(Ee==null?void 0:Ee.type)==="BlockStatement"){let Te=(()=>{let Pe=g(te);if(Pe.length>0)return a(Re,l(t(Pe)));let Fe=a(Re,l(te.id));return Fe!==!1&&a(Re,Fe+1)})();if(E(S)>Te)return N(Ee,S),!0}return!1}function Ae(ye){let{comment:S,enclosingNode:G}=ye;return(G==null?void 0:G.type)==="LabeledStatement"?(i(G,S),!0):!1}function z(ye){let{comment:S,enclosingNode:G}=ye;return((G==null?void 0:G.type)==="ContinueStatement"||(G==null?void 0:G.type)==="BreakStatement")&&!G.label?(o(G,S),!0):!1}function H(ye){let{comment:S,precedingNode:G,enclosingNode:te}=ye;return B(te)&&G&&te.callee===G&&te.arguments.length>0?(i(te.arguments[0],S),!0):!1}function Z(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee}=ye;return(te==null?void 0:te.type)==="UnionTypeAnnotation"||(te==null?void 0:te.type)==="TSUnionType"?(p(S)&&(Ee.prettierIgnore=!0,S.unignore=!0),G?(o(G,S),!0):!1):(((Ee==null?void 0:Ee.type)==="UnionTypeAnnotation"||(Ee==null?void 0:Ee.type)==="TSUnionType")&&p(S)&&(Ee.types[0].prettierIgnore=!0,S.unignore=!0),!1)}function ne(ye){let{comment:S,enclosingNode:G}=ye;return P(G)?(i(G,S),!0):!1}function fe(ye){let{comment:S,enclosingNode:G,followingNode:te,ast:Ee,isLastComment:Re}=ye;return Ee&&Ee.body&&Ee.body.length===0?(Re?c(Ee,S):i(Ee,S),!0):(G==null?void 0:G.type)==="Program"&&(G==null?void 0:G.body.length)===0&&!h(G.directives)?(Re?c(G,S):i(G,S),!0):(te==null?void 0:te.type)==="Program"&&(te==null?void 0:te.body.length)===0&&(G==null?void 0:G.type)==="ModuleExpression"?(c(te,S),!0):!1}function ge(ye){let{comment:S,enclosingNode:G}=ye;return(G==null?void 0:G.type)==="ForInStatement"||(G==null?void 0:G.type)==="ForOfStatement"?(i(G,S),!0):!1}function Ce(ye){let{comment:S,precedingNode:G,enclosingNode:te,text:Ee}=ye;if((te==null?void 0:te.type)==="ImportSpecifier"||(te==null?void 0:te.type)==="ExportSpecifier")return i(te,S),!0;let Re=(G==null?void 0:G.type)==="ImportSpecifier"&&(te==null?void 0:te.type)==="ImportDeclaration",Te=(G==null?void 0:G.type)==="ExportSpecifier"&&(te==null?void 0:te.type)==="ExportNamedDeclaration";return(Re||Te)&&s(Ee,l(S))?(o(G,S),!0):!1}function _e(ye){let{comment:S,enclosingNode:G}=ye;return(G==null?void 0:G.type)==="AssignmentPattern"?(i(G,S),!0):!1}var Oe=new Set(["VariableDeclarator","AssignmentExpression","TypeAlias","TSTypeAliasDeclaration"]),pe=new Set(["ObjectExpression","ArrayExpression","TemplateLiteral","TaggedTemplateExpression","ObjectTypeAnnotation","TSTypeLiteral"]);function ie(ye){let{comment:S,enclosingNode:G,followingNode:te}=ye;return Oe.has(G==null?void 0:G.type)&&te&&(pe.has(te.type)||d(S))?(i(te,S),!0):!1}function ve(ye){let{comment:S,enclosingNode:G,followingNode:te,text:Ee}=ye;return!te&&((G==null?void 0:G.type)==="TSMethodSignature"||(G==null?void 0:G.type)==="TSDeclareFunction"||(G==null?void 0:G.type)==="TSAbstractMethodDefinition")&&r(Ee,S,l)===";"?(o(G,S),!0):!1}function ce(ye){let{comment:S,enclosingNode:G,followingNode:te}=ye;if(p(S)&&(G==null?void 0:G.type)==="TSMappedType"&&(te==null?void 0:te.type)==="TSTypeParameter"&&te.constraint)return G.prettierIgnore=!0,S.unignore=!0,!0}function U(ye){let{comment:S,precedingNode:G,enclosingNode:te,followingNode:Ee}=ye;return(te==null?void 0:te.type)!=="TSMappedType"?!1:(Ee==null?void 0:Ee.type)==="TSTypeParameter"&&Ee.name?(i(Ee.name,S),!0):(G==null?void 0:G.type)==="TSTypeParameter"&&G.constraint?(o(G.constraint,S),!0):!1}function de(ye){let{comment:S,enclosingNode:G,followingNode:te}=ye;return!G||G.type!=="SwitchCase"||G.test?!1:(te.type==="BlockStatement"&&R(S)?N(te,S):c(G,S),!0)}function De(ye){return ye.type==="ArrowFunctionExpression"||ye.type==="FunctionExpression"||ye.type==="FunctionDeclaration"||ye.type==="ObjectMethod"||ye.type==="ClassMethod"||ye.type==="TSDeclareFunction"||ye.type==="TSCallSignatureDeclaration"||ye.type==="TSConstructSignatureDeclaration"||ye.type==="TSMethodSignature"||ye.type==="TSConstructorType"||ye.type==="TSFunctionType"||ye.type==="TSDeclareMethod"}function he(ye,S){if((S.parser==="typescript"||S.parser==="flow"||S.parser==="acorn"||S.parser==="espree"||S.parser==="meriyah"||S.parser==="__babel_estree")&&ye.type==="MethodDefinition"&&ye.value&&ye.value.type==="FunctionExpression"&&g(ye.value).length===0&&!ye.value.returnType&&!h(ye.value.typeParameters)&&ye.value.body)return[...ye.decorators||[],ye.key,ye.value.body]}function Be(ye){return d(ye)&&ye.value[0]==="*"&&/@type\b/.test(ye.value)}function Se(ye){let S=ye.getValue(),G=ye.getParentNode(),te=Ee=>w(f(Ee,x.Leading))||w(f(Ee,x.Trailing));return(S&&(D(S)||v(S)||B(G)&&te(S))||G&&(G.type==="JSXSpreadAttribute"||G.type==="JSXSpreadChild"||G.type==="UnionTypeAnnotation"||G.type==="TSUnionType"||(G.type==="ClassDeclaration"||G.type==="ClassExpression")&&G.superClass===S))&&(!T(ye)||G.type==="UnionTypeAnnotation"||G.type==="TSUnionType")}n.exports={handleOwnLineComment:C,handleEndOfLineComment:_,handleRemainingComment:b,isTypeCastComment:Be,getCommentChildNodes:he,willPrintOwnComments:Se}}}),Lt=ee({"src/language-js/needs-parens.js"(e,n){"use strict";re();var t=lt(),s=qn(),{getFunctionParameters:a,getLeftSidePathName:r,hasFlowShorthandAnnotationComment:u,hasNakedLeftSide:i,hasNode:o,isBitwiseOperator:c,startsWithNoLookaheadToken:y,shouldFlatten:h,getPrecedence:g,isCallExpression:p,isMemberExpression:D,isObjectProperty:v,isTSTypeExpression:w}=Ke();function T(f,x){let m=f.getParentNode();if(!m)return!1;let E=f.getName(),l=f.getNode();if(x.__isInHtmlInterpolation&&!x.bracketSpacing&&k(l)&&P(f))return!0;if(F(l))return!1;if(x.parser!=="flow"&&u(f.getValue()))return!0;if(l.type==="Identifier")return!!(l.extra&&l.extra.parenthesized&&/^PRETTIER_HTML_PLACEHOLDER_\d+_\d+_IN_JS$/.test(l.name)||E==="left"&&l.name==="async"&&m.type==="ForOfStatement"&&!m.await);switch(m.type){case"ParenthesizedExpression":return!1;case"ClassDeclaration":case"ClassExpression":{if(E==="superClass"&&(l.type==="ArrowFunctionExpression"||l.type==="AssignmentExpression"||l.type==="AwaitExpression"||l.type==="BinaryExpression"||l.type==="ConditionalExpression"||l.type==="LogicalExpression"||l.type==="NewExpression"||l.type==="ObjectExpression"||l.type==="SequenceExpression"||l.type==="TaggedTemplateExpression"||l.type==="UnaryExpression"||l.type==="UpdateExpression"||l.type==="YieldExpression"||l.type==="TSNonNullExpression"))return!0;break}case"ExportDefaultDeclaration":return R(f,x)||l.type==="SequenceExpression";case"Decorator":{if(E==="expression"){let d=!1,C=!1,_=l;for(;_;)switch(_.type){case"MemberExpression":C=!0,_=_.object;break;case"CallExpression":if(C||d)return x.parser!=="typescript";d=!0,_=_.callee;break;case"Identifier":return!1;case"TaggedTemplateExpression":return x.parser!=="typescript";default:return!0}return!0}break}case"ExpressionStatement":{if(y(l,!0))return!0;break}case"ArrowFunctionExpression":{if(E==="body"&&l.type!=="SequenceExpression"&&y(l,!1))return!0;break}}switch(l.type){case"UpdateExpression":if(m.type==="UnaryExpression")return l.prefix&&(l.operator==="++"&&m.operator==="+"||l.operator==="--"&&m.operator==="-");case"UnaryExpression":switch(m.type){case"UnaryExpression":return l.operator===m.operator&&(l.operator==="+"||l.operator==="-");case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return E==="object";case"TaggedTemplateExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return E==="callee";case"BinaryExpression":return E==="left"&&m.operator==="**";case"TSNonNullExpression":return!0;default:return!1}case"BinaryExpression":{if(m.type==="UpdateExpression"||l.operator==="in"&&A(f))return!0;if(l.operator==="|>"&&l.extra&&l.extra.parenthesized){let d=f.getParentNode(1);if(d.type==="BinaryExpression"&&d.operator==="|>")return!0}}case"TSTypeAssertion":case"TSAsExpression":case"TSSatisfiesExpression":case"LogicalExpression":switch(m.type){case"TSSatisfiesExpression":case"TSAsExpression":return!w(l);case"ConditionalExpression":return w(l);case"CallExpression":case"NewExpression":case"OptionalCallExpression":return E==="callee";case"ClassExpression":case"ClassDeclaration":return E==="superClass";case"TSTypeAssertion":case"TaggedTemplateExpression":case"UnaryExpression":case"JSXSpreadAttribute":case"SpreadElement":case"SpreadProperty":case"BindExpression":case"AwaitExpression":case"TSNonNullExpression":case"UpdateExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return E==="object";case"AssignmentExpression":case"AssignmentPattern":return E==="left"&&(l.type==="TSTypeAssertion"||w(l));case"LogicalExpression":if(l.type==="LogicalExpression")return m.operator!==l.operator;case"BinaryExpression":{let{operator:d,type:C}=l;if(!d&&C!=="TSTypeAssertion")return!0;let _=g(d),b=m.operator,N=g(b);return N>_||E==="right"&&N===_||N===_&&!h(b,d)?!0:N<_&&d==="%"?b==="+"||b==="-":!!c(b)}default:return!1}case"SequenceExpression":switch(m.type){case"ReturnStatement":return!1;case"ForStatement":return!1;case"ExpressionStatement":return E!=="expression";case"ArrowFunctionExpression":return E!=="body";default:return!0}case"YieldExpression":if(m.type==="UnaryExpression"||m.type==="AwaitExpression"||w(m)||m.type==="TSNonNullExpression")return!0;case"AwaitExpression":switch(m.type){case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"SpreadElement":case"SpreadProperty":case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return E==="object";case"NewExpression":case"CallExpression":case"OptionalCallExpression":return E==="callee";case"ConditionalExpression":return E==="test";case"BinaryExpression":return!(!l.argument&&m.operator==="|>");default:return!1}case"TSConditionalType":if(E==="extendsType"&&m.type==="TSConditionalType")return!0;case"TSFunctionType":case"TSConstructorType":if(E==="extendsType"&&m.type==="TSConditionalType"){let d=(l.returnType||l.typeAnnotation).typeAnnotation;if(d.type==="TSInferType"&&d.typeParameter.constraint)return!0}if(E==="checkType"&&m.type==="TSConditionalType")return!0;case"TSUnionType":case"TSIntersectionType":if((m.type==="TSUnionType"||m.type==="TSIntersectionType")&&m.types.length>1&&(!l.types||l.types.length>1))return!0;case"TSInferType":if(l.type==="TSInferType"&&m.type==="TSRestType")return!1;case"TSTypeOperator":return m.type==="TSArrayType"||m.type==="TSOptionalType"||m.type==="TSRestType"||E==="objectType"&&m.type==="TSIndexedAccessType"||m.type==="TSTypeOperator"||m.type==="TSTypeAnnotation"&&f.getParentNode(1).type.startsWith("TSJSDoc");case"ArrayTypeAnnotation":return m.type==="NullableTypeAnnotation";case"IntersectionTypeAnnotation":case"UnionTypeAnnotation":return m.type==="ArrayTypeAnnotation"||m.type==="NullableTypeAnnotation"||m.type==="IntersectionTypeAnnotation"||m.type==="UnionTypeAnnotation"||E==="objectType"&&(m.type==="IndexedAccessType"||m.type==="OptionalIndexedAccessType");case"NullableTypeAnnotation":return m.type==="ArrayTypeAnnotation"||E==="objectType"&&(m.type==="IndexedAccessType"||m.type==="OptionalIndexedAccessType");case"FunctionTypeAnnotation":{let d=m.type==="NullableTypeAnnotation"?f.getParentNode(1):m;return d.type==="UnionTypeAnnotation"||d.type==="IntersectionTypeAnnotation"||d.type==="ArrayTypeAnnotation"||E==="objectType"&&(d.type==="IndexedAccessType"||d.type==="OptionalIndexedAccessType")||d.type==="NullableTypeAnnotation"||m.type==="FunctionTypeParam"&&m.name===null&&a(l).some(C=>C.typeAnnotation&&C.typeAnnotation.type==="NullableTypeAnnotation")}case"OptionalIndexedAccessType":return E==="objectType"&&m.type==="IndexedAccessType";case"TypeofTypeAnnotation":return E==="objectType"&&(m.type==="IndexedAccessType"||m.type==="OptionalIndexedAccessType");case"StringLiteral":case"NumericLiteral":case"Literal":if(typeof l.value=="string"&&m.type==="ExpressionStatement"&&!m.directive){let d=f.getParentNode(1);return d.type==="Program"||d.type==="BlockStatement"}return E==="object"&&m.type==="MemberExpression"&&typeof l.value=="number";case"AssignmentExpression":{let d=f.getParentNode(1);return E==="body"&&m.type==="ArrowFunctionExpression"?!0:E==="key"&&(m.type==="ClassProperty"||m.type==="PropertyDefinition")&&m.computed||(E==="init"||E==="update")&&m.type==="ForStatement"?!1:m.type==="ExpressionStatement"?l.left.type==="ObjectPattern":!(E==="key"&&m.type==="TSPropertySignature"||m.type==="AssignmentExpression"||m.type==="SequenceExpression"&&d&&d.type==="ForStatement"&&(d.init===m||d.update===m)||E==="value"&&m.type==="Property"&&d&&d.type==="ObjectPattern"&&d.properties.includes(m)||m.type==="NGChainedExpression")}case"ConditionalExpression":switch(m.type){case"TaggedTemplateExpression":case"UnaryExpression":case"SpreadElement":case"SpreadProperty":case"BinaryExpression":case"LogicalExpression":case"NGPipeExpression":case"ExportDefaultDeclaration":case"AwaitExpression":case"JSXSpreadAttribute":case"TSTypeAssertion":case"TypeCastExpression":case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return E==="callee";case"ConditionalExpression":return E==="test";case"MemberExpression":case"OptionalMemberExpression":return E==="object";default:return!1}case"FunctionExpression":switch(m.type){case"NewExpression":case"CallExpression":case"OptionalCallExpression":return E==="callee";case"TaggedTemplateExpression":return!0;default:return!1}case"ArrowFunctionExpression":switch(m.type){case"BinaryExpression":return m.operator!=="|>"||l.extra&&l.extra.parenthesized;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return E==="callee";case"MemberExpression":case"OptionalMemberExpression":return E==="object";case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"BindExpression":case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"AwaitExpression":case"TSTypeAssertion":return!0;case"ConditionalExpression":return E==="test";default:return!1}case"ClassExpression":if(s(l.decorators))return!0;switch(m.type){case"NewExpression":return E==="callee";default:return!1}case"OptionalMemberExpression":case"OptionalCallExpression":{let d=f.getParentNode(1);if(E==="object"&&m.type==="MemberExpression"||E==="callee"&&(m.type==="CallExpression"||m.type==="NewExpression")||m.type==="TSNonNullExpression"&&d.type==="MemberExpression"&&d.object===m)return!0}case"CallExpression":case"MemberExpression":case"TaggedTemplateExpression":case"TSNonNullExpression":if(E==="callee"&&(m.type==="BindExpression"||m.type==="NewExpression")){let d=l;for(;d;)switch(d.type){case"CallExpression":case"OptionalCallExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":case"BindExpression":d=d.object;break;case"TaggedTemplateExpression":d=d.tag;break;case"TSNonNullExpression":d=d.expression;break;default:return!1}}return!1;case"BindExpression":return E==="callee"&&(m.type==="BindExpression"||m.type==="NewExpression")||E==="object"&&D(m);case"NGPipeExpression":return!(m.type==="NGRoot"||m.type==="NGMicrosyntaxExpression"||m.type==="ObjectProperty"&&!(l.extra&&l.extra.parenthesized)||m.type==="ArrayExpression"||p(m)&&m.arguments[E]===l||E==="right"&&m.type==="NGPipeExpression"||E==="property"&&m.type==="MemberExpression"||m.type==="AssignmentExpression");case"JSXFragment":case"JSXElement":return E==="callee"||E==="left"&&m.type==="BinaryExpression"&&m.operator==="<"||m.type!=="ArrayExpression"&&m.type!=="ArrowFunctionExpression"&&m.type!=="AssignmentExpression"&&m.type!=="AssignmentPattern"&&m.type!=="BinaryExpression"&&m.type!=="NewExpression"&&m.type!=="ConditionalExpression"&&m.type!=="ExpressionStatement"&&m.type!=="JsExpressionRoot"&&m.type!=="JSXAttribute"&&m.type!=="JSXElement"&&m.type!=="JSXExpressionContainer"&&m.type!=="JSXFragment"&&m.type!=="LogicalExpression"&&!p(m)&&!v(m)&&m.type!=="ReturnStatement"&&m.type!=="ThrowStatement"&&m.type!=="TypeCastExpression"&&m.type!=="VariableDeclarator"&&m.type!=="YieldExpression";case"TypeAnnotation":return E==="returnType"&&m.type==="ArrowFunctionExpression"&&B(l)}return!1}function F(f){return f.type==="BlockStatement"||f.type==="BreakStatement"||f.type==="ClassBody"||f.type==="ClassDeclaration"||f.type==="ClassMethod"||f.type==="ClassProperty"||f.type==="PropertyDefinition"||f.type==="ClassPrivateProperty"||f.type==="ContinueStatement"||f.type==="DebuggerStatement"||f.type==="DeclareClass"||f.type==="DeclareExportAllDeclaration"||f.type==="DeclareExportDeclaration"||f.type==="DeclareFunction"||f.type==="DeclareInterface"||f.type==="DeclareModule"||f.type==="DeclareModuleExports"||f.type==="DeclareVariable"||f.type==="DoWhileStatement"||f.type==="EnumDeclaration"||f.type==="ExportAllDeclaration"||f.type==="ExportDefaultDeclaration"||f.type==="ExportNamedDeclaration"||f.type==="ExpressionStatement"||f.type==="ForInStatement"||f.type==="ForOfStatement"||f.type==="ForStatement"||f.type==="FunctionDeclaration"||f.type==="IfStatement"||f.type==="ImportDeclaration"||f.type==="InterfaceDeclaration"||f.type==="LabeledStatement"||f.type==="MethodDefinition"||f.type==="ReturnStatement"||f.type==="SwitchStatement"||f.type==="ThrowStatement"||f.type==="TryStatement"||f.type==="TSDeclareFunction"||f.type==="TSEnumDeclaration"||f.type==="TSImportEqualsDeclaration"||f.type==="TSInterfaceDeclaration"||f.type==="TSModuleDeclaration"||f.type==="TSNamespaceExportDeclaration"||f.type==="TypeAlias"||f.type==="VariableDeclaration"||f.type==="WhileStatement"||f.type==="WithStatement"}function A(f){let x=0,m=f.getValue();for(;m;){let E=f.getParentNode(x++);if(E&&E.type==="ForStatement"&&E.init===m)return!0;m=E}return!1}function B(f){return o(f,x=>x.type==="ObjectTypeAnnotation"&&o(x,m=>m.type==="FunctionTypeAnnotation"||void 0)||void 0)}function k(f){switch(f.type){case"ObjectExpression":return!0;default:return!1}}function P(f){let x=f.getValue(),m=f.getParentNode(),E=f.getName();switch(m.type){case"NGPipeExpression":if(typeof E=="number"&&m.arguments[E]===x&&m.arguments.length-1===E)return f.callParent(P);break;case"ObjectProperty":if(E==="value"){let l=f.getParentNode(1);return t(l.properties)===m}break;case"BinaryExpression":case"LogicalExpression":if(E==="right")return f.callParent(P);break;case"ConditionalExpression":if(E==="alternate")return f.callParent(P);break;case"UnaryExpression":if(m.prefix)return f.callParent(P);break}return!1}function R(f,x){let m=f.getValue(),E=f.getParentNode();return m.type==="FunctionExpression"||m.type==="ClassExpression"?E.type==="ExportDefaultDeclaration"||!T(f,x):!i(m)||E.type!=="ExportDefaultDeclaration"&&T(f,x)?!1:f.call(l=>R(l,x),...r(f,m))}n.exports=T}}),Ja=ee({"src/language-js/print-preprocess.js"(e,n){"use strict";re();function t(s,a){switch(a.parser){case"json":case"json5":case"json-stringify":case"__js_expression":case"__vue_expression":case"__vue_ts_expression":return Object.assign(Object.assign({},s),{},{type:a.parser.startsWith("__")?"JsExpressionRoot":"JsonRoot",node:s,comments:[],rootMarker:a.rootMarker});default:return s}}n.exports=t}}),wm=ee({"src/language-js/print/html-binding.js"(e,n){"use strict";re();var{builders:{join:t,line:s,group:a,softline:r,indent:u}}=qe();function i(c,y,h){let g=c.getValue();if(y.__onHtmlBindingRoot&&c.getName()===null&&y.__onHtmlBindingRoot(g,y),g.type==="File"){if(y.__isVueForBindingLeft)return c.call(p=>{let D=t([",",s],p.map(h,"params")),{params:v}=p.getValue();return v.length===1?D:["(",u([r,a(D)]),r,")"]},"program","body",0);if(y.__isVueBindings)return c.call(p=>t([",",s],p.map(h,"params")),"program","body",0)}}function o(c){switch(c.type){case"MemberExpression":switch(c.property.type){case"Identifier":case"NumericLiteral":case"StringLiteral":return o(c.object)}return!1;case"Identifier":return!0;default:return!1}}n.exports={isVueEventBindingExpression:o,printHtmlBinding:i}}}),Hn=ee({"src/language-js/print/binaryish.js"(e,n){"use strict";re();var{printComments:t}=et(),{getLast:s}=Ge(),{builders:{join:a,line:r,softline:u,group:i,indent:o,align:c,indentIfBreak:y},utils:{cleanDoc:h,getDocParts:g,isConcat:p}}=qe(),{hasLeadingOwnLineComment:D,isBinaryish:v,isJsxNode:w,shouldFlatten:T,hasComment:F,CommentCheckFlags:A,isCallExpression:B,isMemberExpression:k,isObjectProperty:P,isEnabledHackPipeline:R}=Ke(),f=0;function x(l,d,C){let _=l.getValue(),b=l.getParentNode(),N=l.getParentNode(1),I=_!==b.body&&(b.type==="IfStatement"||b.type==="WhileStatement"||b.type==="SwitchStatement"||b.type==="DoWhileStatement"),$=R(d)&&_.operator==="|>",M=m(l,C,d,!1,I);if(I)return M;if($)return i(M);if(B(b)&&b.callee===_||b.type==="UnaryExpression"||k(b)&&!b.computed)return i([o([u,...M]),u]);let q=b.type==="ReturnStatement"||b.type==="ThrowStatement"||b.type==="JSXExpressionContainer"&&N.type==="JSXAttribute"||_.operator!=="|"&&b.type==="JsExpressionRoot"||_.type!=="NGPipeExpression"&&(b.type==="NGRoot"&&d.parser==="__ng_binding"||b.type==="NGMicrosyntaxExpression"&&N.type==="NGMicrosyntax"&&N.body.length===1)||_===b.body&&b.type==="ArrowFunctionExpression"||_!==b.body&&b.type==="ForStatement"||b.type==="ConditionalExpression"&&N.type!=="ReturnStatement"&&N.type!=="ThrowStatement"&&!B(N)||b.type==="TemplateLiteral",J=b.type==="AssignmentExpression"||b.type==="VariableDeclarator"||b.type==="ClassProperty"||b.type==="PropertyDefinition"||b.type==="TSAbstractPropertyDefinition"||b.type==="ClassPrivateProperty"||P(b),L=v(_.left)&&T(_.operator,_.left.operator);if(q||E(_)&&!L||!E(_)&&J)return i(M);if(M.length===0)return"";let Y=w(_.right),V=M.findIndex(W=>typeof W!="string"&&!Array.isArray(W)&&W.type==="group"),O=M.slice(0,V===-1?1:V+1),K=M.slice(O.length,Y?-1:void 0),se=Symbol("logicalChain-"+ ++f),Q=i([...O,o(K)],{id:se});if(!Y)return Q;let le=s(M);return i([Q,y(le,{groupId:se})])}function m(l,d,C,_,b){let N=l.getValue();if(!v(N))return[i(d())];let I=[];T(N.operator,N.left.operator)?I=l.call(K=>m(K,d,C,!0,b),"left"):I.push(i(d("left")));let $=E(N),M=(N.operator==="|>"||N.type==="NGPipeExpression"||N.operator==="|"&&C.parser==="__vue_expression")&&!D(C.originalText,N.right),q=N.type==="NGPipeExpression"?"|":N.operator,J=N.type==="NGPipeExpression"&&N.arguments.length>0?i(o([r,": ",a([r,": "],l.map(d,"arguments").map(K=>c(2,i(K))))])):"",L;if($)L=[q," ",d("right"),J];else{let se=R(C)&&q==="|>"?l.call(Q=>m(Q,d,C,!0,b),"right"):d("right");L=[M?r:"",q,M?" ":r,se,J]}let Y=l.getParentNode(),V=F(N.left,A.Trailing|A.Line),O=V||!(b&&N.type==="LogicalExpression")&&Y.type!==N.type&&N.left.type!==N.type&&N.right.type!==N.type;if(I.push(M?"":" ",O?i(L,{shouldBreak:V}):L),_&&F(N)){let K=h(t(l,I,C));return p(K)||K.type==="fill"?g(K):[K]}return I}function E(l){return l.type!=="LogicalExpression"?!1:!!(l.right.type==="ObjectExpression"&&l.right.properties.length>0||l.right.type==="ArrayExpression"&&l.right.elements.length>0||w(l.right))}n.exports={printBinaryishExpression:x,shouldInlineLogicalExpression:E}}}),_m=ee({"src/language-js/print/angular.js"(e,n){"use strict";re();var{builders:{join:t,line:s,group:a}}=qe(),{hasNode:r,hasComment:u,getComments:i}=Ke(),{printBinaryishExpression:o}=Hn();function c(g,p,D){let v=g.getValue();if(!!v.type.startsWith("NG"))switch(v.type){case"NGRoot":return[D("node"),u(v.node)?" //"+i(v.node)[0].value.trimEnd():""];case"NGPipeExpression":return o(g,p,D);case"NGChainedExpression":return a(t([";",s],g.map(w=>h(w)?D():["(",D(),")"],"expressions")));case"NGEmptyExpression":return"";case"NGQuotedExpression":return[v.prefix,": ",v.value.trim()];case"NGMicrosyntax":return g.map((w,T)=>[T===0?"":y(w.getValue(),T,v)?" ":[";",s],D()],"body");case"NGMicrosyntaxKey":return/^[$_a-z][\w$]*(?:-[$_a-z][\w$])*$/i.test(v.name)?v.name:JSON.stringify(v.name);case"NGMicrosyntaxExpression":return[D("expression"),v.alias===null?"":[" as ",D("alias")]];case"NGMicrosyntaxKeyedExpression":{let w=g.getName(),T=g.getParentNode(),F=y(v,w,T)||(w===1&&(v.key.name==="then"||v.key.name==="else")||w===2&&v.key.name==="else"&&T.body[w-1].type==="NGMicrosyntaxKeyedExpression"&&T.body[w-1].key.name==="then")&&T.body[0].type==="NGMicrosyntaxExpression";return[D("key"),F?" ":": ",D("expression")]}case"NGMicrosyntaxLet":return["let ",D("key"),v.value===null?"":[" = ",D("value")]];case"NGMicrosyntaxAs":return[D("key")," as ",D("alias")];default:throw new Error(`Unknown Angular node type: ${JSON.stringify(v.type)}.`)}}function y(g,p,D){return g.type==="NGMicrosyntaxKeyedExpression"&&g.key.name==="of"&&p===1&&D.body[0].type==="NGMicrosyntaxLet"&&D.body[0].value===null}function h(g){return r(g.getValue(),p=>{switch(p.type){case void 0:return!1;case"CallExpression":case"OptionalCallExpression":case"AssignmentExpression":return!0}})}n.exports={printAngular:c}}}),Pm=ee({"src/language-js/print/jsx.js"(e,n){"use strict";re();var{printComments:t,printDanglingComments:s}=et(),{builders:{line:a,hardline:r,softline:u,group:i,indent:o,conditionalGroup:c,fill:y,ifBreak:h,lineSuffixBoundary:g,join:p},utils:{willBreak:D}}=qe(),{getLast:v,getPreferredQuote:w}=Ge(),{isJsxNode:T,rawText:F,isLiteral:A,isCallExpression:B,isStringLiteral:k,isBinaryish:P,hasComment:R,CommentCheckFlags:f,hasNodeIgnoreComment:x}=Ke(),m=Lt(),{willPrintOwnComments:E}=Ua(),l=H=>H===""||H===a||H===r||H===u;function d(H,Z,ne){let fe=H.getValue();if(fe.type==="JSXElement"&&oe(fe))return[ne("openingElement"),ne("closingElement")];let ge=fe.type==="JSXElement"?ne("openingElement"):ne("openingFragment"),Ce=fe.type==="JSXElement"?ne("closingElement"):ne("closingFragment");if(fe.children.length===1&&fe.children[0].type==="JSXExpressionContainer"&&(fe.children[0].expression.type==="TemplateLiteral"||fe.children[0].expression.type==="TaggedTemplateExpression"))return[ge,...H.map(ne,"children"),Ce];fe.children=fe.children.map(S=>Ae(S)?{type:"JSXText",value:" ",raw:" "}:S);let _e=fe.children.some(T),Oe=fe.children.filter(S=>S.type==="JSXExpressionContainer").length>1,pe=fe.type==="JSXElement"&&fe.openingElement.attributes.length>1,ie=D(ge)||_e||pe||Oe,ve=H.getParentNode().rootMarker==="mdx",ce=Z.singleQuote?"{' '}":'{" "}',U=ve?" ":h([ce,u]," "),de=fe.openingElement&&fe.openingElement.name&&fe.openingElement.name.name==="fbt",De=C(H,Z,ne,U,de),he=fe.children.some(S=>ae(S));for(let S=De.length-2;S>=0;S--){let G=De[S]===""&&De[S+1]==="",te=De[S]===r&&De[S+1]===""&&De[S+2]===r,Ee=(De[S]===u||De[S]===r)&&De[S+1]===""&&De[S+2]===U,Re=De[S]===U&&De[S+1]===""&&(De[S+2]===u||De[S+2]===r),Te=De[S]===U&&De[S+1]===""&&De[S+2]===U,Pe=De[S]===u&&De[S+1]===""&&De[S+2]===r||De[S]===r&&De[S+1]===""&&De[S+2]===u;te&&he||G||Ee||Te||Pe?De.splice(S,2):Re&&De.splice(S+1,2)}for(;De.length>0&&l(v(De));)De.pop();for(;De.length>1&&l(De[0])&&l(De[1]);)De.shift(),De.shift();let Be=[];for(let[S,G]of De.entries()){if(G===U){if(S===1&&De[S-1]===""){if(De.length===2){Be.push(ce);continue}Be.push([ce,r]);continue}else if(S===De.length-1){Be.push(ce);continue}else if(De[S-1]===""&&De[S-2]===r){Be.push(ce);continue}}Be.push(G),D(G)&&(ie=!0)}let Se=he?y(Be):i(Be,{shouldBreak:!0});if(ve)return Se;let ye=i([ge,o([r,Se]),r,Ce]);return ie?ye:c([i([ge,...De,Ce]),ye])}function C(H,Z,ne,fe,ge){let Ce=[];return H.each((_e,Oe,pe)=>{let ie=_e.getValue();if(A(ie)){let ve=F(ie);if(ae(ie)){let ce=ve.split(le);if(ce[0]===""){if(Ce.push(""),ce.shift(),/\n/.test(ce[0])){let de=pe[Oe+1];Ce.push(b(ge,ce[1],ie,de))}else Ce.push(fe);ce.shift()}let U;if(v(ce)===""&&(ce.pop(),U=ce.pop()),ce.length===0)return;for(let[de,De]of ce.entries())de%2===1?Ce.push(a):Ce.push(De);if(U!==void 0)if(/\n/.test(U)){let de=pe[Oe+1];Ce.push(b(ge,v(Ce),ie,de))}else Ce.push(fe);else{let de=pe[Oe+1];Ce.push(_(ge,v(Ce),ie,de))}}else/\n/.test(ve)?ve.match(/\n/g).length>1&&Ce.push("",r):Ce.push("",fe)}else{let ve=ne();Ce.push(ve);let ce=pe[Oe+1];if(ce&&ae(ce)){let de=X(F(ce)).split(le)[0];Ce.push(_(ge,de,ie,ce))}else Ce.push(r)}},"children"),Ce}function _(H,Z,ne,fe){return H?"":ne.type==="JSXElement"&&!ne.closingElement||fe&&fe.type==="JSXElement"&&!fe.closingElement?Z.length===1?u:r:u}function b(H,Z,ne,fe){return H?r:Z.length===1?ne.type==="JSXElement"&&!ne.closingElement||fe&&fe.type==="JSXElement"&&!fe.closingElement?r:u:r}function N(H,Z,ne){let fe=H.getParentNode();if(!fe||{ArrayExpression:!0,JSXAttribute:!0,JSXElement:!0,JSXExpressionContainer:!0,JSXFragment:!0,ExpressionStatement:!0,CallExpression:!0,OptionalCallExpression:!0,ConditionalExpression:!0,JsExpressionRoot:!0}[fe.type])return Z;let Ce=H.match(void 0,Oe=>Oe.type==="ArrowFunctionExpression",B,Oe=>Oe.type==="JSXExpressionContainer"),_e=m(H,ne);return i([_e?"":h("("),o([u,Z]),u,_e?"":h(")")],{shouldBreak:Ce})}function I(H,Z,ne){let fe=H.getValue(),ge=[];if(ge.push(ne("name")),fe.value){let Ce;if(k(fe.value)){let Oe=F(fe.value).slice(1,-1).replace(/&apos;/g,"'").replace(/&quot;/g,'"'),{escaped:pe,quote:ie,regex:ve}=w(Oe,Z.jsxSingleQuote?"'":'"');Oe=Oe.replace(ve,pe),Ce=[ie,Oe,ie]}else Ce=ne("value");ge.push("=",Ce)}return ge}function $(H,Z,ne){let fe=H.getValue(),ge=(Ce,_e)=>Ce.type==="JSXEmptyExpression"||!R(Ce)&&(Ce.type==="ArrayExpression"||Ce.type==="ObjectExpression"||Ce.type==="ArrowFunctionExpression"||Ce.type==="AwaitExpression"&&(ge(Ce.argument,Ce)||Ce.argument.type==="JSXElement")||B(Ce)||Ce.type==="FunctionExpression"||Ce.type==="TemplateLiteral"||Ce.type==="TaggedTemplateExpression"||Ce.type==="DoExpression"||T(_e)&&(Ce.type==="ConditionalExpression"||P(Ce)));return ge(fe.expression,H.getParentNode(0))?i(["{",ne("expression"),g,"}"]):i(["{",o([u,ne("expression")]),u,g,"}"])}function M(H,Z,ne){let fe=H.getValue(),ge=fe.name&&R(fe.name)||fe.typeParameters&&R(fe.typeParameters);if(fe.selfClosing&&fe.attributes.length===0&&!ge)return["<",ne("name"),ne("typeParameters")," />"];if(fe.attributes&&fe.attributes.length===1&&fe.attributes[0].value&&k(fe.attributes[0].value)&&!fe.attributes[0].value.value.includes(`
`)&&!ge&&!R(fe.attributes[0]))return i(["<",ne("name"),ne("typeParameters")," ",...H.map(ne,"attributes"),fe.selfClosing?" />":">"]);let Ce=fe.attributes&&fe.attributes.some(Oe=>Oe.value&&k(Oe.value)&&Oe.value.value.includes(`
`)),_e=Z.singleAttributePerLine&&fe.attributes.length>1?r:a;return i(["<",ne("name"),ne("typeParameters"),o(H.map(()=>[_e,ne()],"attributes")),...q(fe,Z,ge)],{shouldBreak:Ce})}function q(H,Z,ne){return H.selfClosing?[a,"/>"]:J(H,Z,ne)?[">"]:[u,">"]}function J(H,Z,ne){let fe=H.attributes.length>0&&R(v(H.attributes),f.Trailing);return H.attributes.length===0&&!ne||(Z.bracketSameLine||Z.jsxBracketSameLine)&&(!ne||H.attributes.length>0)&&!fe}function L(H,Z,ne){let fe=H.getValue(),ge=[];ge.push("</");let Ce=ne("name");return R(fe.name,f.Leading|f.Line)?ge.push(o([r,Ce]),r):R(fe.name,f.Leading|f.Block)?ge.push(" ",Ce):ge.push(Ce),ge.push(">"),ge}function Y(H,Z){let ne=H.getValue(),fe=R(ne),ge=R(ne,f.Line),Ce=ne.type==="JSXOpeningFragment";return[Ce?"<":"</",o([ge?r:fe&&!Ce?" ":"",s(H,Z,!0)]),ge?r:"",">"]}function V(H,Z,ne){let fe=t(H,d(H,Z,ne),Z);return N(H,fe,Z)}function O(H,Z){let ne=H.getValue(),fe=R(ne,f.Line);return[s(H,Z,!fe),fe?r:""]}function K(H,Z,ne){let fe=H.getValue();return["{",H.call(ge=>{let Ce=["...",ne()],_e=ge.getValue();return!R(_e)||!E(ge)?Ce:[o([u,t(ge,Ce,Z)]),u]},fe.type==="JSXSpreadAttribute"?"argument":"expression"),"}"]}function se(H,Z,ne){let fe=H.getValue();if(!!fe.type.startsWith("JSX"))switch(fe.type){case"JSXAttribute":return I(H,Z,ne);case"JSXIdentifier":return String(fe.name);case"JSXNamespacedName":return p(":",[ne("namespace"),ne("name")]);case"JSXMemberExpression":return p(".",[ne("object"),ne("property")]);case"JSXSpreadAttribute":return K(H,Z,ne);case"JSXSpreadChild":return K(H,Z,ne);case"JSXExpressionContainer":return $(H,Z,ne);case"JSXFragment":case"JSXElement":return V(H,Z,ne);case"JSXOpeningElement":return M(H,Z,ne);case"JSXClosingElement":return L(H,Z,ne);case"JSXOpeningFragment":case"JSXClosingFragment":return Y(H,Z);case"JSXEmptyExpression":return O(H,Z);case"JSXText":throw new Error("JSXTest should be handled by JSXElement");default:throw new Error(`Unknown JSX node type: ${JSON.stringify(fe.type)}.`)}}var Q=` 
\r	`,le=new RegExp("(["+Q+"]+)"),W=new RegExp("[^"+Q+"]"),X=H=>H.replace(new RegExp("(?:^"+le.source+"|"+le.source+"$)"),"");function oe(H){if(H.children.length===0)return!0;if(H.children.length>1)return!1;let Z=H.children[0];return A(Z)&&!ae(Z)}function ae(H){return A(H)&&(W.test(F(H))||!/\n/.test(F(H)))}function Ae(H){return H.type==="JSXExpressionContainer"&&A(H.expression)&&H.expression.value===" "&&!R(H.expression)}function z(H){let Z=H.getValue(),ne=H.getParentNode();if(!ne||!Z||!T(Z)||!T(ne))return!1;let fe=ne.children.indexOf(Z),ge=null;for(let Ce=fe;Ce>0;Ce--){let _e=ne.children[Ce-1];if(!(_e.type==="JSXText"&&!ae(_e))){ge=_e;break}}return ge&&ge.type==="JSXExpressionContainer"&&ge.expression.type==="JSXEmptyExpression"&&x(ge.expression)}n.exports={hasJsxIgnoreComment:z,printJsx:se}}}),Dt=ee({"src/language-js/print/misc.js"(e,n){"use strict";re();var{isNonEmptyArray:t}=Ge(),{builders:{indent:s,join:a,line:r}}=qe(),{isFlowAnnotationComment:u}=Ke();function i(v){let w=v.getValue();return!w.optional||w.type==="Identifier"&&w===v.getParentNode().key?"":w.type==="OptionalCallExpression"||w.type==="OptionalMemberExpression"&&w.computed?"?.":"?"}function o(v){return v.getValue().definite||v.match(void 0,(w,T)=>T==="id"&&w.type==="VariableDeclarator"&&w.definite)?"!":""}function c(v,w,T){let F=v.getValue();return F.typeArguments?T("typeArguments"):F.typeParameters?T("typeParameters"):""}function y(v,w,T){let F=v.getValue();if(!F.typeAnnotation)return"";let A=v.getParentNode(),B=A.type==="DeclareFunction"&&A.id===F;return u(w.originalText,F.typeAnnotation)?[" /*: ",T("typeAnnotation")," */"]:[B?"":": ",T("typeAnnotation")]}function h(v,w,T){return["::",T("callee")]}function g(v,w,T){let F=v.getValue();return t(F.modifiers)?[a(" ",v.map(T,"modifiers"))," "]:""}function p(v,w,T){return v.type==="EmptyStatement"?";":v.type==="BlockStatement"||T?[" ",w]:s([r,w])}function D(v,w,T){return["...",T("argument"),y(v,w,T)]}n.exports={printOptionalToken:i,printDefiniteToken:o,printFunctionTypeParameters:c,printBindExpressionCallee:h,printTypeScriptModifiers:g,printTypeAnnotation:y,printRestSpread:D,adjustClause:p}}}),Kt=ee({"src/language-js/print/array.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{builders:{line:s,softline:a,hardline:r,group:u,indent:i,ifBreak:o,fill:c}}=qe(),{getLast:y,hasNewline:h}=Ge(),{shouldPrintComma:g,hasComment:p,CommentCheckFlags:D,isNextLineEmpty:v,isNumericLiteral:w,isSignedNumericLiteral:T}=Ke(),{locStart:F}=it(),{printOptionalToken:A,printTypeAnnotation:B}=Dt();function k(x,m,E){let l=x.getValue(),d=[],C=l.type==="TupleExpression"?"#[":"[",_="]";if(l.elements.length===0)p(l,D.Dangling)?d.push(u([C,t(x,m),a,_])):d.push(C,_);else{let b=y(l.elements),N=!(b&&b.type==="RestElement"),I=b===null,$=Symbol("array"),M=!m.__inJestEach&&l.elements.length>1&&l.elements.every((L,Y,V)=>{let O=L&&L.type;if(O!=="ArrayExpression"&&O!=="ObjectExpression")return!1;let K=V[Y+1];if(K&&O!==K.type)return!1;let se=O==="ArrayExpression"?"elements":"properties";return L[se]&&L[se].length>1}),q=P(l,m),J=N?I?",":g(m)?q?o(",","",{groupId:$}):o(","):"":"";d.push(u([C,i([a,q?f(x,m,E,J):[R(x,m,"elements",E),J],t(x,m,!0)]),a,_],{shouldBreak:M,id:$}))}return d.push(A(x),B(x,m,E)),d}function P(x,m){return x.elements.length>1&&x.elements.every(E=>E&&(w(E)||T(E)&&!p(E.argument))&&!p(E,D.Trailing|D.Line,l=>!h(m.originalText,F(l),{backwards:!0})))}function R(x,m,E,l){let d=[],C=[];return x.each(_=>{d.push(C,u(l())),C=[",",s],_.getValue()&&v(_.getValue(),m)&&C.push(a)},E),d}function f(x,m,E,l){let d=[];return x.each((C,_,b)=>{let N=_===b.length-1;d.push([E(),N?l:","]),N||d.push(v(C.getValue(),m)?[r,r]:p(b[_+1],D.Leading|D.Line)?r:s)},"elements"),c(d)}n.exports={printArray:k,printArrayItems:R,isConciselyPrintedArray:P}}}),za=ee({"src/language-js/print/call-arguments.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{getLast:s,getPenultimate:a}=Ge(),{getFunctionParameters:r,hasComment:u,CommentCheckFlags:i,isFunctionCompositionArgs:o,isJsxNode:c,isLongCurriedCallExpression:y,shouldPrintComma:h,getCallArguments:g,iterateCallArgumentsPath:p,isNextLineEmpty:D,isCallExpression:v,isStringLiteral:w,isObjectProperty:T,isTSTypeExpression:F}=Ke(),{builders:{line:A,hardline:B,softline:k,group:P,indent:R,conditionalGroup:f,ifBreak:x,breakParent:m},utils:{willBreak:E}}=qe(),{ArgExpansionBailout:l}=zt(),{isConciselyPrintedArray:d}=Kt();function C(q,J,L){let Y=q.getValue(),V=Y.type==="ImportExpression",O=g(Y);if(O.length===0)return["(",t(q,J,!0),")"];if(I(O))return["(",L(["arguments",0]),", ",L(["arguments",1]),")"];let K=!1,se=!1,Q=O.length-1,le=[];p(q,(z,H)=>{let Z=z.getNode(),ne=[L()];H===Q||(D(Z,J)?(H===0&&(se=!0),K=!0,ne.push(",",B,B)):ne.push(",",A)),le.push(ne)});let W=!(V||Y.callee&&Y.callee.type==="Import")&&h(J,"all")?",":"";function X(){return P(["(",R([A,...le]),W,A,")"],{shouldBreak:!0})}if(K||q.getParentNode().type!=="Decorator"&&o(O))return X();let oe=N(O),ae=b(O,J);if(oe||ae){if(oe?le.slice(1).some(E):le.slice(0,-1).some(E))return X();let z=[];try{q.try(()=>{p(q,(H,Z)=>{oe&&Z===0&&(z=[[L([],{expandFirstArg:!0}),le.length>1?",":"",se?B:A,se?B:""],...le.slice(1)]),ae&&Z===Q&&(z=[...le.slice(0,-1),L([],{expandLastArg:!0})])})})}catch(H){if(H instanceof l)return X();throw H}return[le.some(E)?m:"",f([["(",...z,")"],oe?["(",P(z[0],{shouldBreak:!0}),...z.slice(1),")"]:["(",...le.slice(0,-1),P(s(z),{shouldBreak:!0}),")"],X()])]}let Ae=["(",R([k,...le]),x(W),k,")"];return y(q)?Ae:P(Ae,{shouldBreak:le.some(E)||K})}function _(q){let J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return q.type==="ObjectExpression"&&(q.properties.length>0||u(q))||q.type==="ArrayExpression"&&(q.elements.length>0||u(q))||q.type==="TSTypeAssertion"&&_(q.expression)||F(q)&&_(q.expression)||q.type==="FunctionExpression"||q.type==="ArrowFunctionExpression"&&(!q.returnType||!q.returnType.typeAnnotation||q.returnType.typeAnnotation.type!=="TSTypeReference"||$(q.body))&&(q.body.type==="BlockStatement"||q.body.type==="ArrowFunctionExpression"&&_(q.body,!0)||q.body.type==="ObjectExpression"||q.body.type==="ArrayExpression"||!J&&(v(q.body)||q.body.type==="ConditionalExpression")||c(q.body))||q.type==="DoExpression"||q.type==="ModuleExpression"}function b(q,J){let L=s(q),Y=a(q);return!u(L,i.Leading)&&!u(L,i.Trailing)&&_(L)&&(!Y||Y.type!==L.type)&&(q.length!==2||Y.type!=="ArrowFunctionExpression"||L.type!=="ArrayExpression")&&!(q.length>1&&L.type==="ArrayExpression"&&d(L,J))}function N(q){if(q.length!==2)return!1;let[J,L]=q;return J.type==="ModuleExpression"&&M(L)?!0:!u(J)&&(J.type==="FunctionExpression"||J.type==="ArrowFunctionExpression"&&J.body.type==="BlockStatement")&&L.type!=="FunctionExpression"&&L.type!=="ArrowFunctionExpression"&&L.type!=="ConditionalExpression"&&!_(L)}function I(q){return q.length===2&&q[0].type==="ArrowFunctionExpression"&&r(q[0]).length===0&&q[0].body.type==="BlockStatement"&&q[1].type==="ArrayExpression"&&!q.some(J=>u(J))}function $(q){return q.type==="BlockStatement"&&(q.body.some(J=>J.type!=="EmptyStatement")||u(q,i.Dangling))}function M(q){return q.type==="ObjectExpression"&&q.properties.length===1&&T(q.properties[0])&&q.properties[0].key.type==="Identifier"&&q.properties[0].key.name==="type"&&w(q.properties[0].value)&&q.properties[0].value.value==="module"}n.exports=C}}),Xa=ee({"src/language-js/print/member.js"(e,n){"use strict";re();var{builders:{softline:t,group:s,indent:a,label:r}}=qe(),{isNumericLiteral:u,isMemberExpression:i,isCallExpression:o}=Ke(),{printOptionalToken:c}=Dt();function y(g,p,D){let v=g.getValue(),w=g.getParentNode(),T,F=0;do T=g.getParentNode(F),F++;while(T&&(i(T)||T.type==="TSNonNullExpression"));let A=D("object"),B=h(g,p,D),k=T&&(T.type==="NewExpression"||T.type==="BindExpression"||T.type==="AssignmentExpression"&&T.left.type!=="Identifier")||v.computed||v.object.type==="Identifier"&&v.property.type==="Identifier"&&!i(w)||(w.type==="AssignmentExpression"||w.type==="VariableDeclarator")&&(o(v.object)&&v.object.arguments.length>0||v.object.type==="TSNonNullExpression"&&o(v.object.expression)&&v.object.expression.arguments.length>0||A.label==="member-chain");return r(A.label==="member-chain"?"member-chain":"member",[A,k?B:s(a([t,B]))])}function h(g,p,D){let v=D("property"),w=g.getValue(),T=c(g);return w.computed?!w.property||u(w.property)?[T,"[",v,"]"]:s([T,"[",a([t,v]),t,"]"]):[T,".",v]}n.exports={printMemberExpression:y,printMemberLookup:h}}}),km=ee({"src/language-js/print/member-chain.js"(e,n){"use strict";re();var{printComments:t}=et(),{getLast:s,isNextLineEmptyAfterIndex:a,getNextNonSpaceNonCommentCharacterIndex:r}=Ge(),u=Lt(),{isCallExpression:i,isMemberExpression:o,isFunctionOrArrowExpression:c,isLongCurriedCallExpression:y,isMemberish:h,isNumericLiteral:g,isSimpleCallArgument:p,hasComment:D,CommentCheckFlags:v,isNextLineEmpty:w}=Ke(),{locEnd:T}=it(),{builders:{join:F,hardline:A,group:B,indent:k,conditionalGroup:P,breakParent:R,label:f},utils:{willBreak:x}}=qe(),m=za(),{printMemberLookup:E}=Xa(),{printOptionalToken:l,printFunctionTypeParameters:d,printBindExpressionCallee:C}=Dt();function _(b,N,I){let $=b.getParentNode(),M=!$||$.type==="ExpressionStatement",q=[];function J(ie){let{originalText:ve}=N,ce=r(ve,ie,T);return ve.charAt(ce)===")"?ce!==!1&&a(ve,ce+1):w(ie,N)}function L(ie){let ve=ie.getValue();i(ve)&&(h(ve.callee)||i(ve.callee))?(q.unshift({node:ve,printed:[t(ie,[l(ie),d(ie,N,I),m(ie,N,I)],N),J(ve)?A:""]}),ie.call(ce=>L(ce),"callee")):h(ve)?(q.unshift({node:ve,needsParens:u(ie,N),printed:t(ie,o(ve)?E(ie,N,I):C(ie,N,I),N)}),ie.call(ce=>L(ce),"object")):ve.type==="TSNonNullExpression"?(q.unshift({node:ve,printed:t(ie,"!",N)}),ie.call(ce=>L(ce),"expression")):q.unshift({node:ve,printed:I()})}let Y=b.getValue();q.unshift({node:Y,printed:[l(b),d(b,N,I),m(b,N,I)]}),Y.callee&&b.call(ie=>L(ie),"callee");let V=[],O=[q[0]],K=1;for(;K<q.length&&(q[K].node.type==="TSNonNullExpression"||i(q[K].node)||o(q[K].node)&&q[K].node.computed&&g(q[K].node.property));++K)O.push(q[K]);if(!i(q[0].node))for(;K+1<q.length&&(h(q[K].node)&&h(q[K+1].node));++K)O.push(q[K]);V.push(O),O=[];let se=!1;for(;K<q.length;++K){if(se&&h(q[K].node)){if(q[K].node.computed&&g(q[K].node.property)){O.push(q[K]);continue}V.push(O),O=[],se=!1}(i(q[K].node)||q[K].node.type==="ImportExpression")&&(se=!0),O.push(q[K]),D(q[K].node,v.Trailing)&&(V.push(O),O=[],se=!1)}O.length>0&&V.push(O);function Q(ie){return/^[A-Z]|^[$_]+$/.test(ie)}function le(ie){return ie.length<=N.tabWidth}function W(ie){let ve=ie[1].length>0&&ie[1][0].node.computed;if(ie[0].length===1){let U=ie[0][0].node;return U.type==="ThisExpression"||U.type==="Identifier"&&(Q(U.name)||M&&le(U.name)||ve)}let ce=s(ie[0]).node;return o(ce)&&ce.property.type==="Identifier"&&(Q(ce.property.name)||ve)}let X=V.length>=2&&!D(V[1][0].node)&&W(V);function oe(ie){let ve=ie.map(ce=>ce.printed);return ie.length>0&&s(ie).needsParens?["(",...ve,")"]:ve}function ae(ie){return ie.length===0?"":k(B([A,F(A,ie.map(oe))]))}let Ae=V.map(oe),z=Ae,H=X?3:2,Z=V.flat(),ne=Z.slice(1,-1).some(ie=>D(ie.node,v.Leading))||Z.slice(0,-1).some(ie=>D(ie.node,v.Trailing))||V[H]&&D(V[H][0].node,v.Leading);if(V.length<=H&&!ne)return y(b)?z:B(z);let fe=s(V[X?1:0]).node,ge=!i(fe)&&J(fe),Ce=[oe(V[0]),X?V.slice(1,2).map(oe):"",ge?A:"",ae(V.slice(X?2:1))],_e=q.map(ie=>{let{node:ve}=ie;return ve}).filter(i);function Oe(){let ie=s(s(V)).node,ve=s(Ae);return i(ie)&&x(ve)&&_e.slice(0,-1).some(ce=>ce.arguments.some(c))}let pe;return ne||_e.length>2&&_e.some(ie=>!ie.arguments.every(ve=>p(ve,0)))||Ae.slice(0,-1).some(x)||Oe()?pe=B(Ce):pe=[x(z)||ge?R:"",P([z,Ce])],f("member-chain",pe)}n.exports=_}}),Ka=ee({"src/language-js/print/call-expression.js"(e,n){"use strict";re();var{builders:{join:t,group:s}}=qe(),a=Lt(),{getCallArguments:r,hasFlowAnnotationComment:u,isCallExpression:i,isMemberish:o,isStringLiteral:c,isTemplateOnItsOwnLine:y,isTestCall:h,iterateCallArgumentsPath:g}=Ke(),p=km(),D=za(),{printOptionalToken:v,printFunctionTypeParameters:w}=Dt();function T(A,B,k){let P=A.getValue(),R=A.getParentNode(),f=P.type==="NewExpression",x=P.type==="ImportExpression",m=v(A),E=r(P);if(E.length>0&&(!x&&!f&&F(P,R)||E.length===1&&y(E[0],B.originalText)||!f&&h(P,R))){let C=[];return g(A,()=>{C.push(k())}),[f?"new ":"",k("callee"),m,w(A,B,k),"(",t(", ",C),")"]}let l=(B.parser==="babel"||B.parser==="babel-flow")&&P.callee&&P.callee.type==="Identifier"&&u(P.callee.trailingComments);if(l&&(P.callee.trailingComments[0].printed=!0),!x&&!f&&o(P.callee)&&!A.call(C=>a(C,B),"callee"))return p(A,B,k);let d=[f?"new ":"",x?"import":k("callee"),m,l?`/*:: ${P.callee.trailingComments[0].value.slice(2).trim()} */`:"",w(A,B,k),D(A,B,k)];return x||i(P.callee)?s(d):d}function F(A,B){if(A.callee.type!=="Identifier")return!1;if(A.callee.name==="require")return!0;if(A.callee.name==="define"){let k=r(A);return B.type==="ExpressionStatement"&&(k.length===1||k.length===2&&k[0].type==="ArrayExpression"||k.length===3&&c(k[0])&&k[1].type==="ArrayExpression")}return!1}n.exports={printCallExpression:T}}}),Yt=ee({"src/language-js/print/assignment.js"(e,n){"use strict";re();var{isNonEmptyArray:t,getStringWidth:s}=Ge(),{builders:{line:a,group:r,indent:u,indentIfBreak:i,lineSuffixBoundary:o},utils:{cleanDoc:c,willBreak:y,canBreak:h}}=qe(),{hasLeadingOwnLineComment:g,isBinaryish:p,isStringLiteral:D,isLiteral:v,isNumericLiteral:w,isCallExpression:T,isMemberExpression:F,getCallArguments:A,rawText:B,hasComment:k,isSignedNumericLiteral:P,isObjectProperty:R}=Ke(),{shouldInlineLogicalExpression:f}=Hn(),{printCallExpression:x}=Ka();function m(W,X,oe,ae,Ae,z){let H=d(W,X,oe,ae,z),Z=oe(z,{assignmentLayout:H});switch(H){case"break-after-operator":return r([r(ae),Ae,r(u([a,Z]))]);case"never-break-after-operator":return r([r(ae),Ae," ",Z]);case"fluid":{let ne=Symbol("assignment");return r([r(ae),Ae,r(u(a),{id:ne}),o,i(Z,{groupId:ne})])}case"break-lhs":return r([ae,Ae," ",r(Z)]);case"chain":return[r(ae),Ae,a,Z];case"chain-tail":return[r(ae),Ae,u([a,Z])];case"chain-tail-arrow-chain":return[r(ae),Ae,Z];case"only-left":return ae}}function E(W,X,oe){let ae=W.getValue();return m(W,X,oe,oe("left"),[" ",ae.operator],"right")}function l(W,X,oe){return m(W,X,oe,oe("id")," =","init")}function d(W,X,oe,ae,Ae){let z=W.getValue(),H=z[Ae];if(!H)return"only-left";let Z=!b(H);if(W.match(b,N,Ce=>!Z||Ce.type!=="ExpressionStatement"&&Ce.type!=="VariableDeclaration"))return Z?H.type==="ArrowFunctionExpression"&&H.body.type==="ArrowFunctionExpression"?"chain-tail-arrow-chain":"chain-tail":"chain";if(!Z&&b(H.right)||g(X.originalText,H))return"break-after-operator";if(H.type==="CallExpression"&&H.callee.name==="require"||X.parser==="json5"||X.parser==="json")return"never-break-after-operator";if(_(z)||I(z)||q(z)||J(z)&&h(ae))return"break-lhs";let ge=se(z,ae,X);return W.call(()=>C(W,X,oe,ge),Ae)?"break-after-operator":ge||H.type==="TemplateLiteral"||H.type==="TaggedTemplateExpression"||H.type==="BooleanLiteral"||w(H)||H.type==="ClassExpression"?"never-break-after-operator":"fluid"}function C(W,X,oe,ae){let Ae=W.getValue();if(p(Ae)&&!f(Ae))return!0;switch(Ae.type){case"StringLiteralTypeAnnotation":case"SequenceExpression":return!0;case"ConditionalExpression":{let{test:Z}=Ae;return p(Z)&&!f(Z)}case"ClassExpression":return t(Ae.decorators)}if(ae)return!1;let z=Ae,H=[];for(;;)if(z.type==="UnaryExpression")z=z.argument,H.push("argument");else if(z.type==="TSNonNullExpression")z=z.expression,H.push("expression");else break;return!!(D(z)||W.call(()=>V(W,X,oe),...H))}function _(W){if(N(W)){let X=W.left||W.id;return X.type==="ObjectPattern"&&X.properties.length>2&&X.properties.some(oe=>R(oe)&&(!oe.shorthand||oe.value&&oe.value.type==="AssignmentPattern"))}return!1}function b(W){return W.type==="AssignmentExpression"}function N(W){return b(W)||W.type==="VariableDeclarator"}function I(W){let X=$(W);if(t(X)){let oe=W.type==="TSTypeAliasDeclaration"?"constraint":"bound";if(X.length>1&&X.some(ae=>ae[oe]||ae.default))return!0}return!1}function $(W){return M(W)&&W.typeParameters&&W.typeParameters.params?W.typeParameters.params:null}function M(W){return W.type==="TSTypeAliasDeclaration"||W.type==="TypeAlias"}function q(W){if(W.type!=="VariableDeclarator")return!1;let{typeAnnotation:X}=W.id;if(!X||!X.typeAnnotation)return!1;let oe=L(X.typeAnnotation);return t(oe)&&oe.length>1&&oe.some(ae=>t(L(ae))||ae.type==="TSConditionalType")}function J(W){return W.type==="VariableDeclarator"&&W.init&&W.init.type==="ArrowFunctionExpression"}function L(W){return Y(W)&&W.typeParameters&&W.typeParameters.params?W.typeParameters.params:null}function Y(W){return W.type==="TSTypeReference"||W.type==="GenericTypeAnnotation"}function V(W,X,oe){let ae=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,Ae=W.getValue(),z=()=>V(W,X,oe,!0);if(Ae.type==="TSNonNullExpression")return W.call(z,"expression");if(T(Ae)){if(x(W,X,oe).label==="member-chain")return!1;let Z=A(Ae);return!(Z.length===0||Z.length===1&&K(Z[0],X))||Q(Ae,oe)?!1:W.call(z,"callee")}return F(Ae)?W.call(z,"object"):ae&&(Ae.type==="Identifier"||Ae.type==="ThisExpression")}var O=.25;function K(W,X){let{printWidth:oe}=X;if(k(W))return!1;let ae=oe*O;if(W.type==="ThisExpression"||W.type==="Identifier"&&W.name.length<=ae||P(W)&&!k(W.argument))return!0;let Ae=W.type==="Literal"&&"regex"in W&&W.regex.pattern||W.type==="RegExpLiteral"&&W.pattern;return Ae?Ae.length<=ae:D(W)?B(W).length<=ae:W.type==="TemplateLiteral"?W.expressions.length===0&&W.quasis[0].value.raw.length<=ae&&!W.quasis[0].value.raw.includes(`
`):v(W)}function se(W,X,oe){if(!R(W))return!1;X=c(X);let ae=3;return typeof X=="string"&&s(X)<oe.tabWidth+ae}function Q(W,X){let oe=le(W);if(t(oe)){if(oe.length>1)return!0;if(oe.length===1){let Ae=oe[0];if(Ae.type==="TSUnionType"||Ae.type==="UnionTypeAnnotation"||Ae.type==="TSIntersectionType"||Ae.type==="IntersectionTypeAnnotation"||Ae.type==="TSTypeLiteral"||Ae.type==="ObjectTypeAnnotation")return!0}let ae=W.typeParameters?"typeParameters":"typeArguments";if(y(X(ae)))return!0}return!1}function le(W){return W.typeParameters&&W.typeParameters.params||W.typeArguments&&W.typeArguments.params}n.exports={printVariableDeclarator:l,printAssignmentExpression:E,printAssignment:m,isArrowFunctionVariableDeclarator:J}}}),Pr=ee({"src/language-js/print/function-parameters.js"(e,n){"use strict";re();var{getNextNonSpaceNonCommentCharacter:t}=Ge(),{printDanglingComments:s}=et(),{builders:{line:a,hardline:r,softline:u,group:i,indent:o,ifBreak:c},utils:{removeLines:y,willBreak:h}}=qe(),{getFunctionParameters:g,iterateFunctionParametersPath:p,isSimpleType:D,isTestCall:v,isTypeAnnotationAFunction:w,isObjectType:T,isObjectTypePropertyAFunction:F,hasRestParameter:A,shouldPrintComma:B,hasComment:k,isNextLineEmpty:P}=Ke(),{locEnd:R}=it(),{ArgExpansionBailout:f}=zt(),{printFunctionTypeParameters:x}=Dt();function m(C,_,b,N,I){let $=C.getValue(),M=g($),q=I?x(C,b,_):"";if(M.length===0)return[q,"(",s(C,b,!0,se=>t(b.originalText,se,R)===")"),")"];let J=C.getParentNode(),L=v(J),Y=E($),V=[];if(p(C,(se,Q)=>{let le=Q===M.length-1;le&&$.rest&&V.push("..."),V.push(_()),!le&&(V.push(","),L||Y?V.push(" "):P(M[Q],b)?V.push(r,r):V.push(a))}),N){if(h(q)||h(V))throw new f;return i([y(q),"(",y(V),")"])}let O=M.every(se=>!se.decorators);return Y&&O?[q,"(",...V,")"]:L?[q,"(",...V,")"]:(F(J)||w(J)||J.type==="TypeAlias"||J.type==="UnionTypeAnnotation"||J.type==="TSUnionType"||J.type==="IntersectionTypeAnnotation"||J.type==="FunctionTypeAnnotation"&&J.returnType===$)&&M.length===1&&M[0].name===null&&$.this!==M[0]&&M[0].typeAnnotation&&$.typeParameters===null&&D(M[0].typeAnnotation)&&!$.rest?b.arrowParens==="always"?["(",...V,")"]:V:[q,"(",o([u,...V]),c(!A($)&&B(b,"all")?",":""),u,")"]}function E(C){if(!C)return!1;let _=g(C);if(_.length!==1)return!1;let[b]=_;return!k(b)&&(b.type==="ObjectPattern"||b.type==="ArrayPattern"||b.type==="Identifier"&&b.typeAnnotation&&(b.typeAnnotation.type==="TypeAnnotation"||b.typeAnnotation.type==="TSTypeAnnotation")&&T(b.typeAnnotation.typeAnnotation)||b.type==="FunctionTypeParam"&&T(b.typeAnnotation)||b.type==="AssignmentPattern"&&(b.left.type==="ObjectPattern"||b.left.type==="ArrayPattern")&&(b.right.type==="Identifier"||b.right.type==="ObjectExpression"&&b.right.properties.length===0||b.right.type==="ArrayExpression"&&b.right.elements.length===0))}function l(C){let _;return C.returnType?(_=C.returnType,_.typeAnnotation&&(_=_.typeAnnotation)):C.typeAnnotation&&(_=C.typeAnnotation),_}function d(C,_){let b=l(C);if(!b)return!1;let N=C.typeParameters&&C.typeParameters.params;if(N){if(N.length>1)return!1;if(N.length===1){let I=N[0];if(I.constraint||I.default)return!1}}return g(C).length===1&&(T(b)||h(_))}n.exports={printFunctionParameters:m,shouldHugFunctionParameters:E,shouldGroupFunctionParameters:d}}}),kr=ee({"src/language-js/print/type-annotation.js"(e,n){"use strict";re();var{printComments:t,printDanglingComments:s}=et(),{isNonEmptyArray:a}=Ge(),{builders:{group:r,join:u,line:i,softline:o,indent:c,align:y,ifBreak:h}}=qe(),g=Lt(),{locStart:p}=it(),{isSimpleType:D,isObjectType:v,hasLeadingOwnLineComment:w,isObjectTypePropertyAFunction:T,shouldPrintComma:F}=Ke(),{printAssignment:A}=Yt(),{printFunctionParameters:B,shouldGroupFunctionParameters:k}=Pr(),{printArrayItems:P}=Kt();function R(b){if(D(b)||v(b))return!0;if(b.type==="UnionTypeAnnotation"||b.type==="TSUnionType"){let N=b.types.filter($=>$.type==="VoidTypeAnnotation"||$.type==="TSVoidKeyword"||$.type==="NullLiteralTypeAnnotation"||$.type==="TSNullKeyword").length,I=b.types.some($=>$.type==="ObjectTypeAnnotation"||$.type==="TSTypeLiteral"||$.type==="GenericTypeAnnotation"||$.type==="TSTypeReference");if(b.types.length-1===N&&I)return!0}return!1}function f(b,N,I){let $=N.semi?";":"",M=b.getValue(),q=[];return q.push("opaque type ",I("id"),I("typeParameters")),M.supertype&&q.push(": ",I("supertype")),M.impltype&&q.push(" = ",I("impltype")),q.push($),q}function x(b,N,I){let $=N.semi?";":"",M=b.getValue(),q=[];M.declare&&q.push("declare "),q.push("type ",I("id"),I("typeParameters"));let J=M.type==="TSTypeAliasDeclaration"?"typeAnnotation":"right";return[A(b,N,I,q," =",J),$]}function m(b,N,I){let $=b.getValue(),M=b.map(I,"types"),q=[],J=!1;for(let L=0;L<M.length;++L)L===0?q.push(M[L]):v($.types[L-1])&&v($.types[L])?q.push([" & ",J?c(M[L]):M[L]]):!v($.types[L-1])&&!v($.types[L])?q.push(c([" &",i,M[L]])):(L>1&&(J=!0),q.push(" & ",L>1?c(M[L]):M[L]));return r(q)}function E(b,N,I){let $=b.getValue(),M=b.getParentNode(),q=M.type!=="TypeParameterInstantiation"&&M.type!=="TSTypeParameterInstantiation"&&M.type!=="GenericTypeAnnotation"&&M.type!=="TSTypeReference"&&M.type!=="TSTypeAssertion"&&M.type!=="TupleTypeAnnotation"&&M.type!=="TSTupleType"&&!(M.type==="FunctionTypeParam"&&!M.name&&b.getParentNode(1).this!==M)&&!((M.type==="TypeAlias"||M.type==="VariableDeclarator"||M.type==="TSTypeAliasDeclaration")&&w(N.originalText,$)),J=R($),L=b.map(O=>{let K=I();return J||(K=y(2,K)),t(O,K,N)},"types");if(J)return u(" | ",L);let Y=q&&!w(N.originalText,$),V=[h([Y?i:"","| "]),u([i,"| "],L)];return g(b,N)?r([c(V),o]):M.type==="TupleTypeAnnotation"&&M.types.length>1||M.type==="TSTupleType"&&M.elementTypes.length>1?r([c([h(["(",o]),V]),o,h(")")]):r(q?c(V):V)}function l(b,N,I){let $=b.getValue(),M=[],q=b.getParentNode(0),J=b.getParentNode(1),L=b.getParentNode(2),Y=$.type==="TSFunctionType"||!((q.type==="ObjectTypeProperty"||q.type==="ObjectTypeInternalSlot")&&!q.variance&&!q.optional&&p(q)===p($)||q.type==="ObjectTypeCallProperty"||L&&L.type==="DeclareFunction"),V=Y&&(q.type==="TypeAnnotation"||q.type==="TSTypeAnnotation"),O=V&&Y&&(q.type==="TypeAnnotation"||q.type==="TSTypeAnnotation")&&J.type==="ArrowFunctionExpression";T(q)&&(Y=!0,V=!0),O&&M.push("(");let K=B(b,I,N,!1,!0),se=$.returnType||$.predicate||$.typeAnnotation?[Y?" => ":": ",I("returnType"),I("predicate"),I("typeAnnotation")]:"",Q=k($,se);return M.push(Q?r(K):K),se&&M.push(se),O&&M.push(")"),r(M)}function d(b,N,I){let $=b.getValue(),M=$.type==="TSTupleType"?"elementTypes":"types",q=$[M],J=a(q),L=J?o:"";return r(["[",c([L,P(b,N,M,I)]),h(J&&F(N,"all")?",":""),s(b,N,!0),L,"]"])}function C(b,N,I){let $=b.getValue(),M=$.type==="OptionalIndexedAccessType"&&$.optional?"?.[":"[";return[I("objectType"),M,I("indexType"),"]"]}function _(b,N,I){let $=b.getValue();return[$.postfix?"":I,N("typeAnnotation"),$.postfix?I:""]}n.exports={printOpaqueType:f,printTypeAlias:x,printIntersectionType:m,printUnionType:E,printFunctionType:l,printTupleType:d,printIndexedAccessType:C,shouldHugType:R,printJSDocType:_}}}),Ir=ee({"src/language-js/print/type-parameters.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{builders:{join:s,line:a,hardline:r,softline:u,group:i,indent:o,ifBreak:c}}=qe(),{isTestCall:y,hasComment:h,CommentCheckFlags:g,isTSXFile:p,shouldPrintComma:D,getFunctionParameters:v,isObjectType:w}=Ke(),{createGroupIdMapper:T}=Ge(),{shouldHugType:F}=kr(),{isArrowFunctionVariableDeclarator:A}=Yt(),B=T("typeParameters");function k(f,x,m,E){let l=f.getValue();if(!l[E])return"";if(!Array.isArray(l[E]))return m(E);let d=f.getNode(2),C=d&&y(d);if(!f.match(I=>!(I[E].length===1&&w(I[E][0])),void 0,(I,$)=>$==="typeAnnotation",I=>I.type==="Identifier",A)&&(C||l[E].length===0||l[E].length===1&&(l[E][0].type==="NullableTypeAnnotation"||F(l[E][0]))))return["<",s(", ",f.map(m,E)),P(f,x),">"];let N=l.type==="TSTypeParameterInstantiation"?"":v(l).length===1&&p(x)&&!l[E][0].constraint&&f.getParentNode().type==="ArrowFunctionExpression"?",":D(x,"all")?c(","):"";return i(["<",o([u,s([",",a],f.map(m,E))]),N,u,">"],{id:B(l)})}function P(f,x){let m=f.getValue();if(!h(m,g.Dangling))return"";let E=!h(m,g.Line),l=t(f,x,E);return E?l:[l,r]}function R(f,x,m){let E=f.getValue(),l=[],d=f.getParentNode();return d.type==="TSMappedType"?(l.push("[",m("name")),E.constraint&&l.push(" in ",m("constraint")),d.nameType&&l.push(" as ",f.callParent(()=>m("nameType"))),l.push("]"),l):(E.variance&&l.push(m("variance")),E.in&&l.push("in "),E.out&&l.push("out "),l.push(m("name")),E.bound&&l.push(": ",m("bound")),E.constraint&&l.push(" extends ",m("constraint")),E.default&&l.push(" = ",m("default")),l)}n.exports={printTypeParameter:R,printTypeParameters:k,getTypeParametersGroupId:B}}}),Qt=ee({"src/language-js/print/property.js"(e,n){"use strict";re();var{printComments:t}=et(),{printString:s,printNumber:a}=Ge(),{isNumericLiteral:r,isSimpleNumber:u,isStringLiteral:i,isStringPropSafeToUnquote:o,rawText:c}=Ke(),{printAssignment:y}=Yt(),h=new WeakMap;function g(D,v,w){let T=D.getNode();if(T.computed)return["[",w("key"),"]"];let F=D.getParentNode(),{key:A}=T;if(T.type==="ClassPrivateProperty"&&A.type==="Identifier")return["#",w("key")];if(v.quoteProps==="consistent"&&!h.has(F)){let B=(F.properties||F.body||F.members).some(k=>!k.computed&&k.key&&i(k.key)&&!o(k,v));h.set(F,B)}if((A.type==="Identifier"||r(A)&&u(a(c(A)))&&String(A.value)===a(c(A))&&!(v.parser==="typescript"||v.parser==="babel-ts"))&&(v.parser==="json"||v.quoteProps==="consistent"&&h.get(F))){let B=s(JSON.stringify(A.type==="Identifier"?A.name:A.value.toString()),v);return D.call(k=>t(k,B,v),"key")}return o(T,v)&&(v.quoteProps==="as-needed"||v.quoteProps==="consistent"&&!h.get(F))?D.call(B=>t(B,/^\d/.test(A.value)?a(A.value):A.value,v),"key"):w("key")}function p(D,v,w){return D.getValue().shorthand?w("value"):y(D,v,w,g(D,v,w),":","value")}n.exports={printProperty:p,printPropertyKey:g}}}),Lr=ee({"src/language-js/print/function.js"(e,n){"use strict";re();var t=Xt(),{printDanglingComments:s,printCommentsSeparately:a}=et(),r=lt(),{getNextNonSpaceNonCommentCharacterIndex:u}=Ge(),{builders:{line:i,softline:o,group:c,indent:y,ifBreak:h,hardline:g,join:p,indentIfBreak:D},utils:{removeLines:v,willBreak:w}}=qe(),{ArgExpansionBailout:T}=zt(),{getFunctionParameters:F,hasLeadingOwnLineComment:A,isFlowAnnotationComment:B,isJsxNode:k,isTemplateOnItsOwnLine:P,shouldPrintComma:R,startsWithNoLookaheadToken:f,isBinaryish:x,isLineComment:m,hasComment:E,getComments:l,CommentCheckFlags:d,isCallLikeExpression:C,isCallExpression:_,getCallArguments:b,hasNakedLeftSide:N,getLeftSide:I}=Ke(),{locEnd:$}=it(),{printFunctionParameters:M,shouldGroupFunctionParameters:q}=Pr(),{printPropertyKey:J}=Qt(),{printFunctionTypeParameters:L}=Dt();function Y(H,Z,ne,fe){let ge=H.getValue(),Ce=!1;if((ge.type==="FunctionDeclaration"||ge.type==="FunctionExpression")&&fe&&fe.expandLastArg){let ve=H.getParentNode();_(ve)&&b(ve).length>1&&(Ce=!0)}let _e=[];ge.type==="TSDeclareFunction"&&ge.declare&&_e.push("declare "),ge.async&&_e.push("async "),ge.generator?_e.push("function* "):_e.push("function "),ge.id&&_e.push(Z("id"));let Oe=M(H,Z,ne,Ce),pe=X(H,Z,ne),ie=q(ge,pe);return _e.push(L(H,ne,Z),c([ie?c(Oe):Oe,pe]),ge.body?" ":"",Z("body")),ne.semi&&(ge.declare||!ge.body)&&_e.push(";"),_e}function V(H,Z,ne){let fe=H.getNode(),{kind:ge}=fe,Ce=fe.value||fe,_e=[];return!ge||ge==="init"||ge==="method"||ge==="constructor"?Ce.async&&_e.push("async "):(t.ok(ge==="get"||ge==="set"),_e.push(ge," ")),Ce.generator&&_e.push("*"),_e.push(J(H,Z,ne),fe.optional||fe.key.optional?"?":""),fe===Ce?_e.push(O(H,Z,ne)):Ce.type==="FunctionExpression"?_e.push(H.call(Oe=>O(Oe,Z,ne),"value")):_e.push(ne("value")),_e}function O(H,Z,ne){let fe=H.getNode(),ge=M(H,ne,Z),Ce=X(H,ne,Z),_e=q(fe,Ce),Oe=[L(H,Z,ne),c([_e?c(ge):ge,Ce])];return fe.body?Oe.push(" ",ne("body")):Oe.push(Z.semi?";":""),Oe}function K(H,Z,ne,fe){let ge=H.getValue(),Ce=[];if(ge.async&&Ce.push("async "),W(H,Z))Ce.push(ne(["params",0]));else{let Oe=fe&&(fe.expandLastArg||fe.expandFirstArg),pe=X(H,ne,Z);if(Oe){if(w(pe))throw new T;pe=c(v(pe))}Ce.push(c([M(H,ne,Z,Oe,!0),pe]))}let _e=s(H,Z,!0,Oe=>{let pe=u(Z.originalText,Oe,$);return pe!==!1&&Z.originalText.slice(pe,pe+2)==="=>"});return _e&&Ce.push(" ",_e),Ce}function se(H,Z,ne,fe,ge,Ce){let _e=H.getName(),Oe=H.getParentNode(),pe=C(Oe)&&_e==="callee",ie=Boolean(Z&&Z.assignmentLayout),ve=Ce.body.type!=="BlockStatement"&&Ce.body.type!=="ObjectExpression"&&Ce.body.type!=="SequenceExpression",ce=pe&&ve||Z&&Z.assignmentLayout==="chain-tail-arrow-chain",U=Symbol("arrow-chain");return Ce.body.type==="SequenceExpression"&&(ge=c(["(",y([o,ge]),o,")"])),c([c(y([pe||ie?o:"",c(p([" =>",i],ne),{shouldBreak:fe})]),{id:U,shouldBreak:ce})," =>",D(ve?y([i,ge]):[" ",ge],{groupId:U}),pe?h(o,"",{groupId:U}):""])}function Q(H,Z,ne,fe){let ge=H.getValue(),Ce=[],_e=[],Oe=!1;if(function U(){let de=K(H,Z,ne,fe);if(Ce.length===0)Ce.push(de);else{let{leading:De,trailing:he}=a(H,Z);Ce.push([De,de]),_e.unshift(he)}Oe=Oe||ge.returnType&&F(ge).length>0||ge.typeParameters||F(ge).some(De=>De.type!=="Identifier"),ge.body.type!=="ArrowFunctionExpression"||fe&&fe.expandLastArg?_e.unshift(ne("body",fe)):(ge=ge.body,H.call(U,"body"))}(),Ce.length>1)return se(H,fe,Ce,Oe,_e,ge);let pe=Ce;if(pe.push(" =>"),!A(Z.originalText,ge.body)&&(ge.body.type==="ArrayExpression"||ge.body.type==="ObjectExpression"||ge.body.type==="BlockStatement"||k(ge.body)||P(ge.body,Z.originalText)||ge.body.type==="ArrowFunctionExpression"||ge.body.type==="DoExpression"))return c([...pe," ",_e]);if(ge.body.type==="SequenceExpression")return c([...pe,c([" (",y([o,_e]),o,")"])]);let ie=(fe&&fe.expandLastArg||H.getParentNode().type==="JSXExpressionContainer")&&!E(ge),ve=fe&&fe.expandLastArg&&R(Z,"all"),ce=ge.body.type==="ConditionalExpression"&&!f(ge.body,!1);return c([...pe,c([y([i,ce?h("","("):"",_e,ce?h("",")"):""]),ie?[h(ve?",":""),o]:""])])}function le(H){let Z=F(H);return Z.length===1&&!H.typeParameters&&!E(H,d.Dangling)&&Z[0].type==="Identifier"&&!Z[0].typeAnnotation&&!E(Z[0])&&!Z[0].optional&&!H.predicate&&!H.returnType}function W(H,Z){if(Z.arrowParens==="always")return!1;if(Z.arrowParens==="avoid"){let ne=H.getValue();return le(ne)}return!1}function X(H,Z,ne){let fe=H.getValue(),ge=Z("returnType");if(fe.returnType&&B(ne.originalText,fe.returnType))return[" /*: ",ge," */"];let Ce=[ge];return fe.returnType&&fe.returnType.typeAnnotation&&Ce.unshift(": "),fe.predicate&&Ce.push(fe.returnType?" ":": ",Z("predicate")),Ce}function oe(H,Z,ne){let fe=H.getValue(),ge=Z.semi?";":"",Ce=[];fe.argument&&(z(Z,fe.argument)?Ce.push([" (",y([g,ne("argument")]),g,")"]):x(fe.argument)||fe.argument.type==="SequenceExpression"?Ce.push(c([h(" ("," "),y([o,ne("argument")]),o,h(")")])):Ce.push(" ",ne("argument")));let _e=l(fe),Oe=r(_e),pe=Oe&&m(Oe);return pe&&Ce.push(ge),E(fe,d.Dangling)&&Ce.push(" ",s(H,Z,!0)),pe||Ce.push(ge),Ce}function ae(H,Z,ne){return["return",oe(H,Z,ne)]}function Ae(H,Z,ne){return["throw",oe(H,Z,ne)]}function z(H,Z){if(A(H.originalText,Z))return!0;if(N(Z)){let ne=Z,fe;for(;fe=I(ne);)if(ne=fe,A(H.originalText,ne))return!0}return!1}n.exports={printFunction:Y,printArrowFunction:Q,printMethod:V,printReturnStatement:ae,printThrowStatement:Ae,printMethodInternal:O,shouldPrintParamsWithoutParens:W}}}),Gn=ee({"src/language-js/print/decorators.js"(e,n){"use strict";re();var{isNonEmptyArray:t,hasNewline:s}=Ge(),{builders:{line:a,hardline:r,join:u,breakParent:i,group:o}}=qe(),{locStart:c,locEnd:y}=it(),{getParentExportDeclaration:h}=Ke();function g(T,F,A){let B=T.getValue();return o([u(a,T.map(A,"decorators")),v(B,F)?r:a])}function p(T,F,A){return[u(r,T.map(A,"declaration","decorators")),r]}function D(T,F,A){let B=T.getValue(),{decorators:k}=B;if(!t(k)||w(T.getParentNode()))return;let P=B.type==="ClassExpression"||B.type==="ClassDeclaration"||v(B,F);return[h(T)?r:P?i:"",u(a,T.map(A,"decorators")),a]}function v(T,F){return T.decorators.some(A=>s(F.originalText,y(A)))}function w(T){if(T.type!=="ExportDefaultDeclaration"&&T.type!=="ExportNamedDeclaration"&&T.type!=="DeclareExportDeclaration")return!1;let F=T.declaration&&T.declaration.decorators;return t(F)&&c(T)===c(F[0])}n.exports={printDecorators:D,printClassMemberDecorators:g,printDecoratorsBeforeExport:p,hasDecoratorsBeforeExport:w}}}),Zt=ee({"src/language-js/print/class.js"(e,n){"use strict";re();var{isNonEmptyArray:t,createGroupIdMapper:s}=Ge(),{printComments:a,printDanglingComments:r}=et(),{builders:{join:u,line:i,hardline:o,softline:c,group:y,indent:h,ifBreak:g}}=qe(),{hasComment:p,CommentCheckFlags:D}=Ke(),{getTypeParametersGroupId:v}=Ir(),{printMethod:w}=Lr(),{printOptionalToken:T,printTypeAnnotation:F,printDefiniteToken:A}=Dt(),{printPropertyKey:B}=Qt(),{printAssignment:k}=Yt(),{printClassMemberDecorators:P}=Gn();function R(b,N,I){let $=b.getValue(),M=[];$.declare&&M.push("declare "),$.abstract&&M.push("abstract "),M.push("class");let q=$.id&&p($.id,D.Trailing)||$.typeParameters&&p($.typeParameters,D.Trailing)||$.superClass&&p($.superClass)||t($.extends)||t($.mixins)||t($.implements),J=[],L=[];if($.id&&J.push(" ",I("id")),J.push(I("typeParameters")),$.superClass){let Y=[d(b,N,I),I("superTypeParameters")],V=b.call(O=>["extends ",a(O,Y,N)],"superClass");q?L.push(i,y(V)):L.push(" ",V)}else L.push(l(b,N,I,"extends"));if(L.push(l(b,N,I,"mixins"),l(b,N,I,"implements")),q){let Y;E($)?Y=[...J,h(L)]:Y=h([...J,L]),M.push(y(Y,{id:f($)}))}else M.push(...J,...L);return M.push(" ",I("body")),M}var f=s("heritageGroup");function x(b){return g(o,"",{groupId:f(b)})}function m(b){return["superClass","extends","mixins","implements"].filter(N=>Boolean(b[N])).length>1}function E(b){return b.typeParameters&&!p(b.typeParameters,D.Trailing|D.Line)&&!m(b)}function l(b,N,I,$){let M=b.getValue();if(!t(M[$]))return"";let q=r(b,N,!0,J=>{let{marker:L}=J;return L===$});return[E(M)?g(" ",i,{groupId:v(M.typeParameters)}):i,q,q&&o,$,y(h([i,u([",",i],b.map(I,$))]))]}function d(b,N,I){let $=I("superClass");return b.getParentNode().type==="AssignmentExpression"?y(g(["(",h([c,$]),c,")"],$)):$}function C(b,N,I){let $=b.getValue(),M=[];return t($.decorators)&&M.push(P(b,N,I)),$.accessibility&&M.push($.accessibility+" "),$.readonly&&M.push("readonly "),$.declare&&M.push("declare "),$.static&&M.push("static "),($.type==="TSAbstractMethodDefinition"||$.abstract)&&M.push("abstract "),$.override&&M.push("override "),M.push(w(b,N,I)),M}function _(b,N,I){let $=b.getValue(),M=[],q=N.semi?";":"";return t($.decorators)&&M.push(P(b,N,I)),$.accessibility&&M.push($.accessibility+" "),$.declare&&M.push("declare "),$.static&&M.push("static "),($.type==="TSAbstractPropertyDefinition"||$.abstract)&&M.push("abstract "),$.override&&M.push("override "),$.readonly&&M.push("readonly "),$.variance&&M.push(I("variance")),$.type==="ClassAccessorProperty"&&M.push("accessor "),M.push(B(b,N,I),T(b),A(b),F(b,N,I)),[k(b,N,I,M," =","value"),q]}n.exports={printClass:R,printClassMethod:C,printClassProperty:_,printHardlineAfterHeritage:x}}}),Ya=ee({"src/language-js/print/interface.js"(e,n){"use strict";re();var{isNonEmptyArray:t}=Ge(),{builders:{join:s,line:a,group:r,indent:u,ifBreak:i}}=qe(),{hasComment:o,identity:c,CommentCheckFlags:y}=Ke(),{getTypeParametersGroupId:h}=Ir(),{printTypeScriptModifiers:g}=Dt();function p(D,v,w){let T=D.getValue(),F=[];T.declare&&F.push("declare "),T.type==="TSInterfaceDeclaration"&&F.push(T.abstract?"abstract ":"",g(D,v,w)),F.push("interface");let A=[],B=[];T.type!=="InterfaceTypeAnnotation"&&A.push(" ",w("id"),w("typeParameters"));let k=T.typeParameters&&!o(T.typeParameters,y.Trailing|y.Line);return t(T.extends)&&B.push(k?i(" ",a,{groupId:h(T.typeParameters)}):a,"extends ",(T.extends.length===1?c:u)(s([",",a],D.map(w,"extends")))),T.id&&o(T.id,y.Trailing)||t(T.extends)?k?F.push(r([...A,u(B)])):F.push(r(u([...A,...B]))):F.push(...A,...B),F.push(" ",w("body")),r(F)}n.exports={printInterface:p}}}),Qa=ee({"src/language-js/print/module.js"(e,n){"use strict";re();var{isNonEmptyArray:t}=Ge(),{builders:{softline:s,group:a,indent:r,join:u,line:i,ifBreak:o,hardline:c}}=qe(),{printDanglingComments:y}=et(),{hasComment:h,CommentCheckFlags:g,shouldPrintComma:p,needsHardlineAfterDanglingComment:D,isStringLiteral:v,rawText:w}=Ke(),{locStart:T,hasSameLoc:F}=it(),{hasDecoratorsBeforeExport:A,printDecoratorsBeforeExport:B}=Gn();function k(_,b,N){let I=_.getValue(),$=b.semi?";":"",M=[],{importKind:q}=I;return M.push("import"),q&&q!=="value"&&M.push(" ",q),M.push(m(_,b,N),x(_,b,N),l(_,b,N),$),M}function P(_,b,N){let I=_.getValue(),$=[];A(I)&&$.push(B(_,b,N));let{type:M,exportKind:q,declaration:J}=I;return $.push("export"),(I.default||M==="ExportDefaultDeclaration")&&$.push(" default"),h(I,g.Dangling)&&($.push(" ",y(_,b,!0)),D(I)&&$.push(c)),J?$.push(" ",N("declaration")):$.push(q==="type"?" type":"",m(_,b,N),x(_,b,N),l(_,b,N)),f(I,b)&&$.push(";"),$}function R(_,b,N){let I=_.getValue(),$=b.semi?";":"",M=[],{exportKind:q,exported:J}=I;return M.push("export"),q==="type"&&M.push(" type"),M.push(" *"),J&&M.push(" as ",N("exported")),M.push(x(_,b,N),l(_,b,N),$),M}function f(_,b){if(!b.semi)return!1;let{type:N,declaration:I}=_,$=_.default||N==="ExportDefaultDeclaration";if(!I)return!0;let{type:M}=I;return!!($&&M!=="ClassDeclaration"&&M!=="FunctionDeclaration"&&M!=="TSInterfaceDeclaration"&&M!=="DeclareClass"&&M!=="DeclareFunction"&&M!=="TSDeclareFunction"&&M!=="EnumDeclaration")}function x(_,b,N){let I=_.getValue();if(!I.source)return"";let $=[];return E(I,b)||$.push(" from"),$.push(" ",N("source")),$}function m(_,b,N){let I=_.getValue();if(E(I,b))return"";let $=[" "];if(t(I.specifiers)){let M=[],q=[];_.each(()=>{let J=_.getValue().type;if(J==="ExportNamespaceSpecifier"||J==="ExportDefaultSpecifier"||J==="ImportNamespaceSpecifier"||J==="ImportDefaultSpecifier")M.push(N());else if(J==="ExportSpecifier"||J==="ImportSpecifier")q.push(N());else throw new Error(`Unknown specifier type ${JSON.stringify(J)}`)},"specifiers"),$.push(u(", ",M)),q.length>0&&(M.length>0&&$.push(", "),q.length>1||M.length>0||I.specifiers.some(L=>h(L))?$.push(a(["{",r([b.bracketSpacing?i:s,u([",",i],q)]),o(p(b)?",":""),b.bracketSpacing?i:s,"}"])):$.push(["{",b.bracketSpacing?" ":"",...q,b.bracketSpacing?" ":"","}"]))}else $.push("{}");return $}function E(_,b){let{type:N,importKind:I,source:$,specifiers:M}=_;return N!=="ImportDeclaration"||t(M)||I==="type"?!1:!/{\s*}/.test(b.originalText.slice(T(_),T($)))}function l(_,b,N){let I=_.getNode();return t(I.assertions)?[" assert {",b.bracketSpacing?" ":"",u(", ",_.map(N,"assertions")),b.bracketSpacing?" ":"","}"]:""}function d(_,b,N){let I=_.getNode(),{type:$}=I,M=[],q=$==="ImportSpecifier"?I.importKind:I.exportKind;q&&q!=="value"&&M.push(q," ");let J=$.startsWith("Import"),L=J?"imported":"local",Y=J?"local":"exported",V=I[L],O=I[Y],K="",se="";return $==="ExportNamespaceSpecifier"||$==="ImportNamespaceSpecifier"?K="*":V&&(K=N(L)),O&&!C(I)&&(se=N(Y)),M.push(K,K&&se?" as ":"",se),M}function C(_){if(_.type!=="ImportSpecifier"&&_.type!=="ExportSpecifier")return!1;let{local:b,[_.type==="ImportSpecifier"?"imported":"exported"]:N}=_;if(b.type!==N.type||!F(b,N))return!1;if(v(b))return b.value===N.value&&w(b)===w(N);switch(b.type){case"Identifier":return b.name===N.name;default:return!1}}n.exports={printImportDeclaration:k,printExportDeclaration:P,printExportAllDeclaration:R,printModuleSpecifier:d}}}),Un=ee({"src/language-js/print/object.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{builders:{line:s,softline:a,group:r,indent:u,ifBreak:i,hardline:o}}=qe(),{getLast:c,hasNewlineInRange:y,hasNewline:h,isNonEmptyArray:g}=Ge(),{shouldPrintComma:p,hasComment:D,getComments:v,CommentCheckFlags:w,isNextLineEmpty:T}=Ke(),{locStart:F,locEnd:A}=it(),{printOptionalToken:B,printTypeAnnotation:k}=Dt(),{shouldHugFunctionParameters:P}=Pr(),{shouldHugType:R}=kr(),{printHardlineAfterHeritage:f}=Zt();function x(m,E,l){let d=E.semi?";":"",C=m.getValue(),_;C.type==="TSTypeLiteral"?_="members":C.type==="TSInterfaceBody"?_="body":_="properties";let b=C.type==="ObjectTypeAnnotation",N=[_];b&&N.push("indexers","callProperties","internalSlots");let I=N.map(W=>C[W][0]).sort((W,X)=>F(W)-F(X))[0],$=m.getParentNode(0),M=b&&$&&($.type==="InterfaceDeclaration"||$.type==="DeclareInterface"||$.type==="DeclareClass")&&m.getName()==="body",q=C.type==="TSInterfaceBody"||M||C.type==="ObjectPattern"&&$.type!=="FunctionDeclaration"&&$.type!=="FunctionExpression"&&$.type!=="ArrowFunctionExpression"&&$.type!=="ObjectMethod"&&$.type!=="ClassMethod"&&$.type!=="ClassPrivateMethod"&&$.type!=="AssignmentPattern"&&$.type!=="CatchClause"&&C.properties.some(W=>W.value&&(W.value.type==="ObjectPattern"||W.value.type==="ArrayPattern"))||C.type!=="ObjectPattern"&&I&&y(E.originalText,F(C),F(I)),J=M?";":C.type==="TSInterfaceBody"||C.type==="TSTypeLiteral"?i(d,";"):",",L=C.type==="RecordExpression"?"#{":C.exact?"{|":"{",Y=C.exact?"|}":"}",V=[];for(let W of N)m.each(X=>{let oe=X.getValue();V.push({node:oe,printed:l(),loc:F(oe)})},W);N.length>1&&V.sort((W,X)=>W.loc-X.loc);let O=[],K=V.map(W=>{let X=[...O,r(W.printed)];return O=[J,s],(W.node.type==="TSPropertySignature"||W.node.type==="TSMethodSignature"||W.node.type==="TSConstructSignatureDeclaration")&&D(W.node,w.PrettierIgnore)&&O.shift(),T(W.node,E)&&O.push(o),X});if(C.inexact){let W;if(D(C,w.Dangling)){let X=D(C,w.Line);W=[t(m,E,!0),X||h(E.originalText,A(c(v(C))))?o:s,"..."]}else W=["..."];K.push([...O,...W])}let se=c(C[_]),Q=!(C.inexact||se&&se.type==="RestElement"||se&&(se.type==="TSPropertySignature"||se.type==="TSCallSignatureDeclaration"||se.type==="TSMethodSignature"||se.type==="TSConstructSignatureDeclaration")&&D(se,w.PrettierIgnore)),le;if(K.length===0){if(!D(C,w.Dangling))return[L,Y,k(m,E,l)];le=r([L,t(m,E),a,Y,B(m),k(m,E,l)])}else le=[M&&g(C.properties)?f($):"",L,u([E.bracketSpacing?s:a,...K]),i(Q&&(J!==","||p(E))?J:""),E.bracketSpacing?s:a,Y,B(m),k(m,E,l)];return m.match(W=>W.type==="ObjectPattern"&&!W.decorators,(W,X,oe)=>P(W)&&(X==="params"||X==="parameters"||X==="this"||X==="rest")&&oe===0)||m.match(R,(W,X)=>X==="typeAnnotation",(W,X)=>X==="typeAnnotation",(W,X,oe)=>P(W)&&(X==="params"||X==="parameters"||X==="this"||X==="rest")&&oe===0)||!q&&m.match(W=>W.type==="ObjectPattern",W=>W.type==="AssignmentExpression"||W.type==="VariableDeclarator")?le:r(le,{shouldBreak:q})}n.exports={printObject:x}}}),Im=ee({"src/language-js/print/flow.js"(e,n){"use strict";re();var t=Xt(),{printDanglingComments:s}=et(),{printString:a,printNumber:r}=Ge(),{builders:{hardline:u,softline:i,group:o,indent:c}}=qe(),{getParentExportDeclaration:y,isFunctionNotation:h,isGetterOrSetter:g,rawText:p,shouldPrintComma:D}=Ke(),{locStart:v,locEnd:w}=it(),{replaceTextEndOfLine:T}=Jt(),{printClass:F}=Zt(),{printOpaqueType:A,printTypeAlias:B,printIntersectionType:k,printUnionType:P,printFunctionType:R,printTupleType:f,printIndexedAccessType:x}=kr(),{printInterface:m}=Ya(),{printTypeParameter:E,printTypeParameters:l}=Ir(),{printExportDeclaration:d,printExportAllDeclaration:C}=Qa(),{printArrayItems:_}=Kt(),{printObject:b}=Un(),{printPropertyKey:N}=Qt(),{printOptionalToken:I,printTypeAnnotation:$,printRestSpread:M}=Dt();function q(L,Y,V){let O=L.getValue(),K=Y.semi?";":"",se=[];switch(O.type){case"DeclareClass":return J(L,F(L,Y,V));case"DeclareFunction":return J(L,["function ",V("id"),O.predicate?" ":"",V("predicate"),K]);case"DeclareModule":return J(L,["module ",V("id")," ",V("body")]);case"DeclareModuleExports":return J(L,["module.exports",": ",V("typeAnnotation"),K]);case"DeclareVariable":return J(L,["var ",V("id"),K]);case"DeclareOpaqueType":return J(L,A(L,Y,V));case"DeclareInterface":return J(L,m(L,Y,V));case"DeclareTypeAlias":return J(L,B(L,Y,V));case"DeclareExportDeclaration":return J(L,d(L,Y,V));case"DeclareExportAllDeclaration":return J(L,C(L,Y,V));case"OpaqueType":return A(L,Y,V);case"TypeAlias":return B(L,Y,V);case"IntersectionTypeAnnotation":return k(L,Y,V);case"UnionTypeAnnotation":return P(L,Y,V);case"FunctionTypeAnnotation":return R(L,Y,V);case"TupleTypeAnnotation":return f(L,Y,V);case"GenericTypeAnnotation":return[V("id"),l(L,Y,V,"typeParameters")];case"IndexedAccessType":case"OptionalIndexedAccessType":return x(L,Y,V);case"TypeAnnotation":return V("typeAnnotation");case"TypeParameter":return E(L,Y,V);case"TypeofTypeAnnotation":return["typeof ",V("argument")];case"ExistsTypeAnnotation":return"*";case"EmptyTypeAnnotation":return"empty";case"MixedTypeAnnotation":return"mixed";case"ArrayTypeAnnotation":return[V("elementType"),"[]"];case"BooleanLiteralTypeAnnotation":return String(O.value);case"EnumDeclaration":return["enum ",V("id")," ",V("body")];case"EnumBooleanBody":case"EnumNumberBody":case"EnumStringBody":case"EnumSymbolBody":{if(O.type==="EnumSymbolBody"||O.explicitType){let Q=null;switch(O.type){case"EnumBooleanBody":Q="boolean";break;case"EnumNumberBody":Q="number";break;case"EnumStringBody":Q="string";break;case"EnumSymbolBody":Q="symbol";break}se.push("of ",Q," ")}if(O.members.length===0&&!O.hasUnknownMembers)se.push(o(["{",s(L,Y),i,"}"]));else{let Q=O.members.length>0?[u,_(L,Y,"members",V),O.hasUnknownMembers||D(Y)?",":""]:[];se.push(o(["{",c([...Q,...O.hasUnknownMembers?[u,"..."]:[]]),s(L,Y,!0),u,"}"]))}return se}case"EnumBooleanMember":case"EnumNumberMember":case"EnumStringMember":return[V("id")," = ",typeof O.init=="object"?V("init"):String(O.init)];case"EnumDefaultedMember":return V("id");case"FunctionTypeParam":{let Q=O.name?V("name"):L.getParentNode().this===O?"this":"";return[Q,I(L),Q?": ":"",V("typeAnnotation")]}case"InterfaceDeclaration":case"InterfaceTypeAnnotation":return m(L,Y,V);case"ClassImplements":case"InterfaceExtends":return[V("id"),V("typeParameters")];case"NullableTypeAnnotation":return["?",V("typeAnnotation")];case"Variance":{let{kind:Q}=O;return t.ok(Q==="plus"||Q==="minus"),Q==="plus"?"+":"-"}case"ObjectTypeCallProperty":return O.static&&se.push("static "),se.push(V("value")),se;case"ObjectTypeIndexer":return[O.static?"static ":"",O.variance?V("variance"):"","[",V("id"),O.id?": ":"",V("key"),"]: ",V("value")];case"ObjectTypeProperty":{let Q="";return O.proto?Q="proto ":O.static&&(Q="static "),[Q,g(O)?O.kind+" ":"",O.variance?V("variance"):"",N(L,Y,V),I(L),h(O)?"":": ",V("value")]}case"ObjectTypeAnnotation":return b(L,Y,V);case"ObjectTypeInternalSlot":return[O.static?"static ":"","[[",V("id"),"]]",I(L),O.method?"":": ",V("value")];case"ObjectTypeSpreadProperty":return M(L,Y,V);case"QualifiedTypeofIdentifier":case"QualifiedTypeIdentifier":return[V("qualification"),".",V("id")];case"StringLiteralTypeAnnotation":return T(a(p(O),Y));case"NumberLiteralTypeAnnotation":t.strictEqual(typeof O.value,"number");case"BigIntLiteralTypeAnnotation":return O.extra?r(O.extra.raw):r(O.raw);case"TypeCastExpression":return["(",V("expression"),$(L,Y,V),")"];case"TypeParameterDeclaration":case"TypeParameterInstantiation":{let Q=l(L,Y,V,"params");if(Y.parser==="flow"){let le=v(O),W=w(O),X=Y.originalText.lastIndexOf("/*",le),oe=Y.originalText.indexOf("*/",W);if(X!==-1&&oe!==-1){let ae=Y.originalText.slice(X+2,oe).trim();if(ae.startsWith("::")&&!ae.includes("/*")&&!ae.includes("*/"))return["/*:: ",Q," */"]}}return Q}case"InferredPredicate":return"%checks";case"DeclaredPredicate":return["%checks(",V("value"),")"];case"AnyTypeAnnotation":return"any";case"BooleanTypeAnnotation":return"boolean";case"BigIntTypeAnnotation":return"bigint";case"NullLiteralTypeAnnotation":return"null";case"NumberTypeAnnotation":return"number";case"SymbolTypeAnnotation":return"symbol";case"StringTypeAnnotation":return"string";case"VoidTypeAnnotation":return"void";case"ThisTypeAnnotation":return"this";case"Node":case"Printable":case"SourceLocation":case"Position":case"Statement":case"Function":case"Pattern":case"Expression":case"Declaration":case"Specifier":case"NamedSpecifier":case"Comment":case"MemberTypeAnnotation":case"Type":throw new Error("unprintable type: "+JSON.stringify(O.type))}}function J(L,Y){let V=y(L);return V?(t.strictEqual(V.type,"DeclareExportDeclaration"),Y):["declare ",Y]}n.exports={printFlow:q}}}),Lm=ee({"src/language-js/utils/is-ts-keyword-type.js"(e,n){"use strict";re();function t(s){let{type:a}=s;return a.startsWith("TS")&&a.endsWith("Keyword")}n.exports=t}}),Za=ee({"src/language-js/print/ternary.js"(e,n){"use strict";re();var{hasNewlineInRange:t}=Ge(),{isJsxNode:s,getComments:a,isCallExpression:r,isMemberExpression:u,isTSTypeExpression:i}=Ke(),{locStart:o,locEnd:c}=it(),y=kt(),{builders:{line:h,softline:g,group:p,indent:D,align:v,ifBreak:w,dedent:T,breakParent:F}}=qe();function A(f){let x=[f];for(let m=0;m<x.length;m++){let E=x[m];for(let l of["test","consequent","alternate"]){let d=E[l];if(s(d))return!0;d.type==="ConditionalExpression"&&x.push(d)}}return!1}function B(f,x,m){let E=f.getValue(),l=E.type==="ConditionalExpression",d=l?"alternate":"falseType",C=f.getParentNode(),_=l?m("test"):[m("checkType")," ","extends"," ",m("extendsType")];return C.type===E.type&&C[d]===E?v(2,_):_}var k=new Map([["AssignmentExpression","right"],["VariableDeclarator","init"],["ReturnStatement","argument"],["ThrowStatement","argument"],["UnaryExpression","argument"],["YieldExpression","argument"]]);function P(f){let x=f.getValue();if(x.type!=="ConditionalExpression")return!1;let m,E=x;for(let l=0;!m;l++){let d=f.getParentNode(l);if(r(d)&&d.callee===E||u(d)&&d.object===E||d.type==="TSNonNullExpression"&&d.expression===E){E=d;continue}d.type==="NewExpression"&&d.callee===E||i(d)&&d.expression===E?(m=f.getParentNode(l+1),E=d):m=d}return E===x?!1:m[k.get(m.type)]===E}function R(f,x,m){let E=f.getValue(),l=E.type==="ConditionalExpression",d=l?"consequent":"trueType",C=l?"alternate":"falseType",_=l?["test"]:["checkType","extendsType"],b=E[d],N=E[C],I=[],$=!1,M=f.getParentNode(),q=M.type===E.type&&_.some(ae=>M[ae]===E),J=M.type===E.type&&!q,L,Y,V=0;do Y=L||E,L=f.getParentNode(V),V++;while(L&&L.type===E.type&&_.every(ae=>L[ae]!==Y));let O=L||M,K=Y;if(l&&(s(E[_[0]])||s(b)||s(N)||A(K))){$=!0,J=!0;let ae=z=>[w("("),D([g,z]),g,w(")")],Ae=z=>z.type==="NullLiteral"||z.type==="Literal"&&z.value===null||z.type==="Identifier"&&z.name==="undefined";I.push(" ? ",Ae(b)?m(d):ae(m(d))," : ",N.type===E.type||Ae(N)?m(C):ae(m(C)))}else{let ae=[h,"? ",b.type===E.type?w("","("):"",v(2,m(d)),b.type===E.type?w("",")"):"",h,": ",N.type===E.type?m(C):v(2,m(C))];I.push(M.type!==E.type||M[C]===E||q?ae:x.useTabs?T(D(ae)):v(Math.max(0,x.tabWidth-2),ae))}let Q=[..._.map(ae=>a(E[ae])),a(b),a(N)].flat().some(ae=>y(ae)&&t(x.originalText,o(ae),c(ae))),le=ae=>M===O?p(ae,{shouldBreak:Q}):Q?[ae,F]:ae,W=!$&&(u(M)||M.type==="NGPipeExpression"&&M.left===E)&&!M.computed,X=P(f),oe=le([B(f,x,m),J?I:D(I),l&&W&&!X?g:""]);return q||X?p([D([g,oe]),g]):oe}n.exports={printTernary:R}}}),eo=ee({"src/language-js/print/statement.js"(e,n){"use strict";re();var{builders:{hardline:t}}=qe(),s=Lt(),{getLeftSidePathName:a,hasNakedLeftSide:r,isJsxNode:u,isTheOnlyJsxElementInMarkdown:i,hasComment:o,CommentCheckFlags:c,isNextLineEmpty:y}=Ke(),{shouldPrintParamsWithoutParens:h}=Lr();function g(B,k,P,R){let f=B.getValue(),x=[],m=f.type==="ClassBody",E=p(f[R]);return B.each((l,d,C)=>{let _=l.getValue();if(_.type==="EmptyStatement")return;let b=P();!k.semi&&!m&&!i(k,l)&&D(l,k)?o(_,c.Leading)?x.push(P([],{needsSemi:!0})):x.push(";",b):x.push(b),!k.semi&&m&&F(_)&&A(_,C[d+1])&&x.push(";"),_!==E&&(x.push(t),y(_,k)&&x.push(t))},R),x}function p(B){for(let k=B.length-1;k>=0;k--){let P=B[k];if(P.type!=="EmptyStatement")return P}}function D(B,k){return B.getNode().type!=="ExpressionStatement"?!1:B.call(R=>v(R,k),"expression")}function v(B,k){let P=B.getValue();switch(P.type){case"ParenthesizedExpression":case"TypeCastExpression":case"ArrayExpression":case"ArrayPattern":case"TemplateLiteral":case"TemplateElement":case"RegExpLiteral":return!0;case"ArrowFunctionExpression":{if(!h(B,k))return!0;break}case"UnaryExpression":{let{prefix:R,operator:f}=P;if(R&&(f==="+"||f==="-"))return!0;break}case"BindExpression":{if(!P.object)return!0;break}case"Literal":{if(P.regex)return!0;break}default:if(u(P))return!0}return s(B,k)?!0:r(P)?B.call(R=>v(R,k),...a(B,P)):!1}function w(B,k,P){return g(B,k,P,"body")}function T(B,k,P){return g(B,k,P,"consequent")}var F=B=>{let{type:k}=B;return k==="ClassProperty"||k==="PropertyDefinition"||k==="ClassPrivateProperty"||k==="ClassAccessorProperty"};function A(B,k){let P=B.key&&B.key.name;if((P==="static"||P==="get"||P==="set")&&!B.value&&!B.typeAnnotation)return!0;if(!k||k.static||k.accessibility)return!1;if(!k.computed){let R=k.key&&k.key.name;if(R==="in"||R==="instanceof")return!0}if(F(k)&&k.variance&&!k.static&&!k.declare)return!0;switch(k.type){case"ClassProperty":case"PropertyDefinition":case"TSAbstractPropertyDefinition":return k.computed;case"MethodDefinition":case"TSAbstractMethodDefinition":case"ClassMethod":case"ClassPrivateMethod":{if((k.value?k.value.async:k.async)||k.kind==="get"||k.kind==="set")return!1;let f=k.value?k.value.generator:k.generator;return!!(k.computed||f)}case"TSIndexSignature":return!0}return!1}n.exports={printBody:w,printSwitchCaseConsequent:T}}}),to=ee({"src/language-js/print/block.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{isNonEmptyArray:s}=Ge(),{builders:{hardline:a,indent:r}}=qe(),{hasComment:u,CommentCheckFlags:i,isNextLineEmpty:o}=Ke(),{printHardlineAfterHeritage:c}=Zt(),{printBody:y}=eo();function h(p,D,v){let w=p.getValue(),T=[];if(w.type==="StaticBlock"&&T.push("static "),w.type==="ClassBody"&&s(w.body)){let A=p.getParentNode();T.push(c(A))}T.push("{");let F=g(p,D,v);if(F)T.push(r([a,F]),a);else{let A=p.getParentNode(),B=p.getParentNode(1);A.type==="ArrowFunctionExpression"||A.type==="FunctionExpression"||A.type==="FunctionDeclaration"||A.type==="ObjectMethod"||A.type==="ClassMethod"||A.type==="ClassPrivateMethod"||A.type==="ForStatement"||A.type==="WhileStatement"||A.type==="DoWhileStatement"||A.type==="DoExpression"||A.type==="CatchClause"&&!B.finalizer||A.type==="TSModuleDeclaration"||A.type==="TSDeclareFunction"||w.type==="StaticBlock"||w.type==="ClassBody"||T.push(a)}return T.push("}"),T}function g(p,D,v){let w=p.getValue(),T=s(w.directives),F=w.body.some(k=>k.type!=="EmptyStatement"),A=u(w,i.Dangling);if(!T&&!F&&!A)return"";let B=[];if(T&&p.each((k,P,R)=>{B.push(v()),(P<R.length-1||F||A)&&(B.push(a),o(k.getValue(),D)&&B.push(a))},"directives"),F&&B.push(y(p,D,v)),A&&B.push(t(p,D,!0)),w.type==="Program"){let k=p.getParentNode();(!k||k.type!=="ModuleExpression")&&B.push(a)}return B}n.exports={printBlock:h,printBlockBody:g}}}),jm=ee({"src/language-js/print/typescript.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{hasNewlineInRange:s}=Ge(),{builders:{join:a,line:r,hardline:u,softline:i,group:o,indent:c,conditionalGroup:y,ifBreak:h}}=qe(),{isLiteral:g,getTypeScriptMappedTypeModifier:p,shouldPrintComma:D,isCallExpression:v,isMemberExpression:w}=Ke(),T=Lm(),{locStart:F,locEnd:A}=it(),{printOptionalToken:B,printTypeScriptModifiers:k}=Dt(),{printTernary:P}=Za(),{printFunctionParameters:R,shouldGroupFunctionParameters:f}=Pr(),{printTemplateLiteral:x}=It(),{printArrayItems:m}=Kt(),{printObject:E}=Un(),{printClassProperty:l,printClassMethod:d}=Zt(),{printTypeParameter:C,printTypeParameters:_}=Ir(),{printPropertyKey:b}=Qt(),{printFunction:N,printMethodInternal:I}=Lr(),{printInterface:$}=Ya(),{printBlock:M}=to(),{printTypeAlias:q,printIntersectionType:J,printUnionType:L,printFunctionType:Y,printTupleType:V,printIndexedAccessType:O,printJSDocType:K}=kr();function se(Q,le,W){let X=Q.getValue();if(!X.type.startsWith("TS"))return;if(T(X))return X.type.slice(2,-7).toLowerCase();let oe=le.semi?";":"",ae=[];switch(X.type){case"TSThisType":return"this";case"TSTypeAssertion":{let Ae=!(X.expression.type==="ArrayExpression"||X.expression.type==="ObjectExpression"),z=o(["<",c([i,W("typeAnnotation")]),i,">"]),H=[h("("),c([i,W("expression")]),i,h(")")];return Ae?y([[z,W("expression")],[z,o(H,{shouldBreak:!0})],[z,W("expression")]]):o([z,W("expression")])}case"TSDeclareFunction":return N(Q,W,le);case"TSExportAssignment":return["export = ",W("expression"),oe];case"TSModuleBlock":return M(Q,le,W);case"TSInterfaceBody":case"TSTypeLiteral":return E(Q,le,W);case"TSTypeAliasDeclaration":return q(Q,le,W);case"TSQualifiedName":return a(".",[W("left"),W("right")]);case"TSAbstractMethodDefinition":case"TSDeclareMethod":return d(Q,le,W);case"TSAbstractPropertyDefinition":return l(Q,le,W);case"TSInterfaceHeritage":case"TSExpressionWithTypeArguments":return ae.push(W("expression")),X.typeParameters&&ae.push(W("typeParameters")),ae;case"TSTemplateLiteralType":return x(Q,W,le);case"TSNamedTupleMember":return[W("label"),X.optional?"?":"",": ",W("elementType")];case"TSRestType":return["...",W("typeAnnotation")];case"TSOptionalType":return[W("typeAnnotation"),"?"];case"TSInterfaceDeclaration":return $(Q,le,W);case"TSClassImplements":return[W("expression"),W("typeParameters")];case"TSTypeParameterDeclaration":case"TSTypeParameterInstantiation":return _(Q,le,W,"params");case"TSTypeParameter":return C(Q,le,W);case"TSSatisfiesExpression":case"TSAsExpression":{let Ae=X.type==="TSAsExpression"?"as":"satisfies";ae.push(W("expression"),` ${Ae} `,W("typeAnnotation"));let z=Q.getParentNode();return v(z)&&z.callee===X||w(z)&&z.object===X?o([c([i,...ae]),i]):ae}case"TSArrayType":return[W("elementType"),"[]"];case"TSPropertySignature":return X.readonly&&ae.push("readonly "),ae.push(b(Q,le,W),B(Q)),X.typeAnnotation&&ae.push(": ",W("typeAnnotation")),X.initializer&&ae.push(" = ",W("initializer")),ae;case"TSParameterProperty":return X.accessibility&&ae.push(X.accessibility+" "),X.export&&ae.push("export "),X.static&&ae.push("static "),X.override&&ae.push("override "),X.readonly&&ae.push("readonly "),ae.push(W("parameter")),ae;case"TSTypeQuery":return["typeof ",W("exprName"),W("typeParameters")];case"TSIndexSignature":{let Ae=Q.getParentNode(),z=X.parameters.length>1?h(D(le)?",":""):"",H=o([c([i,a([", ",i],Q.map(W,"parameters"))]),z,i]);return[X.export?"export ":"",X.accessibility?[X.accessibility," "]:"",X.static?"static ":"",X.readonly?"readonly ":"",X.declare?"declare ":"","[",X.parameters?H:"",X.typeAnnotation?"]: ":"]",X.typeAnnotation?W("typeAnnotation"):"",Ae.type==="ClassBody"?oe:""]}case"TSTypePredicate":return[X.asserts?"asserts ":"",W("parameterName"),X.typeAnnotation?[" is ",W("typeAnnotation")]:""];case"TSNonNullExpression":return[W("expression"),"!"];case"TSImportType":return[X.isTypeOf?"typeof ":"","import(",W(X.parameter?"parameter":"argument"),")",X.qualifier?[".",W("qualifier")]:"",_(Q,le,W,"typeParameters")];case"TSLiteralType":return W("literal");case"TSIndexedAccessType":return O(Q,le,W);case"TSConstructSignatureDeclaration":case"TSCallSignatureDeclaration":case"TSConstructorType":{if(X.type==="TSConstructorType"&&X.abstract&&ae.push("abstract "),X.type!=="TSCallSignatureDeclaration"&&ae.push("new "),ae.push(o(R(Q,W,le,!1,!0))),X.returnType||X.typeAnnotation){let Ae=X.type==="TSConstructorType";ae.push(Ae?" => ":": ",W("returnType"),W("typeAnnotation"))}return ae}case"TSTypeOperator":return[X.operator," ",W("typeAnnotation")];case"TSMappedType":{let Ae=s(le.originalText,F(X),A(X));return o(["{",c([le.bracketSpacing?r:i,X.readonly?[p(X.readonly,"readonly")," "]:"",k(Q,le,W),W("typeParameter"),X.optional?p(X.optional,"?"):"",X.typeAnnotation?": ":"",W("typeAnnotation"),h(oe)]),t(Q,le,!0),le.bracketSpacing?r:i,"}"],{shouldBreak:Ae})}case"TSMethodSignature":{let Ae=X.kind&&X.kind!=="method"?`${X.kind} `:"";ae.push(X.accessibility?[X.accessibility," "]:"",Ae,X.export?"export ":"",X.static?"static ":"",X.readonly?"readonly ":"",X.abstract?"abstract ":"",X.declare?"declare ":"",X.computed?"[":"",W("key"),X.computed?"]":"",B(Q));let z=R(Q,W,le,!1,!0),H=X.returnType?"returnType":"typeAnnotation",Z=X[H],ne=Z?W(H):"",fe=f(X,ne);return ae.push(fe?o(z):z),Z&&ae.push(": ",o(ne)),o(ae)}case"TSNamespaceExportDeclaration":return ae.push("export as namespace ",W("id")),le.semi&&ae.push(";"),o(ae);case"TSEnumDeclaration":return X.declare&&ae.push("declare "),X.modifiers&&ae.push(k(Q,le,W)),X.const&&ae.push("const "),ae.push("enum ",W("id")," "),X.members.length===0?ae.push(o(["{",t(Q,le),i,"}"])):ae.push(o(["{",c([u,m(Q,le,"members",W),D(le,"es5")?",":""]),t(Q,le,!0),u,"}"])),ae;case"TSEnumMember":return X.computed?ae.push("[",W("id"),"]"):ae.push(W("id")),X.initializer&&ae.push(" = ",W("initializer")),ae;case"TSImportEqualsDeclaration":return X.isExport&&ae.push("export "),ae.push("import "),X.importKind&&X.importKind!=="value"&&ae.push(X.importKind," "),ae.push(W("id")," = ",W("moduleReference")),le.semi&&ae.push(";"),o(ae);case"TSExternalModuleReference":return["require(",W("expression"),")"];case"TSModuleDeclaration":{let Ae=Q.getParentNode(),z=g(X.id),H=Ae.type==="TSModuleDeclaration",Z=X.body&&X.body.type==="TSModuleDeclaration";if(H)ae.push(".");else{X.declare&&ae.push("declare "),ae.push(k(Q,le,W));let ne=le.originalText.slice(F(X),F(X.id));X.id.type==="Identifier"&&X.id.name==="global"&&!/namespace|module/.test(ne)||ae.push(z||/(?:^|\s)module(?:\s|$)/.test(ne)?"module ":"namespace ")}return ae.push(W("id")),Z?ae.push(W("body")):X.body?ae.push(" ",o(W("body"))):ae.push(oe),ae}case"TSConditionalType":return P(Q,le,W);case"TSInferType":return["infer"," ",W("typeParameter")];case"TSIntersectionType":return J(Q,le,W);case"TSUnionType":return L(Q,le,W);case"TSFunctionType":return Y(Q,le,W);case"TSTupleType":return V(Q,le,W);case"TSTypeReference":return[W("typeName"),_(Q,le,W,"typeParameters")];case"TSTypeAnnotation":return W("typeAnnotation");case"TSEmptyBodyFunctionExpression":return I(Q,le,W);case"TSJSDocAllType":return"*";case"TSJSDocUnknownType":return"?";case"TSJSDocNullableType":return K(Q,W,"?");case"TSJSDocNonNullableType":return K(Q,W,"!");case"TSInstantiationExpression":return[W("expression"),W("typeParameters")];default:throw new Error(`Unknown TypeScript node type: ${JSON.stringify(X.type)}.`)}}n.exports={printTypescript:se}}}),Om=ee({"src/language-js/print/comment.js"(e,n){"use strict";re();var{hasNewline:t}=Ge(),{builders:{join:s,hardline:a},utils:{replaceTextEndOfLine:r}}=qe(),{isLineComment:u}=Ke(),{locStart:i,locEnd:o}=it(),c=kt();function y(p,D){let v=p.getValue();if(u(v))return D.originalText.slice(i(v),o(v)).trimEnd();if(c(v)){if(h(v)){let F=g(v);return v.trailing&&!t(D.originalText,i(v),{backwards:!0})?[a,F]:F}let w=o(v),T=D.originalText.slice(w-3,w)==="*-/";return["/*",r(v.value),T?"*-/":"*/"]}throw new Error("Not a comment: "+JSON.stringify(v))}function h(p){let D=`*${p.value}*`.split(`
`);return D.length>1&&D.every(v=>v.trim()[0]==="*")}function g(p){let D=p.value.split(`
`);return["/*",s(a,D.map((v,w)=>w===0?v.trimEnd():" "+(w<D.length-1?v.trim():v.trimStart()))),"*/"]}n.exports={printComment:y}}}),qm=ee({"src/language-js/print/literal.js"(e,n){"use strict";re();var{printString:t,printNumber:s}=Ge(),{replaceTextEndOfLine:a}=Jt();function r(o,c){let y=o.getNode();switch(y.type){case"RegExpLiteral":return i(y);case"BigIntLiteral":return u(y.bigint||y.extra.raw);case"NumericLiteral":return s(y.extra.raw);case"StringLiteral":return a(t(y.extra.raw,c));case"NullLiteral":return"null";case"BooleanLiteral":return String(y.value);case"DecimalLiteral":return s(y.value)+"m";case"Literal":{if(y.regex)return i(y.regex);if(y.bigint)return u(y.raw);if(y.decimal)return s(y.decimal)+"m";let{value:h}=y;return typeof h=="number"?s(y.raw):typeof h=="string"?a(t(y.raw,c)):String(h)}}}function u(o){return o.toLowerCase()}function i(o){let{pattern:c,flags:y}=o;return y=[...y].sort().join(""),`/${c}/${y}`}n.exports={printLiteral:r}}}),Mm=ee({"src/language-js/printer-estree.js"(e,n){"use strict";re();var{printDanglingComments:t}=et(),{hasNewline:s}=Ge(),{builders:{join:a,line:r,hardline:u,softline:i,group:o,indent:c},utils:{replaceTextEndOfLine:y}}=qe(),h=Em(),g=Fm(),{insertPragma:p}=Ga(),D=Ua(),v=Lt(),w=Ja(),{hasFlowShorthandAnnotationComment:T,hasComment:F,CommentCheckFlags:A,isTheOnlyJsxElementInMarkdown:B,isLineComment:k,isNextLineEmpty:P,needsHardlineAfterDanglingComment:R,rawText:f,hasIgnoreComment:x,isCallExpression:m,isMemberExpression:E,markerForIfWithoutBlockAndSameLineComment:l}=Ke(),{locStart:d,locEnd:C}=it(),_=kt(),{printHtmlBinding:b,isVueEventBindingExpression:N}=wm(),{printAngular:I}=_m(),{printJsx:$,hasJsxIgnoreComment:M}=Pm(),{printFlow:q}=Im(),{printTypescript:J}=jm(),{printOptionalToken:L,printBindExpressionCallee:Y,printTypeAnnotation:V,adjustClause:O,printRestSpread:K,printDefiniteToken:se}=Dt(),{printImportDeclaration:Q,printExportDeclaration:le,printExportAllDeclaration:W,printModuleSpecifier:X}=Qa(),{printTernary:oe}=Za(),{printTemplateLiteral:ae}=It(),{printArray:Ae}=Kt(),{printObject:z}=Un(),{printClass:H,printClassMethod:Z,printClassProperty:ne}=Zt(),{printProperty:fe}=Qt(),{printFunction:ge,printArrowFunction:Ce,printMethod:_e,printReturnStatement:Oe,printThrowStatement:pe}=Lr(),{printCallExpression:ie}=Ka(),{printVariableDeclarator:ve,printAssignmentExpression:ce}=Yt(),{printBinaryishExpression:U}=Hn(),{printSwitchCaseConsequent:de}=eo(),{printMemberExpression:De}=Xa(),{printBlock:he,printBlockBody:Be}=to(),{printComment:Se}=Om(),{printLiteral:ye}=qm(),{printDecorators:S}=Gn();function G(Te,Pe,Fe,Ze){let xe=te(Te,Pe,Fe,Ze);if(!xe)return"";let Je=Te.getValue(),{type:Ne}=Je;if(Ne==="ClassMethod"||Ne==="ClassPrivateMethod"||Ne==="ClassProperty"||Ne==="ClassAccessorProperty"||Ne==="PropertyDefinition"||Ne==="TSAbstractPropertyDefinition"||Ne==="ClassPrivateProperty"||Ne==="MethodDefinition"||Ne==="TSAbstractMethodDefinition"||Ne==="TSDeclareMethod")return xe;let Le=[xe],Ve=S(Te,Pe,Fe),be=Je.type==="ClassExpression"&&Ve;if(Ve&&(Le=[...Ve,xe],!be))return o(Le);if(!v(Te,Pe))return Ze&&Ze.needsSemi&&Le.unshift(";"),Le.length===1&&Le[0]===xe?xe:Le;if(be&&(Le=[c([r,...Le])]),Le.unshift("("),Ze&&Ze.needsSemi&&Le.unshift(";"),T(Je)){let[Me]=Je.trailingComments;Le.push(" /*",Me.value.trimStart(),"*/"),Me.printed=!0}return be&&Le.push(r),Le.push(")"),Le}function te(Te,Pe,Fe,Ze){let xe=Te.getValue(),Je=Pe.semi?";":"";if(!xe)return"";if(typeof xe=="string")return xe;for(let Le of[ye,b,I,$,q,J]){let Ve=Le(Te,Pe,Fe);if(typeof Ve<"u")return Ve}let Ne=[];switch(xe.type){case"JsExpressionRoot":return Fe("node");case"JsonRoot":return[Fe("node"),u];case"File":return xe.program&&xe.program.interpreter&&Ne.push(Fe(["program","interpreter"])),Ne.push(Fe("program")),Ne;case"Program":return Be(Te,Pe,Fe);case"EmptyStatement":return"";case"ExpressionStatement":{if(xe.directive)return[Ee(xe.expression,Pe),Je];if(Pe.parser==="__vue_event_binding"||Pe.parser==="__vue_ts_event_binding"){let Ve=Te.getParentNode();if(Ve.type==="Program"&&Ve.body.length===1&&Ve.body[0]===xe)return[Fe("expression"),N(xe.expression)?";":""]}let Le=t(Te,Pe,!0,Ve=>{let{marker:be}=Ve;return be===l});return[Fe("expression"),B(Pe,Te)?"":Je,Le?[" ",Le]:""]}case"ParenthesizedExpression":return!F(xe.expression)&&(xe.expression.type==="ObjectExpression"||xe.expression.type==="ArrayExpression")?["(",Fe("expression"),")"]:o(["(",c([i,Fe("expression")]),i,")"]);case"AssignmentExpression":return ce(Te,Pe,Fe);case"VariableDeclarator":return ve(Te,Pe,Fe);case"BinaryExpression":case"LogicalExpression":return U(Te,Pe,Fe);case"AssignmentPattern":return[Fe("left")," = ",Fe("right")];case"OptionalMemberExpression":case"MemberExpression":return De(Te,Pe,Fe);case"MetaProperty":return[Fe("meta"),".",Fe("property")];case"BindExpression":return xe.object&&Ne.push(Fe("object")),Ne.push(o(c([i,Y(Te,Pe,Fe)]))),Ne;case"Identifier":return[xe.name,L(Te),se(Te),V(Te,Pe,Fe)];case"V8IntrinsicIdentifier":return["%",xe.name];case"SpreadElement":case"SpreadElementPattern":case"SpreadProperty":case"SpreadPropertyPattern":case"RestElement":return K(Te,Pe,Fe);case"FunctionDeclaration":case"FunctionExpression":return ge(Te,Fe,Pe,Ze);case"ArrowFunctionExpression":return Ce(Te,Pe,Fe,Ze);case"YieldExpression":return Ne.push("yield"),xe.delegate&&Ne.push("*"),xe.argument&&Ne.push(" ",Fe("argument")),Ne;case"AwaitExpression":{if(Ne.push("await"),xe.argument){Ne.push(" ",Fe("argument"));let Le=Te.getParentNode();if(m(Le)&&Le.callee===xe||E(Le)&&Le.object===xe){Ne=[c([i,...Ne]),i];let Ve=Te.findAncestor(be=>be.type==="AwaitExpression"||be.type==="BlockStatement");if(!Ve||Ve.type!=="AwaitExpression")return o(Ne)}}return Ne}case"ExportDefaultDeclaration":case"ExportNamedDeclaration":return le(Te,Pe,Fe);case"ExportAllDeclaration":return W(Te,Pe,Fe);case"ImportDeclaration":return Q(Te,Pe,Fe);case"ImportSpecifier":case"ExportSpecifier":case"ImportNamespaceSpecifier":case"ExportNamespaceSpecifier":case"ImportDefaultSpecifier":case"ExportDefaultSpecifier":return X(Te,Pe,Fe);case"ImportAttribute":return[Fe("key"),": ",Fe("value")];case"Import":return"import";case"BlockStatement":case"StaticBlock":case"ClassBody":return he(Te,Pe,Fe);case"ThrowStatement":return pe(Te,Pe,Fe);case"ReturnStatement":return Oe(Te,Pe,Fe);case"NewExpression":case"ImportExpression":case"OptionalCallExpression":case"CallExpression":return ie(Te,Pe,Fe);case"ObjectExpression":case"ObjectPattern":case"RecordExpression":return z(Te,Pe,Fe);case"ObjectProperty":case"Property":return xe.method||xe.kind==="get"||xe.kind==="set"?_e(Te,Pe,Fe):fe(Te,Pe,Fe);case"ObjectMethod":return _e(Te,Pe,Fe);case"Decorator":return["@",Fe("expression")];case"ArrayExpression":case"ArrayPattern":case"TupleExpression":return Ae(Te,Pe,Fe);case"SequenceExpression":{let Le=Te.getParentNode(0);if(Le.type==="ExpressionStatement"||Le.type==="ForStatement"){let Ve=[];return Te.each((be,Ie)=>{Ie===0?Ve.push(Fe()):Ve.push(",",c([r,Fe()]))},"expressions"),o(Ve)}return o(a([",",r],Te.map(Fe,"expressions")))}case"ThisExpression":return"this";case"Super":return"super";case"Directive":return[Fe("value"),Je];case"DirectiveLiteral":return Ee(xe,Pe);case"UnaryExpression":return Ne.push(xe.operator),/[a-z]$/.test(xe.operator)&&Ne.push(" "),F(xe.argument)?Ne.push(o(["(",c([i,Fe("argument")]),i,")"])):Ne.push(Fe("argument")),Ne;case"UpdateExpression":return Ne.push(Fe("argument"),xe.operator),xe.prefix&&Ne.reverse(),Ne;case"ConditionalExpression":return oe(Te,Pe,Fe);case"VariableDeclaration":{let Le=Te.map(Fe,"declarations"),Ve=Te.getParentNode(),be=Ve.type==="ForStatement"||Ve.type==="ForInStatement"||Ve.type==="ForOfStatement",Ie=xe.declarations.some(ue=>ue.init),Me;return Le.length===1&&!F(xe.declarations[0])?Me=Le[0]:Le.length>0&&(Me=c(Le[0])),Ne=[xe.declare?"declare ":"",xe.kind,Me?[" ",Me]:"",c(Le.slice(1).map(ue=>[",",Ie&&!be?u:r,ue]))],be&&Ve.body!==xe||Ne.push(Je),o(Ne)}case"WithStatement":return o(["with (",Fe("object"),")",O(xe.body,Fe("body"))]);case"IfStatement":{let Le=O(xe.consequent,Fe("consequent")),Ve=o(["if (",o([c([i,Fe("test")]),i]),")",Le]);if(Ne.push(Ve),xe.alternate){let be=F(xe.consequent,A.Trailing|A.Line)||R(xe),Ie=xe.consequent.type==="BlockStatement"&&!be;Ne.push(Ie?" ":u),F(xe,A.Dangling)&&Ne.push(t(Te,Pe,!0),be?u:" "),Ne.push("else",o(O(xe.alternate,Fe("alternate"),xe.alternate.type==="IfStatement")))}return Ne}case"ForStatement":{let Le=O(xe.body,Fe("body")),Ve=t(Te,Pe,!0),be=Ve?[Ve,i]:"";return!xe.init&&!xe.test&&!xe.update?[be,o(["for (;;)",Le])]:[be,o(["for (",o([c([i,Fe("init"),";",r,Fe("test"),";",r,Fe("update")]),i]),")",Le])]}case"WhileStatement":return o(["while (",o([c([i,Fe("test")]),i]),")",O(xe.body,Fe("body"))]);case"ForInStatement":return o(["for (",Fe("left")," in ",Fe("right"),")",O(xe.body,Fe("body"))]);case"ForOfStatement":return o(["for",xe.await?" await":""," (",Fe("left")," of ",Fe("right"),")",O(xe.body,Fe("body"))]);case"DoWhileStatement":{let Le=O(xe.body,Fe("body"));return Ne=[o(["do",Le])],xe.body.type==="BlockStatement"?Ne.push(" "):Ne.push(u),Ne.push("while (",o([c([i,Fe("test")]),i]),")",Je),Ne}case"DoExpression":return[xe.async?"async ":"","do ",Fe("body")];case"BreakStatement":return Ne.push("break"),xe.label&&Ne.push(" ",Fe("label")),Ne.push(Je),Ne;case"ContinueStatement":return Ne.push("continue"),xe.label&&Ne.push(" ",Fe("label")),Ne.push(Je),Ne;case"LabeledStatement":return xe.body.type==="EmptyStatement"?[Fe("label"),":;"]:[Fe("label"),": ",Fe("body")];case"TryStatement":return["try ",Fe("block"),xe.handler?[" ",Fe("handler")]:"",xe.finalizer?[" finally ",Fe("finalizer")]:""];case"CatchClause":if(xe.param){let Le=F(xe.param,be=>!_(be)||be.leading&&s(Pe.originalText,C(be))||be.trailing&&s(Pe.originalText,d(be),{backwards:!0})),Ve=Fe("param");return["catch ",Le?["(",c([i,Ve]),i,") "]:["(",Ve,") "],Fe("body")]}return["catch ",Fe("body")];case"SwitchStatement":return[o(["switch (",c([i,Fe("discriminant")]),i,")"])," {",xe.cases.length>0?c([u,a(u,Te.map((Le,Ve,be)=>{let Ie=Le.getValue();return[Fe(),Ve!==be.length-1&&P(Ie,Pe)?u:""]},"cases"))]):"",u,"}"];case"SwitchCase":{xe.test?Ne.push("case ",Fe("test"),":"):Ne.push("default:"),F(xe,A.Dangling)&&Ne.push(" ",t(Te,Pe,!0));let Le=xe.consequent.filter(Ve=>Ve.type!=="EmptyStatement");if(Le.length>0){let Ve=de(Te,Pe,Fe);Ne.push(Le.length===1&&Le[0].type==="BlockStatement"?[" ",Ve]:c([u,Ve]))}return Ne}case"DebuggerStatement":return["debugger",Je];case"ClassDeclaration":case"ClassExpression":return H(Te,Pe,Fe);case"ClassMethod":case"ClassPrivateMethod":case"MethodDefinition":return Z(Te,Pe,Fe);case"ClassProperty":case"PropertyDefinition":case"ClassPrivateProperty":case"ClassAccessorProperty":return ne(Te,Pe,Fe);case"TemplateElement":return y(xe.value.raw);case"TemplateLiteral":return ae(Te,Fe,Pe);case"TaggedTemplateExpression":return[Fe("tag"),Fe("typeParameters"),Fe("quasi")];case"PrivateIdentifier":return["#",Fe("name")];case"PrivateName":return["#",Fe("id")];case"InterpreterDirective":return Ne.push("#!",xe.value,u),P(xe,Pe)&&Ne.push(u),Ne;case"TopicReference":return"%";case"ArgumentPlaceholder":return"?";case"ModuleExpression":{Ne.push("module {");let Le=Fe("body");return Le&&Ne.push(c([u,Le]),u),Ne.push("}"),Ne}default:throw new Error("unknown type: "+JSON.stringify(xe.type))}}function Ee(Te,Pe){let Fe=f(Te),Ze=Fe.slice(1,-1);if(Ze.includes('"')||Ze.includes("'"))return Fe;let xe=Pe.singleQuote?"'":'"';return xe+Ze+xe}function Re(Te){return Te.type&&!_(Te)&&!k(Te)&&Te.type!=="EmptyStatement"&&Te.type!=="TemplateElement"&&Te.type!=="Import"&&Te.type!=="TSEmptyBodyFunctionExpression"}n.exports={preprocess:w,print:G,embed:h,insertPragma:p,massageAstNode:g,hasPrettierIgnore(Te){return x(Te)||M(Te)},willPrintOwnComments:D.willPrintOwnComments,canAttachComment:Re,printComment:Se,isBlockComment:_,handleComments:{avoidAstMutation:!0,ownLine:D.handleOwnLineComment,endOfLine:D.handleEndOfLineComment,remaining:D.handleRemainingComment},getCommentChildNodes:D.getCommentChildNodes}}}),Rm=ee({"src/language-js/printer-estree-json.js"(e,n){"use strict";re();var{builders:{hardline:t,indent:s,join:a}}=qe(),r=Ja();function u(c,y,h){let g=c.getValue();switch(g.type){case"JsonRoot":return[h("node"),t];case"ArrayExpression":{if(g.elements.length===0)return"[]";let p=c.map(()=>c.getValue()===null?"null":h(),"elements");return["[",s([t,a([",",t],p)]),t,"]"]}case"ObjectExpression":return g.properties.length===0?"{}":["{",s([t,a([",",t],c.map(h,"properties"))]),t,"}"];case"ObjectProperty":return[h("key"),": ",h("value")];case"UnaryExpression":return[g.operator==="+"?"":g.operator,h("argument")];case"NullLiteral":return"null";case"BooleanLiteral":return g.value?"true":"false";case"StringLiteral":case"NumericLiteral":return JSON.stringify(g.value);case"Identifier":{let p=c.getParentNode();return p&&p.type==="ObjectProperty"&&p.key===g?JSON.stringify(g.name):g.name}case"TemplateLiteral":return h(["quasis",0]);case"TemplateElement":return JSON.stringify(g.value.cooked);default:throw new Error("unknown type: "+JSON.stringify(g.type))}}var i=new Set(["start","end","extra","loc","comments","leadingComments","trailingComments","innerComments","errors","range","tokens"]);function o(c,y){let{type:h}=c;if(h==="ObjectProperty"&&c.key.type==="Identifier"){y.key={type:"StringLiteral",value:c.key.name};return}if(h==="UnaryExpression"&&c.operator==="+")return y.argument;if(h==="ArrayExpression"){for(let[g,p]of c.elements.entries())p===null&&y.elements.splice(g,0,{type:"NullLiteral"});return}if(h==="TemplateLiteral")return{type:"StringLiteral",value:c.quasis[0].value.cooked}}o.ignoredProperties=i,n.exports={preprocess:r,print:u,massageAstNode:o}}}),jt=ee({"src/common/common-options.js"(e,n){"use strict";re();var t="Common";n.exports={bracketSpacing:{since:"0.0.0",category:t,type:"boolean",default:!0,description:"Print spaces between brackets.",oppositeDescription:"Do not print spaces between brackets."},singleQuote:{since:"0.0.0",category:t,type:"boolean",default:!1,description:"Use single quotes instead of double quotes."},proseWrap:{since:"1.8.2",category:t,type:"choice",default:[{since:"1.8.2",value:!0},{since:"1.9.0",value:"preserve"}],description:"How to wrap prose.",choices:[{since:"1.9.0",value:"always",description:"Wrap prose if it exceeds the print width."},{since:"1.9.0",value:"never",description:"Do not wrap prose."},{since:"1.9.0",value:"preserve",description:"Wrap prose as-is."}]},bracketSameLine:{since:"2.4.0",category:t,type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{since:"2.6.0",category:t,type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}}}}),$m=ee({"src/language-js/options.js"(e,n){"use strict";re();var t=jt(),s="JavaScript";n.exports={arrowParens:{since:"1.9.0",category:s,type:"choice",default:[{since:"1.9.0",value:"avoid"},{since:"2.0.0",value:"always"}],description:"Include parentheses around a sole arrow function parameter.",choices:[{value:"always",description:"Always include parens. Example: `(x) => x`"},{value:"avoid",description:"Omit parens when possible. Example: `x => x`"}]},bracketSameLine:t.bracketSameLine,bracketSpacing:t.bracketSpacing,jsxBracketSameLine:{since:"0.17.0",category:s,type:"boolean",description:"Put > on the last line instead of at a new line.",deprecated:"2.4.0"},semi:{since:"1.0.0",category:s,type:"boolean",default:!0,description:"Print semicolons.",oppositeDescription:"Do not print semicolons, except at the beginning of lines which may need them."},singleQuote:t.singleQuote,jsxSingleQuote:{since:"1.15.0",category:s,type:"boolean",default:!1,description:"Use single quotes in JSX."},quoteProps:{since:"1.17.0",category:s,type:"choice",default:"as-needed",description:"Change when properties in objects are quoted.",choices:[{value:"as-needed",description:"Only add quotes around object properties where required."},{value:"consistent",description:"If at least one property in an object requires quotes, quote all properties."},{value:"preserve",description:"Respect the input use of quotes in object properties."}]},trailingComma:{since:"0.0.0",category:s,type:"choice",default:[{since:"0.0.0",value:!1},{since:"0.19.0",value:"none"},{since:"2.0.0",value:"es5"}],description:"Print trailing commas wherever possible when multi-line.",choices:[{value:"es5",description:"Trailing commas where valid in ES5 (objects, arrays, etc.)"},{value:"none",description:"No trailing commas."},{value:"all",description:"Trailing commas wherever possible (including function arguments)."}]},singleAttributePerLine:t.singleAttributePerLine}}}),Vm=ee({"src/language-js/parse/parsers.js"(){re()}}),Sn=ee({"node_modules/linguist-languages/data/JavaScript.json"(e,n){n.exports={name:"JavaScript",type:"programming",tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",color:"#f1e05a",aliases:["js","node"],extensions:[".js","._js",".bones",".cjs",".es",".es6",".frag",".gs",".jake",".javascript",".jsb",".jscad",".jsfl",".jslib",".jsm",".jspre",".jss",".jsx",".mjs",".njs",".pac",".sjs",".ssjs",".xsjs",".xsjslib"],filenames:["Jakefile"],interpreters:["chakra","d8","gjs","js","node","nodejs","qjs","rhino","v8","v8-shell"],languageId:183}}}),Wm=ee({"node_modules/linguist-languages/data/TypeScript.json"(e,n){n.exports={name:"TypeScript",type:"programming",color:"#3178c6",aliases:["ts"],interpreters:["deno","ts-node"],extensions:[".ts",".cts",".mts"],tmScope:"source.ts",aceMode:"typescript",codemirrorMode:"javascript",codemirrorMimeType:"application/typescript",languageId:378}}}),Hm=ee({"node_modules/linguist-languages/data/TSX.json"(e,n){n.exports={name:"TSX",type:"programming",color:"#3178c6",group:"TypeScript",extensions:[".tsx"],tmScope:"source.tsx",aceMode:"javascript",codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",languageId:94901924}}}),ra=ee({"node_modules/linguist-languages/data/JSON.json"(e,n){n.exports={name:"JSON",type:"data",color:"#292929",tmScope:"source.json",aceMode:"json",codemirrorMode:"javascript",codemirrorMimeType:"application/json",aliases:["geojson","jsonl","topojson"],extensions:[".json",".4DForm",".4DProject",".avsc",".geojson",".gltf",".har",".ice",".JSON-tmLanguage",".jsonl",".mcmeta",".tfstate",".tfstate.backup",".topojson",".webapp",".webmanifest",".yy",".yyp"],filenames:[".arcconfig",".auto-changelog",".c8rc",".htmlhintrc",".imgbotconfig",".nycrc",".tern-config",".tern-project",".watchmanconfig","Pipfile.lock","composer.lock","mcmod.info"],languageId:174}}}),Gm=ee({"node_modules/linguist-languages/data/JSON with Comments.json"(e,n){n.exports={name:"JSON with Comments",type:"data",color:"#292929",group:"JSON",tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",aliases:["jsonc"],extensions:[".jsonc",".code-snippets",".sublime-build",".sublime-commands",".sublime-completions",".sublime-keymap",".sublime-macro",".sublime-menu",".sublime-mousemap",".sublime-project",".sublime-settings",".sublime-theme",".sublime-workspace",".sublime_metrics",".sublime_session"],filenames:[".babelrc",".devcontainer.json",".eslintrc.json",".jscsrc",".jshintrc",".jslintrc","api-extractor.json","devcontainer.json","jsconfig.json","language-configuration.json","tsconfig.json","tslint.json"],languageId:423}}}),Um=ee({"node_modules/linguist-languages/data/JSON5.json"(e,n){n.exports={name:"JSON5",type:"data",color:"#267CB9",extensions:[".json5"],tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"application/json",languageId:175}}}),Jm=ee({"src/language-js/index.js"(e,n){"use strict";re();var t=Nt(),s=Mm(),a=Rm(),r=$m(),u=Vm(),i=[t(Sn(),c=>({since:"0.0.0",parsers:["babel","acorn","espree","meriyah","babel-flow","babel-ts","flow","typescript"],vscodeLanguageIds:["javascript","mongo"],interpreters:[...c.interpreters,"zx"],extensions:[...c.extensions.filter(y=>y!==".jsx"),".wxs"]})),t(Sn(),()=>({name:"Flow",since:"0.0.0",parsers:["flow","babel-flow"],vscodeLanguageIds:["javascript"],aliases:[],filenames:[],extensions:[".js.flow"]})),t(Sn(),()=>({name:"JSX",since:"0.0.0",parsers:["babel","babel-flow","babel-ts","flow","typescript","espree","meriyah"],vscodeLanguageIds:["javascriptreact"],aliases:void 0,filenames:void 0,extensions:[".jsx"],group:"JavaScript",interpreters:void 0,tmScope:"source.js.jsx",aceMode:"javascript",codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",color:void 0})),t(Wm(),()=>({since:"1.4.0",parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescript"]})),t(Hm(),()=>({since:"1.4.0",parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescriptreact"]})),t(ra(),()=>({name:"JSON.stringify",since:"1.13.0",parsers:["json-stringify"],vscodeLanguageIds:["json"],extensions:[".importmap"],filenames:["package.json","package-lock.json","composer.json"]})),t(ra(),c=>({since:"1.5.0",parsers:["json"],vscodeLanguageIds:["json"],extensions:c.extensions.filter(y=>y!==".jsonl")})),t(Gm(),c=>({since:"1.5.0",parsers:["json"],vscodeLanguageIds:["jsonc"],filenames:[...c.filenames,".eslintrc",".swcrc"]})),t(Um(),()=>({since:"1.13.0",parsers:["json5"],vscodeLanguageIds:["json5"]}))],o={estree:s,"estree-json":a};n.exports={languages:i,options:r,printers:o,parsers:u}}}),zm=ee({"src/language-css/clean.js"(e,n){"use strict";re();var{isFrontMatterNode:t}=Ge(),s=lt(),a=new Set(["raw","raws","sourceIndex","source","before","after","trailingComma"]);function r(i,o,c){if(t(i)&&i.lang==="yaml"&&delete o.value,i.type==="css-comment"&&c.type==="css-root"&&c.nodes.length>0&&((c.nodes[0]===i||t(c.nodes[0])&&c.nodes[1]===i)&&(delete o.text,/^\*\s*@(?:format|prettier)\s*$/.test(i.text))||c.type==="css-root"&&s(c.nodes)===i))return null;if(i.type==="value-root"&&delete o.text,(i.type==="media-query"||i.type==="media-query-list"||i.type==="media-feature-expression")&&delete o.value,i.type==="css-rule"&&delete o.params,i.type==="selector-combinator"&&(o.value=o.value.replace(/\s+/g," ")),i.type==="media-feature"&&(o.value=o.value.replace(/ /g,"")),(i.type==="value-word"&&(i.isColor&&i.isHex||["initial","inherit","unset","revert"].includes(o.value.replace().toLowerCase()))||i.type==="media-feature"||i.type==="selector-root-invalid"||i.type==="selector-pseudo")&&(o.value=o.value.toLowerCase()),i.type==="css-decl"&&(o.prop=o.prop.toLowerCase()),(i.type==="css-atrule"||i.type==="css-import")&&(o.name=o.name.toLowerCase()),i.type==="value-number"&&(o.unit=o.unit.toLowerCase()),(i.type==="media-feature"||i.type==="media-keyword"||i.type==="media-type"||i.type==="media-unknown"||i.type==="media-url"||i.type==="media-value"||i.type==="selector-attribute"||i.type==="selector-string"||i.type==="selector-class"||i.type==="selector-combinator"||i.type==="value-string")&&o.value&&(o.value=u(o.value)),i.type==="selector-attribute"&&(o.attribute=o.attribute.trim(),o.namespace&&typeof o.namespace=="string"&&(o.namespace=o.namespace.trim(),o.namespace.length===0&&(o.namespace=!0)),o.value&&(o.value=o.value.trim().replace(/^["']|["']$/g,""),delete o.quoted)),(i.type==="media-value"||i.type==="media-type"||i.type==="value-number"||i.type==="selector-root-invalid"||i.type==="selector-class"||i.type==="selector-combinator"||i.type==="selector-tag")&&o.value&&(o.value=o.value.replace(/([\d+.Ee-]+)([A-Za-z]*)/g,(y,h,g)=>{let p=Number(h);return Number.isNaN(p)?y:p+g.toLowerCase()})),i.type==="selector-tag"){let y=i.value.toLowerCase();["from","to"].includes(y)&&(o.value=y)}if(i.type==="css-atrule"&&i.name.toLowerCase()==="supports"&&delete o.value,i.type==="selector-unknown"&&delete o.value,i.type==="value-comma_group"){let y=i.groups.findIndex(h=>h.type==="value-number"&&h.unit==="...");y!==-1&&(o.groups[y].unit="",o.groups.splice(y+1,0,{type:"value-word",value:"...",isColor:!1,isHex:!1}))}}r.ignoredProperties=a;function u(i){return i.replace(/'/g,'"').replace(/\\([^\dA-Fa-f])/g,"$1")}n.exports=r}}),Jn=ee({"src/utils/front-matter/print.js"(e,n){"use strict";re();var{builders:{hardline:t,markAsRoot:s}}=qe();function a(r,u){if(r.lang==="yaml"){let i=r.value.trim(),o=i?u(i,{parser:"yaml"},{stripTrailingHardline:!0}):"";return s([r.startDelimiter,t,o,o?t:"",r.endDelimiter])}}n.exports=a}}),Xm=ee({"src/language-css/embed.js"(e,n){"use strict";re();var{builders:{hardline:t}}=qe(),s=Jn();function a(r,u,i){let o=r.getValue();if(o.type==="front-matter"){let c=s(o,i);return c?[c,t]:""}}n.exports=a}}),ro=ee({"src/utils/front-matter/parse.js"(e,n){"use strict";re();var t=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");function s(a){let r=a.match(t);if(!r)return{content:a};let{startDelimiter:u,language:i,value:o="",endDelimiter:c}=r.groups,y=i.trim()||"yaml";if(u==="+++"&&(y="toml"),y!=="yaml"&&u!==c)return{content:a};let[h]=r;return{frontMatter:{type:"front-matter",lang:y,value:o,startDelimiter:u,endDelimiter:c,raw:h.replace(/\n$/,"")},content:h.replace(/[^\n]/g," ")+a.slice(h.length)}}n.exports=s}}),Km=ee({"src/language-css/pragma.js"(e,n){"use strict";re();var t=Ga(),s=ro();function a(u){return t.hasPragma(s(u).content)}function r(u){let{frontMatter:i,content:o}=s(u);return(i?i.raw+`

`:"")+t.insertPragma(o)}n.exports={hasPragma:a,insertPragma:r}}}),Ym=ee({"src/language-css/utils/index.js"(e,n){"use strict";re();var t=new Set(["red","green","blue","alpha","a","rgb","hue","h","saturation","s","lightness","l","whiteness","w","blackness","b","tint","shade","blend","blenda","contrast","hsl","hsla","hwb","hwba"]);function s(z,H){let Z=Array.isArray(H)?H:[H],ne=-1,fe;for(;fe=z.getParentNode(++ne);)if(Z.includes(fe.type))return ne;return-1}function a(z,H){let Z=s(z,H);return Z===-1?null:z.getParentNode(Z)}function r(z){var H;let Z=a(z,"css-decl");return Z==null||(H=Z.prop)===null||H===void 0?void 0:H.toLowerCase()}var u=new Set(["initial","inherit","unset","revert"]);function i(z){return u.has(z.toLowerCase())}function o(z,H){let Z=a(z,"css-atrule");return(Z==null?void 0:Z.name)&&Z.name.toLowerCase().endsWith("keyframes")&&["from","to"].includes(H.toLowerCase())}function c(z){return z.includes("$")||z.includes("@")||z.includes("#")||z.startsWith("%")||z.startsWith("--")||z.startsWith(":--")||z.includes("(")&&z.includes(")")?z:z.toLowerCase()}function y(z,H){var Z;let ne=a(z,"value-func");return(ne==null||(Z=ne.value)===null||Z===void 0?void 0:Z.toLowerCase())===H}function h(z){var H;let Z=a(z,"css-rule"),ne=Z==null||(H=Z.raws)===null||H===void 0?void 0:H.selector;return ne&&(ne.startsWith(":import")||ne.startsWith(":export"))}function g(z,H){let Z=Array.isArray(H)?H:[H],ne=a(z,"css-atrule");return ne&&Z.includes(ne.name.toLowerCase())}function p(z){let H=z.getValue(),Z=a(z,"css-atrule");return(Z==null?void 0:Z.name)==="import"&&H.groups[0].value==="url"&&H.groups.length===2}function D(z){return z.type==="value-func"&&z.value.toLowerCase()==="url"}function v(z,H){var Z;let ne=(Z=z.getParentNode())===null||Z===void 0?void 0:Z.nodes;return ne&&ne.indexOf(H)===ne.length-1}function w(z){let{selector:H}=z;return H?typeof H=="string"&&/^@.+:.*$/.test(H)||H.value&&/^@.+:.*$/.test(H.value):!1}function T(z){return z.type==="value-word"&&["from","through","end"].includes(z.value)}function F(z){return z.type==="value-word"&&["and","or","not"].includes(z.value)}function A(z){return z.type==="value-word"&&z.value==="in"}function B(z){return z.type==="value-operator"&&z.value==="*"}function k(z){return z.type==="value-operator"&&z.value==="/"}function P(z){return z.type==="value-operator"&&z.value==="+"}function R(z){return z.type==="value-operator"&&z.value==="-"}function f(z){return z.type==="value-operator"&&z.value==="%"}function x(z){return B(z)||k(z)||P(z)||R(z)||f(z)}function m(z){return z.type==="value-word"&&["==","!="].includes(z.value)}function E(z){return z.type==="value-word"&&["<",">","<=",">="].includes(z.value)}function l(z){return z.type==="css-atrule"&&["if","else","for","each","while"].includes(z.name)}function d(z){var H;return((H=z.raws)===null||H===void 0?void 0:H.params)&&/^\(\s*\)$/.test(z.raws.params)}function C(z){return z.name.startsWith("prettier-placeholder")}function _(z){return z.prop.startsWith("@prettier-placeholder")}function b(z,H){return z.value==="$$"&&z.type==="value-func"&&(H==null?void 0:H.type)==="value-word"&&!H.raws.before}function N(z){var H,Z;return((H=z.value)===null||H===void 0?void 0:H.type)==="value-root"&&((Z=z.value.group)===null||Z===void 0?void 0:Z.type)==="value-value"&&z.prop.toLowerCase()==="composes"}function I(z){var H,Z,ne;return((H=z.value)===null||H===void 0||(Z=H.group)===null||Z===void 0||(ne=Z.group)===null||ne===void 0?void 0:ne.type)==="value-paren_group"&&z.value.group.group.open!==null&&z.value.group.group.close!==null}function $(z){var H;return((H=z.raws)===null||H===void 0?void 0:H.before)===""}function M(z){var H,Z;return z.type==="value-comma_group"&&((H=z.groups)===null||H===void 0||(Z=H[1])===null||Z===void 0?void 0:Z.type)==="value-colon"}function q(z){var H;return z.type==="value-paren_group"&&((H=z.groups)===null||H===void 0?void 0:H[0])&&M(z.groups[0])}function J(z){var H;let Z=z.getValue();if(Z.groups.length===0)return!1;let ne=z.getParentNode(1);if(!q(Z)&&!(ne&&q(ne)))return!1;let fe=a(z,"css-decl");return!!(fe!=null&&(H=fe.prop)!==null&&H!==void 0&&H.startsWith("$")||q(ne)||ne.type==="value-func")}function L(z){return z.type==="value-comment"&&z.inline}function Y(z){return z.type==="value-word"&&z.value==="#"}function V(z){return z.type==="value-word"&&z.value==="{"}function O(z){return z.type==="value-word"&&z.value==="}"}function K(z){return["value-word","value-atword"].includes(z.type)}function se(z){return(z==null?void 0:z.type)==="value-colon"}function Q(z,H){if(!M(H))return!1;let{groups:Z}=H,ne=Z.indexOf(z);return ne===-1?!1:se(Z[ne+1])}function le(z){return z.value&&["not","and","or"].includes(z.value.toLowerCase())}function W(z){return z.type!=="value-func"?!1:t.has(z.value.toLowerCase())}function X(z){return/\/\//.test(z.split(/[\n\r]/).pop())}function oe(z){return(z==null?void 0:z.type)==="value-atword"&&z.value.startsWith("prettier-placeholder-")}function ae(z,H){var Z,ne;if(((Z=z.open)===null||Z===void 0?void 0:Z.value)!=="("||((ne=z.close)===null||ne===void 0?void 0:ne.value)!==")"||z.groups.some(fe=>fe.type!=="value-comma_group"))return!1;if(H.type==="value-comma_group"){let fe=H.groups.indexOf(z)-1,ge=H.groups[fe];if((ge==null?void 0:ge.type)==="value-word"&&ge.value==="with")return!0}return!1}function Ae(z){var H,Z;return z.type==="value-paren_group"&&((H=z.open)===null||H===void 0?void 0:H.value)==="("&&((Z=z.close)===null||Z===void 0?void 0:Z.value)===")"}n.exports={getAncestorCounter:s,getAncestorNode:a,getPropOfDeclNode:r,maybeToLowerCase:c,insideValueFunctionNode:y,insideICSSRuleNode:h,insideAtRuleNode:g,insideURLFunctionInImportAtRuleNode:p,isKeyframeAtRuleKeywords:o,isWideKeywords:i,isLastNode:v,isSCSSControlDirectiveNode:l,isDetachedRulesetDeclarationNode:w,isRelationalOperatorNode:E,isEqualityOperatorNode:m,isMultiplicationNode:B,isDivisionNode:k,isAdditionNode:P,isSubtractionNode:R,isModuloNode:f,isMathOperatorNode:x,isEachKeywordNode:A,isForKeywordNode:T,isURLFunctionNode:D,isIfElseKeywordNode:F,hasComposesNode:N,hasParensAroundNode:I,hasEmptyRawBefore:$,isDetachedRulesetCallNode:d,isTemplatePlaceholderNode:C,isTemplatePropNode:_,isPostcssSimpleVarNode:b,isKeyValuePairNode:M,isKeyValuePairInParenGroupNode:q,isKeyInValuePairNode:Q,isSCSSMapItemNode:J,isInlineValueCommentNode:L,isHashNode:Y,isLeftCurlyBraceNode:V,isRightCurlyBraceNode:O,isWordNode:K,isColonNode:se,isMediaAndSupportsKeywords:le,isColorAdjusterFuncNode:W,lastLineHasInlineComment:X,isAtWordPlaceholderNode:oe,isConfigurationNode:ae,isParenGroupNode:Ae}}}),Qm=ee({"src/utils/line-column-to-index.js"(e,n){"use strict";re(),n.exports=function(t,s){let a=0;for(let r=0;r<t.line-1;++r)a=s.indexOf(`
`,a)+1;return a+t.column}}}),Zm=ee({"src/language-css/loc.js"(e,n){"use strict";re();var{skipEverythingButNewLine:t}=Nr(),s=lt(),a=Qm();function r(p,D){return typeof p.sourceIndex=="number"?p.sourceIndex:p.source?a(p.source.start,D)-1:null}function u(p,D){if(p.type==="css-comment"&&p.inline)return t(D,p.source.startOffset);let v=p.nodes&&s(p.nodes);return v&&p.source&&!p.source.end&&(p=v),p.source&&p.source.end?a(p.source.end,D):null}function i(p,D){p.source&&(p.source.startOffset=r(p,D),p.source.endOffset=u(p,D));for(let v in p){let w=p[v];v==="source"||!w||typeof w!="object"||(w.type==="value-root"||w.type==="value-unknown"?o(w,c(p),w.text||w.value):i(w,D))}}function o(p,D,v){p.source&&(p.source.startOffset=r(p,v)+D,p.source.endOffset=u(p,v)+D);for(let w in p){let T=p[w];w==="source"||!T||typeof T!="object"||o(T,D,v)}}function c(p){let D=p.source.startOffset;return typeof p.prop=="string"&&(D+=p.prop.length),p.type==="css-atrule"&&typeof p.name=="string"&&(D+=1+p.name.length+p.raws.afterName.match(/^\s*:?\s*/)[0].length),p.type!=="css-atrule"&&p.raws&&typeof p.raws.between=="string"&&(D+=p.raws.between.length),D}function y(p){let D="initial",v="initial",w,T=!1,F=[];for(let A=0;A<p.length;A++){let B=p[A];switch(D){case"initial":if(B==="'"){D="single-quotes";continue}if(B==='"'){D="double-quotes";continue}if((B==="u"||B==="U")&&p.slice(A,A+4).toLowerCase()==="url("){D="url",A+=3;continue}if(B==="*"&&p[A-1]==="/"){D="comment-block";continue}if(B==="/"&&p[A-1]==="/"){D="comment-inline",w=A-1;continue}continue;case"single-quotes":if(B==="'"&&p[A-1]!=="\\"&&(D=v,v="initial"),B===`
`||B==="\r")return p;continue;case"double-quotes":if(B==='"'&&p[A-1]!=="\\"&&(D=v,v="initial"),B===`
`||B==="\r")return p;continue;case"url":if(B===")"&&(D="initial"),B===`
`||B==="\r")return p;if(B==="'"){D="single-quotes",v="url";continue}if(B==='"'){D="double-quotes",v="url";continue}continue;case"comment-block":B==="/"&&p[A-1]==="*"&&(D="initial");continue;case"comment-inline":(B==='"'||B==="'"||B==="*")&&(T=!0),(B===`
`||B==="\r")&&(T&&F.push([w,A]),D="initial",T=!1);continue}}for(let[A,B]of F)p=p.slice(0,A)+p.slice(A,B).replace(/["'*]/g," ")+p.slice(B);return p}function h(p){return p.source.startOffset}function g(p){return p.source.endOffset}n.exports={locStart:h,locEnd:g,calculateLoc:i,replaceQuotesInInlineComments:y}}}),ed=ee({"src/language-css/utils/is-less-parser.js"(e,n){"use strict";re();function t(s){return s.parser==="css"||s.parser==="less"}n.exports=t}}),td=ee({"src/language-css/utils/is-scss.js"(e,n){"use strict";re();function t(s,a){return s==="less"||s==="scss"?s==="scss":/(?:\w\s*:\s*[^:}]+|#){|@import[^\n]+(?:url|,)/.test(a)}n.exports=t}}),rd=ee({"src/language-css/utils/css-units.evaluate.js"(e,n){n.exports={em:"em",rem:"rem",ex:"ex",rex:"rex",cap:"cap",rcap:"rcap",ch:"ch",rch:"rch",ic:"ic",ric:"ric",lh:"lh",rlh:"rlh",vw:"vw",svw:"svw",lvw:"lvw",dvw:"dvw",vh:"vh",svh:"svh",lvh:"lvh",dvh:"dvh",vi:"vi",svi:"svi",lvi:"lvi",dvi:"dvi",vb:"vb",svb:"svb",lvb:"lvb",dvb:"dvb",vmin:"vmin",svmin:"svmin",lvmin:"lvmin",dvmin:"dvmin",vmax:"vmax",svmax:"svmax",lvmax:"lvmax",dvmax:"dvmax",cm:"cm",mm:"mm",q:"Q",in:"in",pt:"pt",pc:"pc",px:"px",deg:"deg",grad:"grad",rad:"rad",turn:"turn",s:"s",ms:"ms",hz:"Hz",khz:"kHz",dpi:"dpi",dpcm:"dpcm",dppx:"dppx",x:"x"}}}),nd=ee({"src/language-css/utils/print-unit.js"(e,n){"use strict";re();var t=rd();function s(a){let r=a.toLowerCase();return Object.prototype.hasOwnProperty.call(t,r)?t[r]:a}n.exports=s}}),ud=ee({"src/language-css/printer-postcss.js"(e,n){"use strict";re();var t=lt(),{printNumber:s,printString:a,hasNewline:r,isFrontMatterNode:u,isNextLineEmpty:i,isNonEmptyArray:o}=Ge(),{builders:{join:c,line:y,hardline:h,softline:g,group:p,fill:D,indent:v,dedent:w,ifBreak:T,breakParent:F},utils:{removeLines:A,getDocParts:B}}=qe(),k=zm(),P=Xm(),{insertPragma:R}=Km(),{getAncestorNode:f,getPropOfDeclNode:x,maybeToLowerCase:m,insideValueFunctionNode:E,insideICSSRuleNode:l,insideAtRuleNode:d,insideURLFunctionInImportAtRuleNode:C,isKeyframeAtRuleKeywords:_,isWideKeywords:b,isLastNode:N,isSCSSControlDirectiveNode:I,isDetachedRulesetDeclarationNode:$,isRelationalOperatorNode:M,isEqualityOperatorNode:q,isMultiplicationNode:J,isDivisionNode:L,isAdditionNode:Y,isSubtractionNode:V,isMathOperatorNode:O,isEachKeywordNode:K,isForKeywordNode:se,isURLFunctionNode:Q,isIfElseKeywordNode:le,hasComposesNode:W,hasParensAroundNode:X,hasEmptyRawBefore:oe,isKeyValuePairNode:ae,isKeyInValuePairNode:Ae,isDetachedRulesetCallNode:z,isTemplatePlaceholderNode:H,isTemplatePropNode:Z,isPostcssSimpleVarNode:ne,isSCSSMapItemNode:fe,isInlineValueCommentNode:ge,isHashNode:Ce,isLeftCurlyBraceNode:_e,isRightCurlyBraceNode:Oe,isWordNode:pe,isColonNode:ie,isMediaAndSupportsKeywords:ve,isColorAdjusterFuncNode:ce,lastLineHasInlineComment:U,isAtWordPlaceholderNode:de,isConfigurationNode:De,isParenGroupNode:he}=Ym(),{locStart:Be,locEnd:Se}=Zm(),ye=ed(),S=td(),G=nd();function te(be){return be.trailingComma==="es5"||be.trailingComma==="all"}function Ee(be,Ie,Me){let ue=be.getValue();if(!ue)return"";if(typeof ue=="string")return ue;switch(ue.type){case"front-matter":return[ue.raw,h];case"css-root":{let He=Re(be,Ie,Me),Ue=ue.raws.after.trim();return[He,Ue?` ${Ue}`:"",B(He).length>0?h:""]}case"css-comment":{let He=ue.inline||ue.raws.inline,Ue=Ie.originalText.slice(Be(ue),Se(ue));return He?Ue.trimEnd():Ue}case"css-rule":return[Me("selector"),ue.important?" !important":"",ue.nodes?[ue.selector&&ue.selector.type==="selector-unknown"&&U(ue.selector.value)?y:" ","{",ue.nodes.length>0?v([h,Re(be,Ie,Me)]):"",h,"}",$(ue)?";":""]:";"];case"css-decl":{let He=be.getParentNode(),{between:Ue}=ue.raws,Xe=Ue.trim(),at=Xe===":",nt=W(ue)?A(Me("value")):Me("value");return!at&&U(Xe)&&(nt=v([h,w(nt)])),[ue.raws.before.replace(/[\s;]/g,""),l(be)?ue.prop:m(ue.prop),Xe.startsWith("//")?" ":"",Xe,ue.extend?"":" ",ye(Ie)&&ue.extend&&ue.selector?["extend(",Me("selector"),")"]:"",nt,ue.raws.important?ue.raws.important.replace(/\s*!\s*important/i," !important"):ue.important?" !important":"",ue.raws.scssDefault?ue.raws.scssDefault.replace(/\s*!default/i," !default"):ue.scssDefault?" !default":"",ue.raws.scssGlobal?ue.raws.scssGlobal.replace(/\s*!global/i," !global"):ue.scssGlobal?" !global":"",ue.nodes?[" {",v([g,Re(be,Ie,Me)]),g,"}"]:Z(ue)&&!He.raws.semicolon&&Ie.originalText[Se(ue)-1]!==";"?"":Ie.__isHTMLStyleAttribute&&N(be,ue)?T(";"):";"]}case"css-atrule":{let He=be.getParentNode(),Ue=H(ue)&&!He.raws.semicolon&&Ie.originalText[Se(ue)-1]!==";";if(ye(Ie)){if(ue.mixin)return[Me("selector"),ue.important?" !important":"",Ue?"":";"];if(ue.function)return[ue.name,Me("params"),Ue?"":";"];if(ue.variable)return["@",ue.name,": ",ue.value?Me("value"):"",ue.raws.between.trim()?ue.raws.between.trim()+" ":"",ue.nodes?["{",v([ue.nodes.length>0?g:"",Re(be,Ie,Me)]),g,"}"]:"",Ue?"":";"]}return["@",z(ue)||ue.name.endsWith(":")?ue.name:m(ue.name),ue.params?[z(ue)?"":H(ue)?ue.raws.afterName===""?"":ue.name.endsWith(":")?" ":/^\s*\n\s*\n/.test(ue.raws.afterName)?[h,h]:/^\s*\n/.test(ue.raws.afterName)?h:" ":" ",Me("params")]:"",ue.selector?v([" ",Me("selector")]):"",ue.value?p([" ",Me("value"),I(ue)?X(ue)?" ":y:""]):ue.name==="else"?" ":"",ue.nodes?[I(ue)?"":ue.selector&&!ue.selector.nodes&&typeof ue.selector.value=="string"&&U(ue.selector.value)||!ue.selector&&typeof ue.params=="string"&&U(ue.params)?y:" ","{",v([ue.nodes.length>0?g:"",Re(be,Ie,Me)]),g,"}"]:Ue?"":";"]}case"media-query-list":{let He=[];return be.each(Ue=>{let Xe=Ue.getValue();Xe.type==="media-query"&&Xe.value===""||He.push(Me())},"nodes"),p(v(c(y,He)))}case"media-query":return[c(" ",be.map(Me,"nodes")),N(be,ue)?"":","];case"media-type":return Le(Je(ue.value,Ie));case"media-feature-expression":return ue.nodes?["(",...be.map(Me,"nodes"),")"]:ue.value;case"media-feature":return m(Je(ue.value.replace(/ +/g," "),Ie));case"media-colon":return[ue.value," "];case"media-value":return Le(Je(ue.value,Ie));case"media-keyword":return Je(ue.value,Ie);case"media-url":return Je(ue.value.replace(/^url\(\s+/gi,"url(").replace(/\s+\)$/g,")"),Ie);case"media-unknown":return ue.value;case"selector-root":return p([d(be,"custom-selector")?[f(be,"css-atrule").customSelector,y]:"",c([",",d(be,["extend","custom-selector","nest"])?y:h],be.map(Me,"nodes"))]);case"selector-selector":return p(v(be.map(Me,"nodes")));case"selector-comment":return ue.value;case"selector-string":return Je(ue.value,Ie);case"selector-tag":{let He=be.getParentNode(),Ue=He&&He.nodes.indexOf(ue),Xe=Ue&&He.nodes[Ue-1];return[ue.namespace?[ue.namespace===!0?"":ue.namespace.trim(),"|"]:"",Xe.type==="selector-nesting"?ue.value:Le(_(be,ue.value)?ue.value.toLowerCase():ue.value)]}case"selector-id":return["#",ue.value];case"selector-class":return[".",Le(Je(ue.value,Ie))];case"selector-attribute":{var st;return["[",ue.namespace?[ue.namespace===!0?"":ue.namespace.trim(),"|"]:"",ue.attribute.trim(),(st=ue.operator)!==null&&st!==void 0?st:"",ue.value?Ne(Je(ue.value.trim(),Ie),Ie):"",ue.insensitive?" i":"","]"]}case"selector-combinator":{if(ue.value==="+"||ue.value===">"||ue.value==="~"||ue.value===">>>"){let Xe=be.getParentNode();return[Xe.type==="selector-selector"&&Xe.nodes[0]===ue?"":y,ue.value,N(be,ue)?"":" "]}let He=ue.value.trim().startsWith("(")?y:"",Ue=Le(Je(ue.value.trim(),Ie))||y;return[He,Ue]}case"selector-universal":return[ue.namespace?[ue.namespace===!0?"":ue.namespace.trim(),"|"]:"",ue.value];case"selector-pseudo":return[m(ue.value),o(ue.nodes)?p(["(",v([g,c([",",y],be.map(Me,"nodes"))]),g,")"]):""];case"selector-nesting":return ue.value;case"selector-unknown":{let He=f(be,"css-rule");if(He&&He.isSCSSNesterProperty)return Le(Je(m(ue.value),Ie));let Ue=be.getParentNode();if(Ue.raws&&Ue.raws.selector){let at=Be(Ue),nt=at+Ue.raws.selector.length;return Ie.originalText.slice(at,nt).trim()}let Xe=be.getParentNode(1);if(Ue.type==="value-paren_group"&&Xe&&Xe.type==="value-func"&&Xe.value==="selector"){let at=Se(Ue.open)+1,nt=Be(Ue.close),j=Ie.originalText.slice(at,nt).trim();return U(j)?[F,j]:j}return ue.value}case"value-value":case"value-root":return Me("group");case"value-comment":return Ie.originalText.slice(Be(ue),Se(ue));case"value-comma_group":{let He=be.getParentNode(),Ue=be.getParentNode(1),Xe=x(be),at=Xe&&He.type==="value-value"&&(Xe==="grid"||Xe.startsWith("grid-template")),nt=f(be,"css-atrule"),j=nt&&I(nt),me=ue.groups.some(ot=>ge(ot)),ke=be.map(Me,"groups"),je=[],Ye=E(be,"url"),ut=!1,ze=!1;for(let ot=0;ot<ue.groups.length;++ot){var rt;je.push(ke[ot]);let tt=ue.groups[ot-1],$e=ue.groups[ot],We=ue.groups[ot+1],tr=ue.groups[ot+2];if(Ye){(We&&Y(We)||Y($e))&&je.push(" ");continue}if(d(be,"forward")&&$e.type==="value-word"&&$e.value&&tt!==void 0&&tt.type==="value-word"&&tt.value==="as"&&We.type==="value-operator"&&We.value==="*"||!We||$e.type==="value-word"&&$e.value.endsWith("-")&&de(We))continue;let ao=$e.type==="value-string"&&$e.value.startsWith("#{"),oo=ut&&We.type==="value-string"&&We.value.endsWith("}");if(ao||oo){ut=!ut;continue}if(ut||ie($e)||ie(We)||$e.type==="value-atword"&&$e.value===""||$e.value==="~"||$e.value&&$e.value.includes("\\")&&We&&We.type!=="value-comment"||tt&&tt.value&&tt.value.indexOf("\\")===tt.value.length-1&&$e.type==="value-operator"&&$e.value==="/"||$e.value==="\\"||ne($e,We)||Ce($e)||_e($e)||Oe(We)||_e(We)&&oe(We)||Oe($e)&&oe(We)||$e.value==="--"&&Ce(We))continue;let Kn=O($e),Yn=O(We);if((Kn&&Ce(We)||Yn&&Oe($e))&&oe(We)||!tt&&L($e)||E(be,"calc")&&(Y($e)||Y(We)||V($e)||V(We))&&oe(We))continue;let lo=(Y($e)||V($e))&&ot===0&&(We.type==="value-number"||We.isHex)&&Ue&&ce(Ue)&&!oe(We),Qn=tr&&tr.type==="value-func"||tr&&pe(tr)||$e.type==="value-func"||pe($e),Zn=We.type==="value-func"||pe(We)||tt&&tt.type==="value-func"||tt&&pe(tt);if(!(!(J(We)||J($e))&&!E(be,"calc")&&!lo&&(L(We)&&!Qn||L($e)&&!Zn||Y(We)&&!Qn||Y($e)&&!Zn||V(We)||V($e))&&(oe(We)||Kn&&(!tt||tt&&O(tt))))){if(ge($e)){if(He.type==="value-paren_group"){je.push(w(h));continue}je.push(h);continue}if(j&&(q(We)||M(We)||le(We)||K($e)||se($e))){je.push(" ");continue}if(nt&&nt.name.toLowerCase()==="namespace"){je.push(" ");continue}if(at){$e.source&&We.source&&$e.source.start.line!==We.source.start.line?(je.push(h),ze=!0):je.push(" ");continue}if(Yn){je.push(" ");continue}if(!(We&&We.value==="...")&&!(de($e)&&de(We)&&Se($e)===Be(We))){if(de($e)&&he(We)&&Se($e)===Be(We.open)){je.push(g);continue}if($e.value==="with"&&he(We)){je.push(" ");continue}(rt=$e.value)!==null&&rt!==void 0&&rt.endsWith("#")&&We.value==="{"&&he(We.group)||je.push(y)}}}return me&&je.push(F),ze&&je.unshift(h),j?p(v(je)):C(be)?p(D(je)):p(v(D(je)))}case"value-paren_group":{let He=be.getParentNode();if(He&&Q(He)&&(ue.groups.length===1||ue.groups.length>0&&ue.groups[0].type==="value-comma_group"&&ue.groups[0].groups.length>0&&ue.groups[0].groups[0].type==="value-word"&&ue.groups[0].groups[0].value.startsWith("data:")))return[ue.open?Me("open"):"",c(",",be.map(Me,"groups")),ue.close?Me("close"):""];if(!ue.open){let Ye=be.map(Me,"groups"),ut=[];for(let ze=0;ze<Ye.length;ze++)ze!==0&&ut.push([",",y]),ut.push(Ye[ze]);return p(v(D(ut)))}let Ue=fe(be),Xe=t(ue.groups),at=Xe&&Xe.type==="value-comment",nt=Ae(ue,He),j=De(ue,He),me=j||Ue&&!nt,ke=j||nt,je=p([ue.open?Me("open"):"",v([g,c([y],be.map((Ye,ut)=>{let ze=Ye.getValue(),ot=ut===ue.groups.length-1,tt=[Me(),ot?"":","];if(ae(ze)&&ze.type==="value-comma_group"&&ze.groups&&ze.groups[0].type!=="value-paren_group"&&ze.groups[2]&&ze.groups[2].type==="value-paren_group"){let $e=B(tt[0].contents.contents);return $e[1]=p($e[1]),p(w(tt))}if(!ot&&ze.type==="value-comma_group"&&o(ze.groups)){let $e=t(ze.groups);$e.source&&i(Ie.originalText,$e,Se)&&tt.push(h)}return tt},"groups"))]),T(!at&&S(Ie.parser,Ie.originalText)&&Ue&&te(Ie)?",":""),g,ue.close?Me("close"):""],{shouldBreak:me});return ke?w(je):je}case"value-func":return[ue.value,d(be,"supports")&&ve(ue)?" ":"",Me("group")];case"value-paren":return ue.value;case"value-number":return[Ve(ue.value),G(ue.unit)];case"value-operator":return ue.value;case"value-word":return ue.isColor&&ue.isHex||b(ue.value)?ue.value.toLowerCase():ue.value;case"value-colon":{let He=be.getParentNode(),Ue=He&&He.groups.indexOf(ue),Xe=Ue&&He.groups[Ue-1];return[ue.value,Xe&&typeof Xe.value=="string"&&t(Xe.value)==="\\"||E(be,"url")?"":y]}case"value-comma":return[ue.value," "];case"value-string":return a(ue.raws.quote+ue.value+ue.raws.quote,Ie);case"value-atword":return["@",ue.value];case"value-unicode-range":return ue.value;case"value-unknown":return ue.value;default:throw new Error(`Unknown postcss type ${JSON.stringify(ue.type)}`)}}function Re(be,Ie,Me){let ue=[];return be.each((st,rt,He)=>{let Ue=He[rt-1];if(Ue&&Ue.type==="css-comment"&&Ue.text.trim()==="prettier-ignore"){let Xe=st.getValue();ue.push(Ie.originalText.slice(Be(Xe),Se(Xe)))}else ue.push(Me());rt!==He.length-1&&(He[rt+1].type==="css-comment"&&!r(Ie.originalText,Be(He[rt+1]),{backwards:!0})&&!u(He[rt])||He[rt+1].type==="css-atrule"&&He[rt+1].name==="else"&&He[rt].type!=="css-comment"?ue.push(" "):(ue.push(Ie.__isHTMLStyleAttribute?y:h),i(Ie.originalText,st.getValue(),Se)&&!u(He[rt])&&ue.push(h)))},"nodes"),ue}var Te=/(["'])(?:(?!\1)[^\\]|\\.)*\1/gs,Pe=/(?:\d*\.\d+|\d+\.?)(?:[Ee][+-]?\d+)?/g,Fe=/[A-Za-z]+/g,Ze=/[$@]?[A-Z_a-z\u0080-\uFFFF][\w\u0080-\uFFFF-]*/g,xe=new RegExp(Te.source+`|(${Ze.source})?(${Pe.source})(${Fe.source})?`,"g");function Je(be,Ie){return be.replace(Te,Me=>a(Me,Ie))}function Ne(be,Ie){let Me=Ie.singleQuote?"'":'"';return be.includes('"')||be.includes("'")?be:Me+be+Me}function Le(be){return be.replace(xe,(Ie,Me,ue,st,rt)=>!ue&&st?Ve(st)+m(rt||""):Ie)}function Ve(be){return s(be).replace(/\.0(?=$|e)/,"")}n.exports={print:Ee,embed:P,insertPragma:R,massageAstNode:k}}}),sd=ee({"src/language-css/options.js"(e,n){"use strict";re();var t=jt();n.exports={singleQuote:t.singleQuote}}}),id=ee({"src/language-css/parsers.js"(){re()}}),ad=ee({"node_modules/linguist-languages/data/CSS.json"(e,n){n.exports={name:"CSS",type:"markup",tmScope:"source.css",aceMode:"css",codemirrorMode:"css",codemirrorMimeType:"text/css",color:"#563d7c",extensions:[".css"],languageId:50}}}),od=ee({"node_modules/linguist-languages/data/PostCSS.json"(e,n){n.exports={name:"PostCSS",type:"markup",color:"#dc3a0c",tmScope:"source.postcss",group:"CSS",extensions:[".pcss",".postcss"],aceMode:"text",languageId:262764437}}}),ld=ee({"node_modules/linguist-languages/data/Less.json"(e,n){n.exports={name:"Less",type:"markup",color:"#1d365d",aliases:["less-css"],extensions:[".less"],tmScope:"source.css.less",aceMode:"less",codemirrorMode:"css",codemirrorMimeType:"text/css",languageId:198}}}),cd=ee({"node_modules/linguist-languages/data/SCSS.json"(e,n){n.exports={name:"SCSS",type:"markup",color:"#c6538c",tmScope:"source.css.scss",aceMode:"scss",codemirrorMode:"css",codemirrorMimeType:"text/x-scss",extensions:[".scss"],languageId:329}}}),pd=ee({"src/language-css/index.js"(e,n){"use strict";re();var t=Nt(),s=ud(),a=sd(),r=id(),u=[t(ad(),o=>({since:"1.4.0",parsers:["css"],vscodeLanguageIds:["css"],extensions:[...o.extensions,".wxss"]})),t(od(),()=>({since:"1.4.0",parsers:["css"],vscodeLanguageIds:["postcss"]})),t(ld(),()=>({since:"1.4.0",parsers:["less"],vscodeLanguageIds:["less"]})),t(cd(),()=>({since:"1.4.0",parsers:["scss"],vscodeLanguageIds:["scss"]}))],i={postcss:s};n.exports={languages:u,options:a,printers:i,parsers:r}}}),fd=ee({"src/language-handlebars/loc.js"(e,n){"use strict";re();function t(a){return a.loc.start.offset}function s(a){return a.loc.end.offset}n.exports={locStart:t,locEnd:s}}}),Dd=ee({"src/language-handlebars/clean.js"(e,n){"use strict";re();function t(s,a){if(s.type==="TextNode"){let r=s.chars.trim();if(!r)return null;a.chars=r.replace(/[\t\n\f\r ]+/g," ")}s.type==="AttrNode"&&s.name.toLowerCase()==="class"&&delete a.value}t.ignoredProperties=new Set(["loc","selfClosing"]),n.exports=t}}),md=ee({"vendors/html-void-elements.json"(e,n){n.exports={htmlVoidElements:["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","isindex","keygen","link","menuitem","meta","nextid","param","source","track","wbr"]}}}),dd=ee({"src/language-handlebars/utils.js"(e,n){"use strict";re();var{htmlVoidElements:t}=md(),s=lt();function a(A){let B=A.getValue(),k=A.getParentNode(0);return!!(h(A,["ElementNode"])&&s(k.children)===B||h(A,["Block"])&&s(k.body)===B)}function r(A){return A.toUpperCase()===A}function u(A){return y(A,["ElementNode"])&&typeof A.tag=="string"&&!A.tag.startsWith(":")&&(r(A.tag[0])||A.tag.includes("."))}var i=new Set(t);function o(A){return i.has(A.tag)||u(A)&&A.children.every(B=>c(B))}function c(A){return y(A,["TextNode"])&&!/\S/.test(A.chars)}function y(A,B){return A&&B.includes(A.type)}function h(A,B){let k=A.getParentNode(0);return y(k,B)}function g(A,B){let k=v(A);return y(k,B)}function p(A,B){let k=w(A);return y(k,B)}function D(A,B){var k,P,R,f;let x=A.getValue(),m=(k=A.getParentNode(0))!==null&&k!==void 0?k:{},E=(P=(R=(f=m.children)!==null&&f!==void 0?f:m.body)!==null&&R!==void 0?R:m.parts)!==null&&P!==void 0?P:[],l=E.indexOf(x);return l!==-1&&E[l+B]}function v(A){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return D(A,-B)}function w(A){return D(A,1)}function T(A){return y(A,["MustacheCommentStatement"])&&typeof A.value=="string"&&A.value.trim()==="prettier-ignore"}function F(A){let B=A.getValue(),k=v(A,2);return T(B)||T(k)}n.exports={getNextNode:w,getPreviousNode:v,hasPrettierIgnore:F,isLastNodeOfSiblings:a,isNextNodeOfSomeType:p,isNodeOfSomeType:y,isParentOfSomeType:h,isPreviousNodeOfSomeType:g,isVoid:o,isWhitespaceNode:c}}}),gd=ee({"src/language-handlebars/printer-glimmer.js"(e,n){"use strict";re();var{builders:{dedent:t,fill:s,group:a,hardline:r,ifBreak:u,indent:i,join:o,line:c,softline:y},utils:{getDocParts:h,replaceTextEndOfLine:g}}=qe(),{getPreferredQuote:p,isNonEmptyArray:D}=Ge(),{locStart:v,locEnd:w}=fd(),T=Dd(),{getNextNode:F,getPreviousNode:A,hasPrettierIgnore:B,isLastNodeOfSiblings:k,isNextNodeOfSomeType:P,isNodeOfSomeType:R,isParentOfSomeType:f,isPreviousNodeOfSomeType:x,isVoid:m,isWhitespaceNode:E}=dd(),l=2;function d(U,de,De){let he=U.getValue();if(!he)return"";if(B(U))return de.originalText.slice(v(he),w(he));let Be=de.singleQuote?"'":'"';switch(he.type){case"Block":case"Program":case"Template":return a(U.map(De,"body"));case"ElementNode":{let Se=a(_(U,De)),ye=de.htmlWhitespaceSensitivity==="ignore"&&P(U,["ElementNode"])?y:"";if(m(he))return[Se,ye];let S=["</",he.tag,">"];return he.children.length===0?[Se,i(S),ye]:de.htmlWhitespaceSensitivity==="ignore"?[Se,i(b(U,de,De)),r,i(S),ye]:[Se,i(a(b(U,de,De))),i(S),ye]}case"BlockStatement":{let Se=U.getParentNode(1);return Se&&Se.inverse&&Se.inverse.body.length===1&&Se.inverse.body[0]===he&&Se.inverse.body[0].path.parts[0]===Se.path.parts[0]?[se(U,De,Se.inverse.body[0].path.parts[0]),oe(U,De,de),ae(U,De,de)]:[O(U,De),a([oe(U,De,de),ae(U,De,de),Q(U,De,de)])]}case"ElementModifierStatement":return a(["{{",pe(U,De),"}}"]);case"MustacheStatement":return a([I(he),pe(U,De),$(he)]);case"SubExpression":return a(["(",Oe(U,De),y,")"]);case"AttrNode":{let Se=he.value.type==="TextNode";if(Se&&he.value.chars===""&&v(he.value)===w(he.value))return he.name;let S=Se?p(he.value.chars,Be).quote:he.value.type==="ConcatStatement"?p(he.value.parts.filter(te=>te.type==="TextNode").map(te=>te.chars).join(""),Be).quote:"",G=De("value");return[he.name,"=",S,he.name==="class"&&S?a(i(G)):G,S]}case"ConcatStatement":return U.map(De,"parts");case"Hash":return o(c,U.map(De,"pairs"));case"HashPair":return[he.key,"=",De("value")];case"TextNode":{let Se=he.chars.replace(/{{/g,"\\{{"),ye=H(U);if(ye){if(ye==="class"){let xe=Se.trim().split(/\s+/).join(" "),Je=!1,Ne=!1;return f(U,["ConcatStatement"])&&(x(U,["MustacheStatement"])&&/^\s/.test(Se)&&(Je=!0),P(U,["MustacheStatement"])&&/\s$/.test(Se)&&xe!==""&&(Ne=!0)),[Je?c:"",xe,Ne?c:""]}return g(Se)}let G=/^[\t\n\f\r ]*$/.test(Se),te=!A(U),Ee=!F(U);if(de.htmlWhitespaceSensitivity!=="ignore"){let xe=/^[\t\n\f\r ]*/,Je=/[\t\n\f\r ]*$/,Ne=Ee&&f(U,["Template"]),Le=te&&f(U,["Template"]);if(G){if(Le||Ne)return"";let ue=[c],st=Z(Se);return st&&(ue=ge(st)),k(U)&&(ue=ue.map(rt=>t(rt))),ue}let[Ve]=Se.match(xe),[be]=Se.match(Je),Ie=[];if(Ve){Ie=[c];let ue=Z(Ve);ue&&(Ie=ge(ue)),Se=Se.replace(xe,"")}let Me=[];if(be){if(!Ne){Me=[c];let ue=Z(be);ue&&(Me=ge(ue)),k(U)&&(Me=Me.map(st=>t(st)))}Se=Se.replace(Je,"")}return[...Ie,s(Ae(Se)),...Me]}let Re=Z(Se),Te=ne(Se),Pe=fe(Se);if((te||Ee)&&G&&f(U,["Block","ElementNode","Template"]))return"";G&&Re?(Te=Math.min(Re,l),Pe=0):(P(U,["BlockStatement","ElementNode"])&&(Pe=Math.max(Pe,1)),x(U,["BlockStatement","ElementNode"])&&(Te=Math.max(Te,1)));let Fe="",Ze="";return Pe===0&&P(U,["MustacheStatement"])&&(Ze=" "),Te===0&&x(U,["MustacheStatement"])&&(Fe=" "),te&&(Te=0,Fe=""),Ee&&(Pe=0,Ze=""),Se=Se.replace(/^[\t\n\f\r ]+/g,Fe).replace(/[\t\n\f\r ]+$/,Ze),[...ge(Te),s(Ae(Se)),...ge(Pe)]}case"MustacheCommentStatement":{let Se=v(he),ye=w(he),S=de.originalText.charAt(Se+2)==="~",G=de.originalText.charAt(ye-3)==="~",te=he.value.includes("}}")?"--":"";return["{{",S?"~":"","!",te,he.value,te,G?"~":"","}}"]}case"PathExpression":return he.original;case"BooleanLiteral":return String(he.value);case"CommentStatement":return["<!--",he.value,"-->"];case"StringLiteral":{if(_e(U)){let Se=de.singleQuote?'"':"'";return Ce(he.value,Se)}return Ce(he.value,Be)}case"NumberLiteral":return String(he.value);case"UndefinedLiteral":return"undefined";case"NullLiteral":return"null";default:throw new Error("unknown glimmer type: "+JSON.stringify(he.type))}}function C(U,de){return v(U)-v(de)}function _(U,de){let De=U.getValue(),he=["attributes","modifiers","comments"].filter(Se=>D(De[Se])),Be=he.flatMap(Se=>De[Se]).sort(C);for(let Se of he)U.each(ye=>{let S=Be.indexOf(ye.getValue());Be.splice(S,1,[c,de()])},Se);return D(De.blockParams)&&Be.push(c,ce(De)),["<",De.tag,i(Be),N(De)]}function b(U,de,De){let Be=U.getValue().children.every(Se=>E(Se));return de.htmlWhitespaceSensitivity==="ignore"&&Be?"":U.map((Se,ye)=>{let S=De();return ye===0&&de.htmlWhitespaceSensitivity==="ignore"?[y,S]:S},"children")}function N(U){return m(U)?u([y,"/>"],[" />",y]):u([y,">"],">")}function I(U){let de=U.escaped===!1?"{{{":"{{",De=U.strip&&U.strip.open?"~":"";return[de,De]}function $(U){let de=U.escaped===!1?"}}}":"}}";return[U.strip&&U.strip.close?"~":"",de]}function M(U){let de=I(U),De=U.openStrip.open?"~":"";return[de,De,"#"]}function q(U){let de=$(U);return[U.openStrip.close?"~":"",de]}function J(U){let de=I(U),De=U.closeStrip.open?"~":"";return[de,De,"/"]}function L(U){let de=$(U);return[U.closeStrip.close?"~":"",de]}function Y(U){let de=I(U),De=U.inverseStrip.open?"~":"";return[de,De]}function V(U){let de=$(U);return[U.inverseStrip.close?"~":"",de]}function O(U,de){let De=U.getValue(),he=M(De),Be=q(De),Se=[ie(U,de)],ye=ve(U,de);if(ye&&Se.push(c,ye),D(De.program.blockParams)){let S=ce(De.program);Se.push(c,S)}return a([he,i(Se),y,Be])}function K(U,de){return[de.htmlWhitespaceSensitivity==="ignore"?r:"",Y(U),"else",V(U)]}function se(U,de,De){let he=U.getParentNode(1);return[Y(he),"else ",De," ",ve(U,de),V(he)]}function Q(U,de,De){let he=U.getValue();return De.htmlWhitespaceSensitivity==="ignore"?[le(he)?y:r,J(he),de("path"),L(he)]:[J(he),de("path"),L(he)]}function le(U){return R(U,["BlockStatement"])&&U.program.body.every(de=>E(de))}function W(U){return X(U)&&U.inverse.body.length===1&&R(U.inverse.body[0],["BlockStatement"])&&U.inverse.body[0].path.parts[0]===U.path.parts[0]}function X(U){return R(U,["BlockStatement"])&&U.inverse}function oe(U,de,De){let he=U.getValue();if(le(he))return"";let Be=de("program");return De.htmlWhitespaceSensitivity==="ignore"?i([r,Be]):i(Be)}function ae(U,de,De){let he=U.getValue(),Be=de("inverse"),Se=De.htmlWhitespaceSensitivity==="ignore"?[r,Be]:Be;return W(he)?Se:X(he)?[K(he,De),i(Se)]:""}function Ae(U){return h(o(c,z(U)))}function z(U){return U.split(/[\t\n\f\r ]+/)}function H(U){for(let de=0;de<2;de++){let De=U.getParentNode(de);if(De&&De.type==="AttrNode")return De.name.toLowerCase()}}function Z(U){return U=typeof U=="string"?U:"",U.split(`
`).length-1}function ne(U){U=typeof U=="string"?U:"";let de=(U.match(/^([^\S\n\r]*[\n\r])+/g)||[])[0]||"";return Z(de)}function fe(U){U=typeof U=="string"?U:"";let de=(U.match(/([\n\r][^\S\n\r]*)+$/g)||[])[0]||"";return Z(de)}function ge(){let U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return Array.from({length:Math.min(U,l)}).fill(r)}function Ce(U,de){let{quote:De,regex:he}=p(U,de);return[De,U.replace(he,`\\${De}`),De]}function _e(U){let de=0,De=U.getParentNode(de);for(;De&&R(De,["SubExpression"]);)de++,De=U.getParentNode(de);return!!(De&&R(U.getParentNode(de+1),["ConcatStatement"])&&R(U.getParentNode(de+2),["AttrNode"]))}function Oe(U,de){let De=ie(U,de),he=ve(U,de);return he?i([De,c,a(he)]):De}function pe(U,de){let De=ie(U,de),he=ve(U,de);return he?[i([De,c,he]),y]:De}function ie(U,de){return de("path")}function ve(U,de){let De=U.getValue(),he=[];if(De.params.length>0){let Be=U.map(de,"params");he.push(...Be)}if(De.hash&&De.hash.pairs.length>0){let Be=de("hash");he.push(Be)}return he.length===0?"":o(c,he)}function ce(U){return["as |",U.blockParams.join(" "),"|"]}n.exports={print:d,massageAstNode:T}}}),yd=ee({"src/language-handlebars/parsers.js"(){re()}}),hd=ee({"node_modules/linguist-languages/data/Handlebars.json"(e,n){n.exports={name:"Handlebars",type:"markup",color:"#f7931e",aliases:["hbs","htmlbars"],extensions:[".handlebars",".hbs"],tmScope:"text.html.handlebars",aceMode:"handlebars",languageId:155}}}),vd=ee({"src/language-handlebars/index.js"(e,n){"use strict";re();var t=Nt(),s=gd(),a=yd(),r=[t(hd(),()=>({since:"2.3.0",parsers:["glimmer"],vscodeLanguageIds:["handlebars"]}))],u={glimmer:s};n.exports={languages:r,printers:u,parsers:a}}}),Cd=ee({"src/language-graphql/pragma.js"(e,n){"use strict";re();function t(a){return/^\s*#[^\S\n]*@(?:format|prettier)\s*(?:\n|$)/.test(a)}function s(a){return`# @format

`+a}n.exports={hasPragma:t,insertPragma:s}}}),Ed=ee({"src/language-graphql/loc.js"(e,n){"use strict";re();function t(a){return typeof a.start=="number"?a.start:a.loc&&a.loc.start}function s(a){return typeof a.end=="number"?a.end:a.loc&&a.loc.end}n.exports={locStart:t,locEnd:s}}}),Fd=ee({"src/language-graphql/printer-graphql.js"(e,n){"use strict";re();var{builders:{join:t,hardline:s,line:a,softline:r,group:u,indent:i,ifBreak:o}}=qe(),{isNextLineEmpty:c,isNonEmptyArray:y}=Ge(),{insertPragma:h}=Cd(),{locStart:g,locEnd:p}=Ed();function D(P,R,f){let x=P.getValue();if(!x)return"";if(typeof x=="string")return x;switch(x.kind){case"Document":{let m=[];return P.each((E,l,d)=>{m.push(f()),l!==d.length-1&&(m.push(s),c(R.originalText,E.getValue(),p)&&m.push(s))},"definitions"),[...m,s]}case"OperationDefinition":{let m=R.originalText[g(x)]!=="{",E=Boolean(x.name);return[m?x.operation:"",m&&E?[" ",f("name")]:"",m&&!E&&y(x.variableDefinitions)?" ":"",y(x.variableDefinitions)?u(["(",i([r,t([o("",", "),r],P.map(f,"variableDefinitions"))]),r,")"]):"",v(P,f,x),x.selectionSet?!m&&!E?"":" ":"",f("selectionSet")]}case"FragmentDefinition":return["fragment ",f("name"),y(x.variableDefinitions)?u(["(",i([r,t([o("",", "),r],P.map(f,"variableDefinitions"))]),r,")"]):""," on ",f("typeCondition"),v(P,f,x)," ",f("selectionSet")];case"SelectionSet":return["{",i([s,t(s,w(P,R,f,"selections"))]),s,"}"];case"Field":return u([x.alias?[f("alias"),": "]:"",f("name"),x.arguments.length>0?u(["(",i([r,t([o("",", "),r],w(P,R,f,"arguments"))]),r,")"]):"",v(P,f,x),x.selectionSet?" ":"",f("selectionSet")]);case"Name":return x.value;case"StringValue":{if(x.block){let m=x.value.replace(/"""/g,"\\$&").split(`
`);return m.length===1&&(m[0]=m[0].trim()),m.every(E=>E==="")&&(m.length=0),t(s,['"""',...m,'"""'])}return['"',x.value.replace(/["\\]/g,"\\$&").replace(/\n/g,"\\n"),'"']}case"IntValue":case"FloatValue":case"EnumValue":return x.value;case"BooleanValue":return x.value?"true":"false";case"NullValue":return"null";case"Variable":return["$",f("name")];case"ListValue":return u(["[",i([r,t([o("",", "),r],P.map(f,"values"))]),r,"]"]);case"ObjectValue":return u(["{",R.bracketSpacing&&x.fields.length>0?" ":"",i([r,t([o("",", "),r],P.map(f,"fields"))]),r,o("",R.bracketSpacing&&x.fields.length>0?" ":""),"}"]);case"ObjectField":case"Argument":return[f("name"),": ",f("value")];case"Directive":return["@",f("name"),x.arguments.length>0?u(["(",i([r,t([o("",", "),r],w(P,R,f,"arguments"))]),r,")"]):""];case"NamedType":return f("name");case"VariableDefinition":return[f("variable"),": ",f("type"),x.defaultValue?[" = ",f("defaultValue")]:"",v(P,f,x)];case"ObjectTypeExtension":case"ObjectTypeDefinition":return[f("description"),x.description?s:"",x.kind==="ObjectTypeExtension"?"extend ":"","type ",f("name"),x.interfaces.length>0?[" implements ",...A(P,R,f)]:"",v(P,f,x),x.fields.length>0?[" {",i([s,t(s,w(P,R,f,"fields"))]),s,"}"]:""];case"FieldDefinition":return[f("description"),x.description?s:"",f("name"),x.arguments.length>0?u(["(",i([r,t([o("",", "),r],w(P,R,f,"arguments"))]),r,")"]):"",": ",f("type"),v(P,f,x)];case"DirectiveDefinition":return[f("description"),x.description?s:"","directive ","@",f("name"),x.arguments.length>0?u(["(",i([r,t([o("",", "),r],w(P,R,f,"arguments"))]),r,")"]):"",x.repeatable?" repeatable":""," on ",t(" | ",P.map(f,"locations"))];case"EnumTypeExtension":case"EnumTypeDefinition":return[f("description"),x.description?s:"",x.kind==="EnumTypeExtension"?"extend ":"","enum ",f("name"),v(P,f,x),x.values.length>0?[" {",i([s,t(s,w(P,R,f,"values"))]),s,"}"]:""];case"EnumValueDefinition":return[f("description"),x.description?s:"",f("name"),v(P,f,x)];case"InputValueDefinition":return[f("description"),x.description?x.description.block?s:a:"",f("name"),": ",f("type"),x.defaultValue?[" = ",f("defaultValue")]:"",v(P,f,x)];case"InputObjectTypeExtension":case"InputObjectTypeDefinition":return[f("description"),x.description?s:"",x.kind==="InputObjectTypeExtension"?"extend ":"","input ",f("name"),v(P,f,x),x.fields.length>0?[" {",i([s,t(s,w(P,R,f,"fields"))]),s,"}"]:""];case"SchemaExtension":return["extend schema",v(P,f,x),...x.operationTypes.length>0?[" {",i([s,t(s,w(P,R,f,"operationTypes"))]),s,"}"]:[]];case"SchemaDefinition":return[f("description"),x.description?s:"","schema",v(P,f,x)," {",x.operationTypes.length>0?i([s,t(s,w(P,R,f,"operationTypes"))]):"",s,"}"];case"OperationTypeDefinition":return[f("operation"),": ",f("type")];case"InterfaceTypeExtension":case"InterfaceTypeDefinition":return[f("description"),x.description?s:"",x.kind==="InterfaceTypeExtension"?"extend ":"","interface ",f("name"),x.interfaces.length>0?[" implements ",...A(P,R,f)]:"",v(P,f,x),x.fields.length>0?[" {",i([s,t(s,w(P,R,f,"fields"))]),s,"}"]:""];case"FragmentSpread":return["...",f("name"),v(P,f,x)];case"InlineFragment":return["...",x.typeCondition?[" on ",f("typeCondition")]:"",v(P,f,x)," ",f("selectionSet")];case"UnionTypeExtension":case"UnionTypeDefinition":return u([f("description"),x.description?s:"",u([x.kind==="UnionTypeExtension"?"extend ":"","union ",f("name"),v(P,f,x),x.types.length>0?[" =",o(""," "),i([o([a,"  "]),t([a,"| "],P.map(f,"types"))])]:""])]);case"ScalarTypeExtension":case"ScalarTypeDefinition":return[f("description"),x.description?s:"",x.kind==="ScalarTypeExtension"?"extend ":"","scalar ",f("name"),v(P,f,x)];case"NonNullType":return[f("type"),"!"];case"ListType":return["[",f("type"),"]"];default:throw new Error("unknown graphql type: "+JSON.stringify(x.kind))}}function v(P,R,f){if(f.directives.length===0)return"";let x=t(a,P.map(R,"directives"));return f.kind==="FragmentDefinition"||f.kind==="OperationDefinition"?u([a,x]):[" ",u(i([r,x]))]}function w(P,R,f,x){return P.map((m,E,l)=>{let d=f();return E<l.length-1&&c(R.originalText,m.getValue(),p)?[d,s]:d},x)}function T(P){return P.kind&&P.kind!=="Comment"}function F(P){let R=P.getValue();if(R.kind==="Comment")return"#"+R.value.trimEnd();throw new Error("Not a comment: "+JSON.stringify(R))}function A(P,R,f){let x=P.getNode(),m=[],{interfaces:E}=x,l=P.map(d=>f(d),"interfaces");for(let d=0;d<E.length;d++){let C=E[d];m.push(l[d]);let _=E[d+1];if(_){let b=R.originalText.slice(C.loc.end,_.loc.start),N=b.includes("#"),I=b.replace(/#.*/g,"").trim();m.push(I===","?",":" &",N?a:" ")}}return m}function B(P,R){P.kind==="StringValue"&&P.block&&!P.value.includes(`
`)&&(R.value=R.value.trim())}B.ignoredProperties=new Set(["loc","comments"]);function k(P){var R;let f=P.getValue();return f==null||(R=f.comments)===null||R===void 0?void 0:R.some(x=>x.value.trim()==="prettier-ignore")}n.exports={print:D,massageAstNode:B,hasPrettierIgnore:k,insertPragma:h,printComment:F,canAttachComment:T}}}),Ad=ee({"src/language-graphql/options.js"(e,n){"use strict";re();var t=jt();n.exports={bracketSpacing:t.bracketSpacing}}}),Sd=ee({"src/language-graphql/parsers.js"(){re()}}),xd=ee({"node_modules/linguist-languages/data/GraphQL.json"(e,n){n.exports={name:"GraphQL",type:"data",color:"#e10098",extensions:[".graphql",".gql",".graphqls"],tmScope:"source.graphql",aceMode:"text",languageId:139}}}),bd=ee({"src/language-graphql/index.js"(e,n){"use strict";re();var t=Nt(),s=Fd(),a=Ad(),r=Sd(),u=[t(xd(),()=>({since:"1.5.0",parsers:["graphql"],vscodeLanguageIds:["graphql"]}))],i={graphql:s};n.exports={languages:u,options:a,printers:i,parsers:r}}}),no=ee({"src/language-markdown/loc.js"(e,n){"use strict";re();function t(a){return a.position.start.offset}function s(a){return a.position.end.offset}n.exports={locStart:t,locEnd:s}}}),Td=ee({"src/language-markdown/constants.evaluate.js"(e,n){n.exports={cjkPattern:"(?:[\\u02ea-\\u02eb\\u1100-\\u11ff\\u2e80-\\u2e99\\u2e9b-\\u2ef3\\u2f00-\\u2fd5\\u2ff0-\\u303f\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u3190-\\u3191\\u3196-\\u31ba\\u31c0-\\u31e3\\u31f0-\\u321e\\u322a-\\u3247\\u3260-\\u327e\\u328a-\\u32b0\\u32c0-\\u32cb\\u32d0-\\u3370\\u337b-\\u337f\\u33e0-\\u33fe\\u3400-\\u4db5\\u4e00-\\u9fef\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufe10-\\ufe1f\\ufe30-\\ufe6f\\uff00-\\uffef]|[\\ud840-\\ud868\\ud86a-\\ud86c\\ud86f-\\ud872\\ud874-\\ud879][\\udc00-\\udfff]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67]|\\ud83c[\\ude00\\ude50-\\ude51]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d])(?:[\\ufe00-\\ufe0f]|\\udb40[\\udd00-\\uddef])?",kPattern:"[\\u1100-\\u11ff\\u3001-\\u3003\\u3008-\\u3011\\u3013-\\u301f\\u302e-\\u3030\\u3037\\u30fb\\u3131-\\u318e\\u3200-\\u321e\\u3260-\\u327e\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\ufe45-\\ufe46\\uff61-\\uff65\\uffa0-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]",punctuationPattern:"[\\u0021-\\u002f\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e\\u00a1\\u00a7\\u00ab\\u00b6-\\u00b7\\u00bb\\u00bf\\u037e\\u0387\\u055a-\\u055f\\u0589-\\u058a\\u05be\\u05c0\\u05c3\\u05c6\\u05f3-\\u05f4\\u0609-\\u060a\\u060c-\\u060d\\u061b\\u061e-\\u061f\\u066a-\\u066d\\u06d4\\u0700-\\u070d\\u07f7-\\u07f9\\u0830-\\u083e\\u085e\\u0964-\\u0965\\u0970\\u09fd\\u0a76\\u0af0\\u0c77\\u0c84\\u0df4\\u0e4f\\u0e5a-\\u0e5b\\u0f04-\\u0f12\\u0f14\\u0f3a-\\u0f3d\\u0f85\\u0fd0-\\u0fd4\\u0fd9-\\u0fda\\u104a-\\u104f\\u10fb\\u1360-\\u1368\\u1400\\u166e\\u169b-\\u169c\\u16eb-\\u16ed\\u1735-\\u1736\\u17d4-\\u17d6\\u17d8-\\u17da\\u1800-\\u180a\\u1944-\\u1945\\u1a1e-\\u1a1f\\u1aa0-\\u1aa6\\u1aa8-\\u1aad\\u1b5a-\\u1b60\\u1bfc-\\u1bff\\u1c3b-\\u1c3f\\u1c7e-\\u1c7f\\u1cc0-\\u1cc7\\u1cd3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205e\\u207d-\\u207e\\u208d-\\u208e\\u2308-\\u230b\\u2329-\\u232a\\u2768-\\u2775\\u27c5-\\u27c6\\u27e6-\\u27ef\\u2983-\\u2998\\u29d8-\\u29db\\u29fc-\\u29fd\\u2cf9-\\u2cfc\\u2cfe-\\u2cff\\u2d70\\u2e00-\\u2e2e\\u2e30-\\u2e4f\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301f\\u3030\\u303d\\u30a0\\u30fb\\ua4fe-\\ua4ff\\ua60d-\\ua60f\\ua673\\ua67e\\ua6f2-\\ua6f7\\ua874-\\ua877\\ua8ce-\\ua8cf\\ua8f8-\\ua8fa\\ua8fc\\ua92e-\\ua92f\\ua95f\\ua9c1-\\ua9cd\\ua9de-\\ua9df\\uaa5c-\\uaa5f\\uaade-\\uaadf\\uaaf0-\\uaaf1\\uabeb\\ufd3e-\\ufd3f\\ufe10-\\ufe19\\ufe30-\\ufe52\\ufe54-\\ufe61\\ufe63\\ufe68\\ufe6a-\\ufe6b\\uff01-\\uff03\\uff05-\\uff0a\\uff0c-\\uff0f\\uff1a-\\uff1b\\uff1f-\\uff20\\uff3b-\\uff3d\\uff3f\\uff5b\\uff5d\\uff5f-\\uff65]|\\ud800[\\udd00-\\udd02\\udf9f\\udfd0]|\\ud801[\\udd6f]|\\ud802[\\udc57\\udd1f\\udd3f\\ude50-\\ude58\\ude7f\\udef0-\\udef6\\udf39-\\udf3f\\udf99-\\udf9c]|\\ud803[\\udf55-\\udf59]|\\ud804[\\udc47-\\udc4d\\udcbb-\\udcbc\\udcbe-\\udcc1\\udd40-\\udd43\\udd74-\\udd75\\uddc5-\\uddc8\\uddcd\\udddb\\udddd-\\udddf\\ude38-\\ude3d\\udea9]|\\ud805[\\udc4b-\\udc4f\\udc5b\\udc5d\\udcc6\\uddc1-\\uddd7\\ude41-\\ude43\\ude60-\\ude6c\\udf3c-\\udf3e]|\\ud806[\\udc3b\\udde2\\ude3f-\\ude46\\ude9a-\\ude9c\\ude9e-\\udea2]|\\ud807[\\udc41-\\udc45\\udc70-\\udc71\\udef7-\\udef8\\udfff]|\\ud809[\\udc70-\\udc74]|\\ud81a[\\ude6e-\\ude6f\\udef5\\udf37-\\udf3b\\udf44]|\\ud81b[\\ude97-\\ude9a\\udfe2]|\\ud82f[\\udc9f]|\\ud836[\\ude87-\\ude8b]|\\ud83a[\\udd5e-\\udd5f]"}}}),zn=ee({"src/language-markdown/utils.js"(e,n){"use strict";re();var{getLast:t}=Ge(),{locStart:s,locEnd:a}=no(),{cjkPattern:r,kPattern:u,punctuationPattern:i}=Td(),o=["liquidNode","inlineCode","emphasis","esComment","strong","delete","wikiLink","link","linkReference","image","imageReference","footnote","footnoteReference","sentence","whitespace","word","break","inlineMath"],c=[...o,"tableCell","paragraph","heading"],y=new RegExp(u),h=new RegExp(i);function g(F,A){let B="non-cjk",k="cj-letter",P="k-letter",R="cjk-punctuation",f=[],x=(A.proseWrap==="preserve"?F:F.replace(new RegExp(`(${r})
(${r})`,"g"),"$1$2")).split(/([\t\n ]+)/);for(let[E,l]of x.entries()){if(E%2===1){f.push({type:"whitespace",value:/\n/.test(l)?`
`:" "});continue}if((E===0||E===x.length-1)&&l==="")continue;let d=l.split(new RegExp(`(${r})`));for(let[C,_]of d.entries())if(!((C===0||C===d.length-1)&&_==="")){if(C%2===0){_!==""&&m({type:"word",value:_,kind:B,hasLeadingPunctuation:h.test(_[0]),hasTrailingPunctuation:h.test(t(_))});continue}m(h.test(_)?{type:"word",value:_,kind:R,hasLeadingPunctuation:!0,hasTrailingPunctuation:!0}:{type:"word",value:_,kind:y.test(_)?P:k,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1})}}return f;function m(E){let l=t(f);l&&l.type==="word"&&(l.kind===B&&E.kind===k&&!l.hasTrailingPunctuation||l.kind===k&&E.kind===B&&!E.hasLeadingPunctuation?f.push({type:"whitespace",value:" "}):!d(B,R)&&![l.value,E.value].some(C=>/\u3000/.test(C))&&f.push({type:"whitespace",value:""})),f.push(E);function d(C,_){return l.kind===C&&E.kind===_||l.kind===_&&E.kind===C}}}function p(F,A){let[,B,k,P]=A.slice(F.position.start.offset,F.position.end.offset).match(/^\s*(\d+)(\.|\))(\s*)/);return{numberText:B,marker:k,leadingSpaces:P}}function D(F,A){if(!F.ordered||F.children.length<2)return!1;let B=Number(p(F.children[0],A.originalText).numberText),k=Number(p(F.children[1],A.originalText).numberText);if(B===0&&F.children.length>2){let P=Number(p(F.children[2],A.originalText).numberText);return k===1&&P===1}return k===1}function v(F,A){let{value:B}=F;return F.position.end.offset===A.length&&B.endsWith(`
`)&&A.endsWith(`
`)?B.slice(0,-1):B}function w(F,A){return function B(k,P,R){let f=Object.assign({},A(k,P,R));return f.children&&(f.children=f.children.map((x,m)=>B(x,m,[f,...R]))),f}(F,null,[])}function T(F){if((F==null?void 0:F.type)!=="link"||F.children.length!==1)return!1;let[A]=F.children;return s(F)===s(A)&&a(F)===a(A)}n.exports={mapAst:w,splitText:g,punctuationPattern:i,getFencedCodeBlockValue:v,getOrderedListItemInfo:p,hasGitDiffFriendlyOrderedList:D,INLINE_NODE_TYPES:o,INLINE_NODE_WRAPPER_TYPES:c,isAutolink:T}}}),Bd=ee({"src/language-markdown/embed.js"(e,n){"use strict";re();var{inferParserByLanguage:t,getMaxContinuousCount:s}=Ge(),{builders:{hardline:a,markAsRoot:r},utils:{replaceEndOfLine:u}}=qe(),i=Jn(),{getFencedCodeBlockValue:o}=zn();function c(y,h,g,p){let D=y.getValue();if(D.type==="code"&&D.lang!==null){let v=t(D.lang,p);if(v){let w=p.__inJsTemplate?"~":"`",T=w.repeat(Math.max(3,s(D.value,w)+1)),F={parser:v};D.lang==="tsx"&&(F.filepath="dummy.tsx");let A=g(o(D,p.originalText),F,{stripTrailingHardline:!0});return r([T,D.lang,D.meta?" "+D.meta:"",a,u(A),a,T])}}switch(D.type){case"front-matter":return i(D,g);case"importExport":return[g(D.value,{parser:"babel"},{stripTrailingHardline:!0}),a];case"jsx":return g(`<$>${D.value}</$>`,{parser:"__js_expression",rootMarker:"mdx"},{stripTrailingHardline:!0})}return null}n.exports=c}}),uo=ee({"src/language-markdown/pragma.js"(e,n){"use strict";re();var t=ro(),s=["format","prettier"];function a(r){let u=`@(${s.join("|")})`,i=new RegExp([`<!--\\s*${u}\\s*-->`,`{\\s*\\/\\*\\s*${u}\\s*\\*\\/\\s*}`,`<!--.*\r?
[\\s\\S]*(^|
)[^\\S
]*${u}[^\\S
]*($|
)[\\s\\S]*
.*-->`].join("|"),"m"),o=r.match(i);return(o==null?void 0:o.index)===0}n.exports={startWithPragma:a,hasPragma:r=>a(t(r).content.trimStart()),insertPragma:r=>{let u=t(r),i=`<!-- @${s[0]} -->`;return u.frontMatter?`${u.frontMatter.raw}

${i}

${u.content}`:`${i}

${u.content}`}}}}),Nd=ee({"src/language-markdown/print-preprocess.js"(e,n){"use strict";re();var t=lt(),{getOrderedListItemInfo:s,mapAst:a,splitText:r}=zn(),u=/^.$/su;function i(T,F){return T=y(T,F),T=p(T),T=c(T,F),T=v(T,F),T=w(T,F),T=D(T,F),T=o(T),T=h(T),T}function o(T){return a(T,F=>F.type!=="import"&&F.type!=="export"?F:Object.assign(Object.assign({},F),{},{type:"importExport"}))}function c(T,F){return a(T,A=>A.type!=="inlineCode"||F.proseWrap==="preserve"?A:Object.assign(Object.assign({},A),{},{value:A.value.replace(/\s+/g," ")}))}function y(T,F){return a(T,A=>A.type!=="text"||A.value==="*"||A.value==="_"||!u.test(A.value)||A.position.end.offset-A.position.start.offset===A.value.length?A:Object.assign(Object.assign({},A),{},{value:F.originalText.slice(A.position.start.offset,A.position.end.offset)}))}function h(T){return g(T,(F,A)=>F.type==="importExport"&&A.type==="importExport",(F,A)=>({type:"importExport",value:F.value+`

`+A.value,position:{start:F.position.start,end:A.position.end}}))}function g(T,F,A){return a(T,B=>{if(!B.children)return B;let k=B.children.reduce((P,R)=>{let f=t(P);return f&&F(f,R)?P.splice(-1,1,A(f,R)):P.push(R),P},[]);return Object.assign(Object.assign({},B),{},{children:k})})}function p(T){return g(T,(F,A)=>F.type==="text"&&A.type==="text",(F,A)=>({type:"text",value:F.value+A.value,position:{start:F.position.start,end:A.position.end}}))}function D(T,F){return a(T,(A,B,k)=>{let[P]=k;if(A.type!=="text")return A;let{value:R}=A;return P.type==="paragraph"&&(B===0&&(R=R.trimStart()),B===P.children.length-1&&(R=R.trimEnd())),{type:"sentence",position:A.position,children:r(R,F)}})}function v(T,F){return a(T,(A,B,k)=>{if(A.type==="code"){let P=/^\n?(?: {4,}|\t)/.test(F.originalText.slice(A.position.start.offset,A.position.end.offset));if(A.isIndented=P,P)for(let R=0;R<k.length;R++){let f=k[R];if(f.hasIndentedCodeblock)break;f.type==="list"&&(f.hasIndentedCodeblock=!0)}}return A})}function w(T,F){return a(T,(k,P,R)=>{if(k.type==="list"&&k.children.length>0){for(let f=0;f<R.length;f++){let x=R[f];if(x.type==="list"&&!x.isAligned)return k.isAligned=!1,k}k.isAligned=B(k)}return k});function A(k){return k.children.length===0?-1:k.children[0].position.start.column-1}function B(k){if(!k.ordered)return!0;let[P,R]=k.children;if(s(P,F.originalText).leadingSpaces.length>1)return!0;let x=A(P);if(x===-1)return!1;if(k.children.length===1)return x%F.tabWidth===0;let m=A(R);return x!==m?!1:x%F.tabWidth===0?!0:s(R,F.originalText).leadingSpaces.length>1}}n.exports=i}}),wd=ee({"src/language-markdown/clean.js"(e,n){"use strict";re();var{isFrontMatterNode:t}=Ge(),{startWithPragma:s}=uo(),a=new Set(["position","raw"]);function r(u,i,o){if((u.type==="front-matter"||u.type==="code"||u.type==="yaml"||u.type==="import"||u.type==="export"||u.type==="jsx")&&delete i.value,u.type==="list"&&delete i.isAligned,(u.type==="list"||u.type==="listItem")&&(delete i.spread,delete i.loose),u.type==="text"||(u.type==="inlineCode"&&(i.value=u.value.replace(/[\t\n ]+/g," ")),u.type==="wikiLink"&&(i.value=u.value.trim().replace(/[\t\n]+/g," ")),(u.type==="definition"||u.type==="linkReference")&&(i.label=u.label.trim().replace(/[\t\n ]+/g," ").toLowerCase()),(u.type==="definition"||u.type==="link"||u.type==="image")&&u.title&&(i.title=u.title.replace(/\\(["')])/g,"$1")),o&&o.type==="root"&&o.children.length>0&&(o.children[0]===u||t(o.children[0])&&o.children[1]===u)&&u.type==="html"&&s(u.value)))return null}r.ignoredProperties=a,n.exports=r}}),_d=ee({"src/language-markdown/printer-markdown.js"(e,n){"use strict";re();var{getLast:t,getMinNotPresentContinuousCount:s,getMaxContinuousCount:a,getStringWidth:r,isNonEmptyArray:u}=Ge(),{builders:{breakParent:i,join:o,line:c,literalline:y,markAsRoot:h,hardline:g,softline:p,ifBreak:D,fill:v,align:w,indent:T,group:F,hardlineWithoutBreakParent:A},utils:{normalizeDoc:B,replaceTextEndOfLine:k},printer:{printDocToString:P}}=qe(),R=Bd(),{insertPragma:f}=uo(),{locStart:x,locEnd:m}=no(),E=Nd(),l=wd(),{getFencedCodeBlockValue:d,hasGitDiffFriendlyOrderedList:C,splitText:_,punctuationPattern:b,INLINE_NODE_TYPES:N,INLINE_NODE_WRAPPER_TYPES:I,isAutolink:$}=zn(),M=new Set(["importExport"]),q=["heading","tableCell","link","wikiLink"],J=new Set(["listItem","definition","footnoteDefinition"]);function L(pe,ie,ve){let ce=pe.getValue();if(fe(pe))return _(ie.originalText.slice(ce.position.start.offset,ce.position.end.offset),ie).map(U=>U.type==="word"?U.value:U.value===""?"":le(pe,U.value,ie));switch(ce.type){case"front-matter":return ie.originalText.slice(ce.position.start.offset,ce.position.end.offset);case"root":return ce.children.length===0?"":[B(X(pe,ie,ve)),M.has(Ae(ce).type)?"":g];case"paragraph":return oe(pe,ie,ve,{postprocessor:v});case"sentence":return oe(pe,ie,ve);case"word":{let U=ce.value.replace(/\*/g,"\\$&").replace(new RegExp([`(^|${b})(_+)`,`(_+)(${b}|$)`].join("|"),"g"),(he,Be,Se,ye,S)=>(Se?`${Be}${Se}`:`${ye}${S}`).replace(/_/g,"\\_")),de=(he,Be,Se)=>he.type==="sentence"&&Se===0,De=(he,Be,Se)=>$(he.children[Se-1]);return U!==ce.value&&(pe.match(void 0,de,De)||pe.match(void 0,de,(he,Be,Se)=>he.type==="emphasis"&&Se===0,De))&&(U=U.replace(/^(\\?[*_])+/,he=>he.replace(/\\/g,""))),U}case"whitespace":{let U=pe.getParentNode(),de=U.children.indexOf(ce),De=U.children[de+1],he=De&&/^>|^(?:[*+-]|#{1,6}|\d+[).])$/.test(De.value)?"never":ie.proseWrap;return le(pe,ce.value,{proseWrap:he})}case"emphasis":{let U;if($(ce.children[0]))U=ie.originalText[ce.position.start.offset];else{let de=pe.getParentNode(),De=de.children.indexOf(ce),he=de.children[De-1],Be=de.children[De+1];U=he&&he.type==="sentence"&&he.children.length>0&&t(he.children).type==="word"&&!t(he.children).hasTrailingPunctuation||Be&&Be.type==="sentence"&&Be.children.length>0&&Be.children[0].type==="word"&&!Be.children[0].hasLeadingPunctuation||Q(pe,"emphasis")?"*":"_"}return[U,oe(pe,ie,ve),U]}case"strong":return["**",oe(pe,ie,ve),"**"];case"delete":return["~~",oe(pe,ie,ve),"~~"];case"inlineCode":{let U=s(ce.value,"`"),de="`".repeat(U||1),De=U&&!/^\s/.test(ce.value)?" ":"";return[de,De,ce.value,De,de]}case"wikiLink":{let U="";return ie.proseWrap==="preserve"?U=ce.value:U=ce.value.replace(/[\t\n]+/g," "),["[[",U,"]]"]}case"link":switch(ie.originalText[ce.position.start.offset]){case"<":{let U="mailto:",de=ce.url.startsWith(U)&&ie.originalText.slice(ce.position.start.offset+1,ce.position.start.offset+1+U.length)!==U?ce.url.slice(U.length):ce.url;return["<",de,">"]}case"[":return["[",oe(pe,ie,ve),"](",ge(ce.url,")"),Ce(ce.title,ie),")"];default:return ie.originalText.slice(ce.position.start.offset,ce.position.end.offset)}case"image":return["![",ce.alt||"","](",ge(ce.url,")"),Ce(ce.title,ie),")"];case"blockquote":return["> ",w("> ",oe(pe,ie,ve))];case"heading":return["#".repeat(ce.depth)+" ",oe(pe,ie,ve)];case"code":{if(ce.isIndented){let De=" ".repeat(4);return w(De,[De,...k(ce.value,g)])}let U=ie.__inJsTemplate?"~":"`",de=U.repeat(Math.max(3,a(ce.value,U)+1));return[de,ce.lang||"",ce.meta?" "+ce.meta:"",g,...k(d(ce,ie.originalText),g),g,de]}case"html":{let U=pe.getParentNode(),de=U.type==="root"&&t(U.children)===ce?ce.value.trimEnd():ce.value,De=/^<!--.*-->$/s.test(de);return k(de,De?g:h(y))}case"list":{let U=O(ce,pe.getParentNode()),de=C(ce,ie);return oe(pe,ie,ve,{processor:(De,he)=>{let Be=ye(),Se=De.getValue();if(Se.children.length===2&&Se.children[1].type==="html"&&Se.children[0].position.start.column!==Se.children[1].position.start.column)return[Be,Y(De,ie,ve,Be)];return[Be,w(" ".repeat(Be.length),Y(De,ie,ve,Be))];function ye(){let S=ce.ordered?(he===0?ce.start:de?1:ce.start+he)+(U%2===0?". ":") "):U%2===0?"- ":"* ";return ce.isAligned||ce.hasIndentedCodeblock?V(S,ie):S}}})}case"thematicBreak":{let U=se(pe,"list");return U===-1?"---":O(pe.getParentNode(U),pe.getParentNode(U+1))%2===0?"***":"---"}case"linkReference":return["[",oe(pe,ie,ve),"]",ce.referenceType==="full"?["[",ce.identifier,"]"]:ce.referenceType==="collapsed"?"[]":""];case"imageReference":switch(ce.referenceType){case"full":return["![",ce.alt||"","][",ce.identifier,"]"];default:return["![",ce.alt,"]",ce.referenceType==="collapsed"?"[]":""]}case"definition":{let U=ie.proseWrap==="always"?c:" ";return F(["[",ce.identifier,"]:",T([U,ge(ce.url),ce.title===null?"":[U,Ce(ce.title,ie,!1)]])])}case"footnote":return["[^",oe(pe,ie,ve),"]"];case"footnoteReference":return["[^",ce.identifier,"]"];case"footnoteDefinition":{let U=pe.getParentNode().children[pe.getName()+1],de=ce.children.length===1&&ce.children[0].type==="paragraph"&&(ie.proseWrap==="never"||ie.proseWrap==="preserve"&&ce.children[0].position.start.line===ce.children[0].position.end.line);return["[^",ce.identifier,"]: ",de?oe(pe,ie,ve):F([w(" ".repeat(4),oe(pe,ie,ve,{processor:(De,he)=>he===0?F([p,ve()]):ve()})),U&&U.type==="footnoteDefinition"?p:""])]}case"table":return W(pe,ie,ve);case"tableCell":return oe(pe,ie,ve);case"break":return/\s/.test(ie.originalText[ce.position.start.offset])?["  ",h(y)]:["\\",g];case"liquidNode":return k(ce.value,g);case"importExport":return[ce.value,g];case"esComment":return["{/* ",ce.value," */}"];case"jsx":return ce.value;case"math":return["$$",g,ce.value?[...k(ce.value,g),g]:"","$$"];case"inlineMath":return ie.originalText.slice(x(ce),m(ce));case"tableRow":case"listItem":default:throw new Error(`Unknown markdown type ${JSON.stringify(ce.type)}`)}}function Y(pe,ie,ve,ce){let U=pe.getValue(),de=U.checked===null?"":U.checked?"[x] ":"[ ] ";return[de,oe(pe,ie,ve,{processor:(De,he)=>{if(he===0&&De.getValue().type!=="list")return w(" ".repeat(de.length),ve());let Be=" ".repeat(_e(ie.tabWidth-ce.length,0,3));return[Be,w(Be,ve())]}})]}function V(pe,ie){let ve=ce();return pe+" ".repeat(ve>=4?0:ve);function ce(){let U=pe.length%ie.tabWidth;return U===0?0:ie.tabWidth-U}}function O(pe,ie){return K(pe,ie,ve=>ve.ordered===pe.ordered)}function K(pe,ie,ve){let ce=-1;for(let U of ie.children)if(U.type===pe.type&&ve(U)?ce++:ce=-1,U===pe)return ce}function se(pe,ie){let ve=Array.isArray(ie)?ie:[ie],ce=-1,U;for(;U=pe.getParentNode(++ce);)if(ve.includes(U.type))return ce;return-1}function Q(pe,ie){let ve=se(pe,ie);return ve===-1?null:pe.getParentNode(ve)}function le(pe,ie,ve){if(ve.proseWrap==="preserve"&&ie===`
`)return g;let ce=ve.proseWrap==="always"&&!Q(pe,q);return ie!==""?ce?c:" ":ce?p:""}function W(pe,ie,ve){let ce=pe.getValue(),U=[],de=pe.map(S=>S.map((G,te)=>{let Ee=P(ve(),ie).formatted,Re=r(Ee);return U[te]=Math.max(U[te]||3,Re),{text:Ee,width:Re}},"children"),"children"),De=Be(!1);if(ie.proseWrap!=="never")return[i,De];let he=Be(!0);return[i,F(D(he,De))];function Be(S){let G=[ye(de[0],S),Se(S)];return de.length>1&&G.push(o(A,de.slice(1).map(te=>ye(te,S)))),o(A,G)}function Se(S){return`| ${U.map((te,Ee)=>{let Re=ce.align[Ee],Te=Re==="center"||Re==="left"?":":"-",Pe=Re==="center"||Re==="right"?":":"-",Fe=S?"-":"-".repeat(te-2);return`${Te}${Fe}${Pe}`}).join(" | ")} |`}function ye(S,G){return`| ${S.map((Ee,Re)=>{let{text:Te,width:Pe}=Ee;if(G)return Te;let Fe=U[Re]-Pe,Ze=ce.align[Re],xe=0;Ze==="right"?xe=Fe:Ze==="center"&&(xe=Math.floor(Fe/2));let Je=Fe-xe;return`${" ".repeat(xe)}${Te}${" ".repeat(Je)}`}).join(" | ")} |`}}function X(pe,ie,ve){let ce=[],U=null,{children:de}=pe.getValue();for(let[De,he]of de.entries())switch(z(he)){case"start":U===null&&(U={index:De,offset:he.position.end.offset});break;case"end":U!==null&&(ce.push({start:U,end:{index:De,offset:he.position.start.offset}}),U=null);break;default:break}return oe(pe,ie,ve,{processor:(De,he)=>{if(ce.length>0){let Be=ce[0];if(he===Be.start.index)return[ae(de[Be.start.index]),ie.originalText.slice(Be.start.offset,Be.end.offset),ae(de[Be.end.index])];if(Be.start.index<he&&he<Be.end.index)return!1;if(he===Be.end.index)return ce.shift(),!1}return ve()}})}function oe(pe,ie,ve){let ce=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},{postprocessor:U}=ce,de=ce.processor||(()=>ve()),De=pe.getValue(),he=[],Be;return pe.each((Se,ye)=>{let S=Se.getValue(),G=de(Se,ye);if(G!==!1){let te={parts:he,prevNode:Be,parentNode:De,options:ie};H(S,te)&&(he.push(g),Be&&M.has(Be.type)||(Z(S,te)||ne(S,te))&&he.push(g),ne(S,te)&&he.push(g)),he.push(G),Be=S}},"children"),U?U(he):he}function ae(pe){if(pe.type==="html")return pe.value;if(pe.type==="paragraph"&&Array.isArray(pe.children)&&pe.children.length===1&&pe.children[0].type==="esComment")return["{/* ",pe.children[0].value," */}"]}function Ae(pe){let ie=pe;for(;u(ie.children);)ie=t(ie.children);return ie}function z(pe){let ie;if(pe.type==="html")ie=pe.value.match(/^<!--\s*prettier-ignore(?:-(start|end))?\s*-->$/);else{let ve;pe.type==="esComment"?ve=pe:pe.type==="paragraph"&&pe.children.length===1&&pe.children[0].type==="esComment"&&(ve=pe.children[0]),ve&&(ie=ve.value.match(/^prettier-ignore(?:-(start|end))?$/))}return ie?ie[1]||"next":!1}function H(pe,ie){let ve=ie.parts.length===0,ce=N.includes(pe.type),U=pe.type==="html"&&I.includes(ie.parentNode.type);return!ve&&!ce&&!U}function Z(pe,ie){var ve,ce,U;let De=(ie.prevNode&&ie.prevNode.type)===pe.type&&J.has(pe.type),he=ie.parentNode.type==="listItem"&&!ie.parentNode.loose,Be=((ve=ie.prevNode)===null||ve===void 0?void 0:ve.type)==="listItem"&&ie.prevNode.loose,Se=z(ie.prevNode)==="next",ye=pe.type==="html"&&((ce=ie.prevNode)===null||ce===void 0?void 0:ce.type)==="html"&&ie.prevNode.position.end.line+1===pe.position.start.line,S=pe.type==="html"&&ie.parentNode.type==="listItem"&&((U=ie.prevNode)===null||U===void 0?void 0:U.type)==="paragraph"&&ie.prevNode.position.end.line+1===pe.position.start.line;return Be||!(De||he||Se||ye||S)}function ne(pe,ie){let ve=ie.prevNode&&ie.prevNode.type==="list",ce=pe.type==="code"&&pe.isIndented;return ve&&ce}function fe(pe){let ie=Q(pe,["linkReference","imageReference"]);return ie&&(ie.type!=="linkReference"||ie.referenceType!=="full")}function ge(pe){let ie=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],ve=[" ",...Array.isArray(ie)?ie:[ie]];return new RegExp(ve.map(ce=>`\\${ce}`).join("|")).test(pe)?`<${pe}>`:pe}function Ce(pe,ie){let ve=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!pe)return"";if(ve)return" "+Ce(pe,ie,!1);if(pe=pe.replace(/\\(["')])/g,"$1"),pe.includes('"')&&pe.includes("'")&&!pe.includes(")"))return`(${pe})`;let ce=pe.split("'").length-1,U=pe.split('"').length-1,de=ce>U?'"':U>ce||ie.singleQuote?"'":'"';return pe=pe.replace(/\\/,"\\\\"),pe=pe.replace(new RegExp(`(${de})`,"g"),"\\$1"),`${de}${pe}${de}`}function _e(pe,ie,ve){return pe<ie?ie:pe>ve?ve:pe}function Oe(pe){let ie=Number(pe.getName());if(ie===0)return!1;let ve=pe.getParentNode().children[ie-1];return z(ve)==="next"}n.exports={preprocess:E,print:L,embed:R,massageAstNode:l,hasPrettierIgnore:Oe,insertPragma:f}}}),Pd=ee({"src/language-markdown/options.js"(e,n){"use strict";re();var t=jt();n.exports={proseWrap:t.proseWrap,singleQuote:t.singleQuote}}}),kd=ee({"src/language-markdown/parsers.js"(){re()}}),na=ee({"node_modules/linguist-languages/data/Markdown.json"(e,n){n.exports={name:"Markdown",type:"prose",color:"#083fa1",aliases:["pandoc"],aceMode:"markdown",codemirrorMode:"gfm",codemirrorMimeType:"text/x-gfm",wrap:!0,extensions:[".md",".livemd",".markdown",".mdown",".mdwn",".mdx",".mkd",".mkdn",".mkdown",".ronn",".scd",".workbook"],filenames:["contents.lr"],tmScope:"source.gfm",languageId:222}}}),Id=ee({"src/language-markdown/index.js"(e,n){"use strict";re();var t=Nt(),s=_d(),a=Pd(),r=kd(),u=[t(na(),o=>({since:"1.8.0",parsers:["markdown"],vscodeLanguageIds:["markdown"],filenames:[...o.filenames,"README"],extensions:o.extensions.filter(c=>c!==".mdx")})),t(na(),()=>({name:"MDX",since:"1.15.0",parsers:["mdx"],vscodeLanguageIds:["mdx"],filenames:[],extensions:[".mdx"]}))],i={mdast:s};n.exports={languages:u,options:a,printers:i,parsers:r}}}),Ld=ee({"src/language-html/clean.js"(e,n){"use strict";re();var{isFrontMatterNode:t}=Ge(),s=new Set(["sourceSpan","startSourceSpan","endSourceSpan","nameSpan","valueSpan"]);function a(r,u){if(r.type==="text"||r.type==="comment"||t(r)||r.type==="yaml"||r.type==="toml")return null;r.type==="attribute"&&delete u.value,r.type==="docType"&&delete u.value}a.ignoredProperties=s,n.exports=a}}),jd=ee({"src/language-html/constants.evaluate.js"(e,n){n.exports={CSS_DISPLAY_TAGS:{area:"none",base:"none",basefont:"none",datalist:"none",head:"none",link:"none",meta:"none",noembed:"none",noframes:"none",param:"block",rp:"none",script:"block",source:"block",style:"none",template:"inline",track:"block",title:"none",html:"block",body:"block",address:"block",blockquote:"block",center:"block",div:"block",figure:"block",figcaption:"block",footer:"block",form:"block",header:"block",hr:"block",legend:"block",listing:"block",main:"block",p:"block",plaintext:"block",pre:"block",xmp:"block",slot:"contents",ruby:"ruby",rt:"ruby-text",article:"block",aside:"block",h1:"block",h2:"block",h3:"block",h4:"block",h5:"block",h6:"block",hgroup:"block",nav:"block",section:"block",dir:"block",dd:"block",dl:"block",dt:"block",ol:"block",ul:"block",li:"list-item",table:"table",caption:"table-caption",colgroup:"table-column-group",col:"table-column",thead:"table-header-group",tbody:"table-row-group",tfoot:"table-footer-group",tr:"table-row",td:"table-cell",th:"table-cell",fieldset:"block",button:"inline-block",details:"block",summary:"block",dialog:"block",meter:"inline-block",progress:"inline-block",object:"inline-block",video:"inline-block",audio:"inline-block",select:"inline-block",option:"block",optgroup:"block"},CSS_DISPLAY_DEFAULT:"inline",CSS_WHITE_SPACE_TAGS:{listing:"pre",plaintext:"pre",pre:"pre",xmp:"pre",nobr:"nowrap",table:"initial",textarea:"pre-wrap"},CSS_WHITE_SPACE_DEFAULT:"normal"}}}),Od=ee({"src/language-html/utils/is-unknown-namespace.js"(e,n){"use strict";re();function t(s){return s.type==="element"&&!s.hasExplicitNamespace&&!["html","svg"].includes(s.namespace)}n.exports=t}}),Ot=ee({"src/language-html/utils/index.js"(e,n){"use strict";re();var{inferParserByLanguage:t,isFrontMatterNode:s}=Ge(),{builders:{line:a,hardline:r,join:u},utils:{getDocParts:i,replaceTextEndOfLine:o}}=qe(),{CSS_DISPLAY_TAGS:c,CSS_DISPLAY_DEFAULT:y,CSS_WHITE_SPACE_TAGS:h,CSS_WHITE_SPACE_DEFAULT:g}=jd(),p=Od(),D=new Set(["	",`
`,"\f","\r"," "]),v=S=>S.replace(/^[\t\n\f\r ]+/,""),w=S=>S.replace(/[\t\n\f\r ]+$/,""),T=S=>v(w(S)),F=S=>S.replace(/^[\t\f\r ]*\n/g,""),A=S=>F(w(S)),B=S=>S.split(/[\t\n\f\r ]+/),k=S=>S.match(/^[\t\n\f\r ]*/)[0],P=S=>{let[,G,te,Ee]=S.match(/^([\t\n\f\r ]*)(.*?)([\t\n\f\r ]*)$/s);return{leadingWhitespace:G,trailingWhitespace:Ee,text:te}},R=S=>/[\t\n\f\r ]/.test(S);function f(S,G){return!!(S.type==="ieConditionalComment"&&S.lastChild&&!S.lastChild.isSelfClosing&&!S.lastChild.endSourceSpan||S.type==="ieConditionalComment"&&!S.complete||ne(S)&&S.children.some(te=>te.type!=="text"&&te.type!=="interpolation")||De(S,G)&&!l(S)&&S.type!=="interpolation")}function x(S){return S.type==="attribute"||!S.parent||!S.prev?!1:m(S.prev)}function m(S){return S.type==="comment"&&S.value.trim()==="prettier-ignore"}function E(S){return S.type==="text"||S.type==="comment"}function l(S){return S.type==="element"&&(S.fullName==="script"||S.fullName==="style"||S.fullName==="svg:style"||p(S)&&(S.name==="script"||S.name==="style"))}function d(S){return S.children&&!l(S)}function C(S){return l(S)||S.type==="interpolation"||_(S)}function _(S){return _e(S).startsWith("pre")}function b(S,G){let te=Ee();if(te&&!S.prev&&S.parent&&S.parent.tagDefinition&&S.parent.tagDefinition.ignoreFirstLf)return S.type==="interpolation";return te;function Ee(){return s(S)?!1:(S.type==="text"||S.type==="interpolation")&&S.prev&&(S.prev.type==="text"||S.prev.type==="interpolation")?!0:!S.parent||S.parent.cssDisplay==="none"?!1:ne(S.parent)?!0:!(!S.prev&&(S.parent.type==="root"||ne(S)&&S.parent||l(S.parent)||U(S.parent,G)||!ae(S.parent.cssDisplay))||S.prev&&!H(S.prev.cssDisplay))}}function N(S,G){return s(S)?!1:(S.type==="text"||S.type==="interpolation")&&S.next&&(S.next.type==="text"||S.next.type==="interpolation")?!0:!S.parent||S.parent.cssDisplay==="none"?!1:ne(S.parent)?!0:!(!S.next&&(S.parent.type==="root"||ne(S)&&S.parent||l(S.parent)||U(S.parent,G)||!Ae(S.parent.cssDisplay))||S.next&&!z(S.next.cssDisplay))}function I(S){return Z(S.cssDisplay)&&!l(S)}function $(S){return s(S)||S.next&&S.sourceSpan.end&&S.sourceSpan.end.line+1<S.next.sourceSpan.start.line}function M(S){return q(S)||S.type==="element"&&S.children.length>0&&(["body","script","style"].includes(S.name)||S.children.some(G=>Q(G)))||S.firstChild&&S.firstChild===S.lastChild&&S.firstChild.type!=="text"&&V(S.firstChild)&&(!S.lastChild.isTrailingSpaceSensitive||O(S.lastChild))}function q(S){return S.type==="element"&&S.children.length>0&&(["html","head","ul","ol","select"].includes(S.name)||S.cssDisplay.startsWith("table")&&S.cssDisplay!=="table-cell")}function J(S){return K(S)||S.prev&&L(S.prev)||Y(S)}function L(S){return K(S)||S.type==="element"&&S.fullName==="br"||Y(S)}function Y(S){return V(S)&&O(S)}function V(S){return S.hasLeadingSpaces&&(S.prev?S.prev.sourceSpan.end.line<S.sourceSpan.start.line:S.parent.type==="root"||S.parent.startSourceSpan.end.line<S.sourceSpan.start.line)}function O(S){return S.hasTrailingSpaces&&(S.next?S.next.sourceSpan.start.line>S.sourceSpan.end.line:S.parent.type==="root"||S.parent.endSourceSpan&&S.parent.endSourceSpan.start.line>S.sourceSpan.end.line)}function K(S){switch(S.type){case"ieConditionalComment":case"comment":case"directive":return!0;case"element":return["script","select"].includes(S.name)}return!1}function se(S){return S.lastChild?se(S.lastChild):S}function Q(S){return S.children&&S.children.some(G=>G.type!=="text")}function le(S){let{type:G,lang:te}=S.attrMap;if(G==="module"||G==="text/javascript"||G==="text/babel"||G==="application/javascript"||te==="jsx")return"babel";if(G==="application/x-typescript"||te==="ts"||te==="tsx")return"typescript";if(G==="text/markdown")return"markdown";if(G==="text/html")return"html";if(G&&(G.endsWith("json")||G.endsWith("importmap"))||G==="speculationrules")return"json";if(G==="text/x-handlebars-template")return"glimmer"}function W(S,G){let{lang:te}=S.attrMap;if(!te||te==="postcss"||te==="css")return"css";if(te==="scss")return"scss";if(te==="less")return"less";if(te==="stylus")return t("stylus",G)}function X(S,G){if(S.name==="script"&&!S.attrMap.src)return!S.attrMap.lang&&!S.attrMap.type?"babel":le(S);if(S.name==="style")return W(S,G);if(G&&De(S,G))return le(S)||!("src"in S.attrMap)&&t(S.attrMap.lang,G)}function oe(S){return S==="block"||S==="list-item"||S.startsWith("table")}function ae(S){return!oe(S)&&S!=="inline-block"}function Ae(S){return!oe(S)&&S!=="inline-block"}function z(S){return!oe(S)}function H(S){return!oe(S)}function Z(S){return!oe(S)&&S!=="inline-block"}function ne(S){return _e(S).startsWith("pre")}function fe(S,G){let te=0;for(let Ee=S.stack.length-1;Ee>=0;Ee--){let Re=S.stack[Ee];Re&&typeof Re=="object"&&!Array.isArray(Re)&&G(Re)&&te++}return te}function ge(S,G){let te=S;for(;te;){if(G(te))return!0;te=te.parent}return!1}function Ce(S,G){if(S.prev&&S.prev.type==="comment"){let Ee=S.prev.value.match(/^\s*display:\s*([a-z]+)\s*$/);if(Ee)return Ee[1]}let te=!1;if(S.type==="element"&&S.namespace==="svg")if(ge(S,Ee=>Ee.fullName==="svg:foreignObject"))te=!0;else return S.name==="svg"?"inline-block":"block";switch(G.htmlWhitespaceSensitivity){case"strict":return"inline";case"ignore":return"block";default:return G.parser==="vue"&&S.parent&&S.parent.type==="root"?"block":S.type==="element"&&(!S.namespace||te||p(S))&&c[S.name]||y}}function _e(S){return S.type==="element"&&(!S.namespace||p(S))&&h[S.name]||g}function Oe(S){let G=Number.POSITIVE_INFINITY;for(let te of S.split(`
`)){if(te.length===0)continue;if(!D.has(te[0]))return 0;let Ee=k(te).length;te.length!==Ee&&Ee<G&&(G=Ee)}return G===Number.POSITIVE_INFINITY?0:G}function pe(S){let G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Oe(S);return G===0?S:S.split(`
`).map(te=>te.slice(G)).join(`
`)}function ie(S,G){let te=0;for(let Ee=0;Ee<S.length;Ee++)S[Ee]===G&&te++;return te}function ve(S){return S.replace(/&apos;/g,"'").replace(/&quot;/g,'"')}var ce=new Set(["template","style","script"]);function U(S,G){return de(S,G)&&!ce.has(S.fullName)}function de(S,G){return G.parser==="vue"&&S.type==="element"&&S.parent.type==="root"&&S.fullName.toLowerCase()!=="html"}function De(S,G){return de(S,G)&&(U(S,G)||S.attrMap.lang&&S.attrMap.lang!=="html")}function he(S){let G=S.fullName;return G.charAt(0)==="#"||G==="slot-scope"||G==="v-slot"||G.startsWith("v-slot:")}function Be(S,G){let te=S.parent;if(!de(te,G))return!1;let Ee=te.fullName,Re=S.fullName;return Ee==="script"&&Re==="setup"||Ee==="style"&&Re==="vars"}function Se(S){let G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S.value;return S.parent.isWhitespaceSensitive?S.parent.isIndentationSensitive?o(G):o(pe(A(G)),r):i(u(a,B(G)))}function ye(S,G){return de(S,G)&&S.name==="script"}n.exports={htmlTrim:T,htmlTrimPreserveIndentation:A,hasHtmlWhitespace:R,getLeadingAndTrailingHtmlWhitespace:P,canHaveInterpolation:d,countChars:ie,countParents:fe,dedentString:pe,forceBreakChildren:q,forceBreakContent:M,forceNextEmptyLine:$,getLastDescendant:se,getNodeCssStyleDisplay:Ce,getNodeCssStyleWhiteSpace:_e,hasPrettierIgnore:x,inferScriptParser:X,isVueCustomBlock:U,isVueNonHtmlBlock:De,isVueScriptTag:ye,isVueSlotAttribute:he,isVueSfcBindingsAttribute:Be,isVueSfcBlock:de,isDanglingSpaceSensitiveNode:I,isIndentationSensitiveNode:_,isLeadingSpaceSensitiveNode:b,isPreLikeNode:ne,isScriptLikeTag:l,isTextLikeNode:E,isTrailingSpaceSensitiveNode:N,isWhitespaceSensitiveNode:C,isUnknownNamespace:p,preferHardlineAsLeadingSpaces:J,preferHardlineAsTrailingSpaces:L,shouldPreserveContent:f,unescapeQuoteEntities:ve,getTextValueParts:Se}}}),qd=ee({"node_modules/angular-html-parser/lib/compiler/src/chars.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0}),e.$EOF=0,e.$BSPACE=8,e.$TAB=9,e.$LF=10,e.$VTAB=11,e.$FF=12,e.$CR=13,e.$SPACE=32,e.$BANG=33,e.$DQ=34,e.$HASH=35,e.$$=36,e.$PERCENT=37,e.$AMPERSAND=38,e.$SQ=39,e.$LPAREN=40,e.$RPAREN=41,e.$STAR=42,e.$PLUS=43,e.$COMMA=44,e.$MINUS=45,e.$PERIOD=46,e.$SLASH=47,e.$COLON=58,e.$SEMICOLON=59,e.$LT=60,e.$EQ=61,e.$GT=62,e.$QUESTION=63,e.$0=48,e.$7=55,e.$9=57,e.$A=65,e.$E=69,e.$F=70,e.$X=88,e.$Z=90,e.$LBRACKET=91,e.$BACKSLASH=92,e.$RBRACKET=93,e.$CARET=94,e.$_=95,e.$a=97,e.$b=98,e.$e=101,e.$f=102,e.$n=110,e.$r=114,e.$t=116,e.$u=117,e.$v=118,e.$x=120,e.$z=122,e.$LBRACE=123,e.$BAR=124,e.$RBRACE=125,e.$NBSP=160,e.$PIPE=124,e.$TILDA=126,e.$AT=64,e.$BT=96;function n(i){return i>=e.$TAB&&i<=e.$SPACE||i==e.$NBSP}e.isWhitespace=n;function t(i){return e.$0<=i&&i<=e.$9}e.isDigit=t;function s(i){return i>=e.$a&&i<=e.$z||i>=e.$A&&i<=e.$Z}e.isAsciiLetter=s;function a(i){return i>=e.$a&&i<=e.$f||i>=e.$A&&i<=e.$F||t(i)}e.isAsciiHexDigit=a;function r(i){return i===e.$LF||i===e.$CR}e.isNewLine=r;function u(i){return e.$0<=i&&i<=e.$7}e.isOctalDigit=u}}),Md=ee({"node_modules/angular-html-parser/lib/compiler/src/aot/static_symbol.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=class{constructor(s,a,r){this.filePath=s,this.name=a,this.members=r}assertNoMembers(){if(this.members.length)throw new Error(`Illegal state: symbol without members expected, but got ${JSON.stringify(this)}.`)}};e.StaticSymbol=n;var t=class{constructor(){this.cache=new Map}get(s,a,r){r=r||[];let u=r.length?`.${r.join(".")}`:"",i=`"${s}".${a}${u}`,o=this.cache.get(i);return o||(o=new n(s,a,r),this.cache.set(i,o)),o}};e.StaticSymbolCache=t}}),Rd=ee({"node_modules/angular-html-parser/lib/compiler/src/util.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=/-+([a-z0-9])/g;function t(l){return l.replace(n,function(){for(var d=arguments.length,C=new Array(d),_=0;_<d;_++)C[_]=arguments[_];return C[1].toUpperCase()})}e.dashCaseToCamelCase=t;function s(l,d){return r(l,":",d)}e.splitAtColon=s;function a(l,d){return r(l,".",d)}e.splitAtPeriod=a;function r(l,d,C){let _=l.indexOf(d);return _==-1?C:[l.slice(0,_).trim(),l.slice(_+1).trim()]}function u(l,d,C){return Array.isArray(l)?d.visitArray(l,C):F(l)?d.visitStringMap(l,C):l==null||typeof l=="string"||typeof l=="number"||typeof l=="boolean"?d.visitPrimitive(l,C):d.visitOther(l,C)}e.visitValue=u;function i(l){return l!=null}e.isDefined=i;function o(l){return l===void 0?null:l}e.noUndefined=o;var c=class{visitArray(l,d){return l.map(C=>u(C,this,d))}visitStringMap(l,d){let C={};return Object.keys(l).forEach(_=>{C[_]=u(l[_],this,d)}),C}visitPrimitive(l,d){return l}visitOther(l,d){return l}};e.ValueTransformer=c,e.SyncAsync={assertSync:l=>{if(P(l))throw new Error("Illegal state: value cannot be a promise");return l},then:(l,d)=>P(l)?l.then(d):d(l),all:l=>l.some(P)?Promise.all(l):l};function y(l){throw new Error(`Internal Error: ${l}`)}e.error=y;function h(l,d){let C=Error(l);return C[g]=!0,d&&(C[p]=d),C}e.syntaxError=h;var g="ngSyntaxError",p="ngParseErrors";function D(l){return l[g]}e.isSyntaxError=D;function v(l){return l[p]||[]}e.getParseErrors=v;function w(l){return l.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}e.escapeRegExp=w;var T=Object.getPrototypeOf({});function F(l){return typeof l=="object"&&l!==null&&Object.getPrototypeOf(l)===T}function A(l){let d="";for(let C=0;C<l.length;C++){let _=l.charCodeAt(C);if(_>=55296&&_<=56319&&l.length>C+1){let b=l.charCodeAt(C+1);b>=56320&&b<=57343&&(C++,_=(_-55296<<10)+b-56320+65536)}_<=127?d+=String.fromCharCode(_):_<=2047?d+=String.fromCharCode(_>>6&31|192,_&63|128):_<=65535?d+=String.fromCharCode(_>>12|224,_>>6&63|128,_&63|128):_<=2097151&&(d+=String.fromCharCode(_>>18&7|240,_>>12&63|128,_>>6&63|128,_&63|128))}return d}e.utf8Encode=A;function B(l){if(typeof l=="string")return l;if(l instanceof Array)return"["+l.map(B).join(", ")+"]";if(l==null)return""+l;if(l.overriddenName)return`${l.overriddenName}`;if(l.name)return`${l.name}`;if(!l.toString)return"object";let d=l.toString();if(d==null)return""+d;let C=d.indexOf(`
`);return C===-1?d:d.substring(0,C)}e.stringify=B;function k(l){return typeof l=="function"&&l.hasOwnProperty("__forward_ref__")?l():l}e.resolveForwardRef=k;function P(l){return!!l&&typeof l.then=="function"}e.isPromise=P;var R=class{constructor(l){this.full=l;let d=l.split(".");this.major=d[0],this.minor=d[1],this.patch=d.slice(2).join(".")}};e.Version=R;var f=typeof window<"u"&&window,x=typeof self<"u"&&typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&self,m=typeof globalThis<"u"&&globalThis,E=m||f||x;e.global=E}}),$d=ee({"node_modules/angular-html-parser/lib/compiler/src/compile_metadata.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=Md(),t=Rd(),s=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/;function a(C){return C.replace(/\W/g,"_")}e.sanitizeIdentifier=a;var r=0;function u(C){if(!C||!C.reference)return null;let _=C.reference;if(_ instanceof n.StaticSymbol)return _.name;if(_.__anonymousType)return _.__anonymousType;let b=t.stringify(_);return b.indexOf("(")>=0?(b=`anonymous_${r++}`,_.__anonymousType=b):b=a(b),b}e.identifierName=u;function i(C){let _=C.reference;return _ instanceof n.StaticSymbol?_.filePath:`./${t.stringify(_)}`}e.identifierModuleUrl=i;function o(C,_){return`View_${u({reference:C})}_${_}`}e.viewClassName=o;function c(C){return`RenderType_${u({reference:C})}`}e.rendererTypeName=c;function y(C){return`HostView_${u({reference:C})}`}e.hostViewClassName=y;function h(C){return`${u({reference:C})}NgFactory`}e.componentFactoryName=h;var g;(function(C){C[C.Pipe=0]="Pipe",C[C.Directive=1]="Directive",C[C.NgModule=2]="NgModule",C[C.Injectable=3]="Injectable"})(g=e.CompileSummaryKind||(e.CompileSummaryKind={}));function p(C){return C.value!=null?a(C.value):u(C.identifier)}e.tokenName=p;function D(C){return C.identifier!=null?C.identifier.reference:C.value}e.tokenReference=D;var v=class{constructor(){let{moduleUrl:C,styles:_,styleUrls:b}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.moduleUrl=C||null,this.styles=P(_),this.styleUrls=P(b)}};e.CompileStylesheetMetadata=v;var w=class{constructor(C){let{encapsulation:_,template:b,templateUrl:N,htmlAst:I,styles:$,styleUrls:M,externalStylesheets:q,animations:J,ngContentSelectors:L,interpolation:Y,isInline:V,preserveWhitespaces:O}=C;if(this.encapsulation=_,this.template=b,this.templateUrl=N,this.htmlAst=I,this.styles=P($),this.styleUrls=P(M),this.externalStylesheets=P(q),this.animations=J?f(J):[],this.ngContentSelectors=L||[],Y&&Y.length!=2)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=Y,this.isInline=V,this.preserveWhitespaces=O}toSummary(){return{ngContentSelectors:this.ngContentSelectors,encapsulation:this.encapsulation,styles:this.styles,animations:this.animations}}};e.CompileTemplateMetadata=w;var T=class{static create(C){let{isHost:_,type:b,isComponent:N,selector:I,exportAs:$,changeDetection:M,inputs:q,outputs:J,host:L,providers:Y,viewProviders:V,queries:O,guards:K,viewQueries:se,entryComponents:Q,template:le,componentViewType:W,rendererType:X,componentFactory:oe}=C,ae={},Ae={},z={};L!=null&&Object.keys(L).forEach(ne=>{let fe=L[ne],ge=ne.match(s);ge===null?z[ne]=fe:ge[1]!=null?Ae[ge[1]]=fe:ge[2]!=null&&(ae[ge[2]]=fe)});let H={};q!=null&&q.forEach(ne=>{let fe=t.splitAtColon(ne,[ne,ne]);H[fe[0]]=fe[1]});let Z={};return J!=null&&J.forEach(ne=>{let fe=t.splitAtColon(ne,[ne,ne]);Z[fe[0]]=fe[1]}),new T({isHost:_,type:b,isComponent:!!N,selector:I,exportAs:$,changeDetection:M,inputs:H,outputs:Z,hostListeners:ae,hostProperties:Ae,hostAttributes:z,providers:Y,viewProviders:V,queries:O,guards:K,viewQueries:se,entryComponents:Q,template:le,componentViewType:W,rendererType:X,componentFactory:oe})}constructor(C){let{isHost:_,type:b,isComponent:N,selector:I,exportAs:$,changeDetection:M,inputs:q,outputs:J,hostListeners:L,hostProperties:Y,hostAttributes:V,providers:O,viewProviders:K,queries:se,guards:Q,viewQueries:le,entryComponents:W,template:X,componentViewType:oe,rendererType:ae,componentFactory:Ae}=C;this.isHost=!!_,this.type=b,this.isComponent=N,this.selector=I,this.exportAs=$,this.changeDetection=M,this.inputs=q,this.outputs=J,this.hostListeners=L,this.hostProperties=Y,this.hostAttributes=V,this.providers=P(O),this.viewProviders=P(K),this.queries=P(se),this.guards=Q,this.viewQueries=P(le),this.entryComponents=P(W),this.template=X,this.componentViewType=oe,this.rendererType=ae,this.componentFactory=Ae}toSummary(){return{summaryKind:g.Directive,type:this.type,isComponent:this.isComponent,selector:this.selector,exportAs:this.exportAs,inputs:this.inputs,outputs:this.outputs,hostListeners:this.hostListeners,hostProperties:this.hostProperties,hostAttributes:this.hostAttributes,providers:this.providers,viewProviders:this.viewProviders,queries:this.queries,guards:this.guards,viewQueries:this.viewQueries,entryComponents:this.entryComponents,changeDetection:this.changeDetection,template:this.template&&this.template.toSummary(),componentViewType:this.componentViewType,rendererType:this.rendererType,componentFactory:this.componentFactory}}};e.CompileDirectiveMetadata=T;var F=class{constructor(C){let{type:_,name:b,pure:N}=C;this.type=_,this.name=b,this.pure=!!N}toSummary(){return{summaryKind:g.Pipe,type:this.type,name:this.name,pure:this.pure}}};e.CompilePipeMetadata=F;var A=class{};e.CompileShallowModuleMetadata=A;var B=class{constructor(C){let{type:_,providers:b,declaredDirectives:N,exportedDirectives:I,declaredPipes:$,exportedPipes:M,entryComponents:q,bootstrapComponents:J,importedModules:L,exportedModules:Y,schemas:V,transitiveModule:O,id:K}=C;this.type=_||null,this.declaredDirectives=P(N),this.exportedDirectives=P(I),this.declaredPipes=P($),this.exportedPipes=P(M),this.providers=P(b),this.entryComponents=P(q),this.bootstrapComponents=P(J),this.importedModules=P(L),this.exportedModules=P(Y),this.schemas=P(V),this.id=K||null,this.transitiveModule=O||null}toSummary(){let C=this.transitiveModule;return{summaryKind:g.NgModule,type:this.type,entryComponents:C.entryComponents,providers:C.providers,modules:C.modules,exportedDirectives:C.exportedDirectives,exportedPipes:C.exportedPipes}}};e.CompileNgModuleMetadata=B;var k=class{constructor(){this.directivesSet=new Set,this.directives=[],this.exportedDirectivesSet=new Set,this.exportedDirectives=[],this.pipesSet=new Set,this.pipes=[],this.exportedPipesSet=new Set,this.exportedPipes=[],this.modulesSet=new Set,this.modules=[],this.entryComponentsSet=new Set,this.entryComponents=[],this.providers=[]}addProvider(C,_){this.providers.push({provider:C,module:_})}addDirective(C){this.directivesSet.has(C.reference)||(this.directivesSet.add(C.reference),this.directives.push(C))}addExportedDirective(C){this.exportedDirectivesSet.has(C.reference)||(this.exportedDirectivesSet.add(C.reference),this.exportedDirectives.push(C))}addPipe(C){this.pipesSet.has(C.reference)||(this.pipesSet.add(C.reference),this.pipes.push(C))}addExportedPipe(C){this.exportedPipesSet.has(C.reference)||(this.exportedPipesSet.add(C.reference),this.exportedPipes.push(C))}addModule(C){this.modulesSet.has(C.reference)||(this.modulesSet.add(C.reference),this.modules.push(C))}addEntryComponent(C){this.entryComponentsSet.has(C.componentType)||(this.entryComponentsSet.add(C.componentType),this.entryComponents.push(C))}};e.TransitiveCompileNgModuleMetadata=k;function P(C){return C||[]}var R=class{constructor(C,_){let{useClass:b,useValue:N,useExisting:I,useFactory:$,deps:M,multi:q}=_;this.token=C,this.useClass=b||null,this.useValue=N,this.useExisting=I,this.useFactory=$||null,this.dependencies=M||null,this.multi=!!q}};e.ProviderMeta=R;function f(C){return C.reduce((_,b)=>{let N=Array.isArray(b)?f(b):b;return _.concat(N)},[])}e.flatten=f;function x(C){return C.replace(/(\w+:\/\/[\w:-]+)?(\/+)?/,"ng:///")}function m(C,_,b){let N;return b.isInline?_.type.reference instanceof n.StaticSymbol?N=`${_.type.reference.filePath}.${_.type.reference.name}.html`:N=`${u(C)}/${u(_.type)}.html`:N=b.templateUrl,_.type.reference instanceof n.StaticSymbol?N:x(N)}e.templateSourceUrl=m;function E(C,_){let b=C.moduleUrl.split(/\/\\/g),N=b[b.length-1];return x(`css/${_}${N}.ngstyle.js`)}e.sharedStylesheetJitUrl=E;function l(C){return x(`${u(C.type)}/module.ngfactory.js`)}e.ngModuleJitUrl=l;function d(C,_){return x(`${u(C)}/${u(_.type)}.ngfactory.js`)}e.templateJitUrl=d}}),Vd=ee({"node_modules/angular-html-parser/lib/compiler/src/parse_util.js"(e){"use strict";re(),Object.defineProperty(e,"__esModule",{value:!0});var n=qd(),t=$d(),s=class{constructor(y,h,g,p){this.file=y,this.offset=h,this.line=g,this.col=p}toString(){return this.offset!=null?`${this.file.url}@${this.line}:${this.col}`:this.file.url}moveBy(y){let h=this.file.content,g=h.length,p=this.offset,D=this.line,v=this.col;for(;p>0&&y<0;)if(p--,y++,h.charCodeAt(p)==n.$LF){D--;let T=h.substr(0,p-1).lastIndexOf(String.fromCharCode(n.$LF));v=T>0?p-T:p}else v--;for(;p<g&&y>0;){let w=h.charCodeAt(p);p++,y--,w==n.$LF?(D++,v=0):v++}return new s(this.file,p,D,v)}getContext(y,h){let g=this.file.content,p=this.offset;if(p!=null){p>g.length-1&&(p=g.length-1);let D=p,v=0,w=0;for(;v<y&&p>0&&(p--,v++,!(g[p]==`
`&&++w==h)););for(v=0,w=0;v<y&&D<g.length-1&&(D++,v++,!(g[D]==`
`&&++w==h)););return{before:g.substring(p,this.offset),after:g.substring(this.offset,D+1)}}return null}};e.ParseLocation=s;var a=class{constructor(y,h){this.content=y,this.url=h}};e.ParseSourceFile=a;var r=class{constructor(y,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;this.start=y,this.end=h,this.details=g}toString(){return this.start.file.content.substring(this.start.offset,this.end.offset)}};e.ParseSourceSpan=r,e.EMPTY_PARSE_LOCATION=new s(new a("",""),0,0,0),e.EMPTY_SOURCE_SPAN=new r(e.EMPTY_PARSE_LOCATION,e.EMPTY_PARSE_LOCATION);var u;(function(y){y[y.WARNING=0]="WARNING",y[y.ERROR=1]="ERROR"})(u=e.ParseErrorLevel||(e.ParseErrorLevel={}));var i=class{constructor(y,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:u.ERROR;this.span=y,this.msg=h,this.level=g}contextualMessage(){let y=this.span.start.getContext(100,3);return y?`${this.msg} ("${y.before}[${u[this.level]} ->]${y.after}")`:this.msg}toString(){let y=this.span.details?`, ${this.span.details}`:"";return`${this.contextualMessage()}: ${this.span.start}${y}`}};e.ParseError=i;function o(y,h){let g=t.identifierModuleUrl(h),p=g!=null?`in ${y} ${t.identifierName(h)} in ${g}`:`in ${y} ${t.identifierName(h)}`,D=new a("",p);return new r(new s(D,-1,-1,-1),new s(D,-1,-1,-1))}e.typeSourceSpan=o;function c(y,h,g){let p=`in ${y} ${h} in ${g}`,D=new a("",p);return new r(new s(D,-1,-1,-1),new s(D,-1,-1,-1))}e.r3JitTypeSourceSpan=c}}),Wd=ee({"src/language-html/print-preprocess.js"(e,n){"use strict";re();var{ParseSourceSpan:t}=Vd(),{htmlTrim:s,getLeadingAndTrailingHtmlWhitespace:a,hasHtmlWhitespace:r,canHaveInterpolation:u,getNodeCssStyleDisplay:i,isDanglingSpaceSensitiveNode:o,isIndentationSensitiveNode:c,isLeadingSpaceSensitiveNode:y,isTrailingSpaceSensitiveNode:h,isWhitespaceSensitiveNode:g,isVueScriptTag:p}=Ot(),D=[w,T,A,k,P,x,R,f,m,B,E];function v(l,d){for(let C of D)C(l,d);return l}function w(l){l.walk(d=>{if(d.type==="element"&&d.tagDefinition.ignoreFirstLf&&d.children.length>0&&d.children[0].type==="text"&&d.children[0].value[0]===`
`){let C=d.children[0];C.value.length===1?d.removeChild(C):C.value=C.value.slice(1)}})}function T(l){let d=C=>C.type==="element"&&C.prev&&C.prev.type==="ieConditionalStartComment"&&C.prev.sourceSpan.end.offset===C.startSourceSpan.start.offset&&C.firstChild&&C.firstChild.type==="ieConditionalEndComment"&&C.firstChild.sourceSpan.start.offset===C.startSourceSpan.end.offset;l.walk(C=>{if(C.children)for(let _=0;_<C.children.length;_++){let b=C.children[_];if(!d(b))continue;let N=b.prev,I=b.firstChild;C.removeChild(N),_--;let $=new t(N.sourceSpan.start,I.sourceSpan.end),M=new t($.start,b.sourceSpan.end);b.condition=N.condition,b.sourceSpan=M,b.startSourceSpan=$,b.removeChild(I)}})}function F(l,d,C){l.walk(_=>{if(_.children)for(let b=0;b<_.children.length;b++){let N=_.children[b];if(N.type!=="text"&&!d(N))continue;N.type!=="text"&&(N.type="text",N.value=C(N));let I=N.prev;!I||I.type!=="text"||(I.value+=N.value,I.sourceSpan=new t(I.sourceSpan.start,N.sourceSpan.end),_.removeChild(N),b--)}})}function A(l){return F(l,d=>d.type==="cdata",d=>`<![CDATA[${d.value}]]>`)}function B(l){let d=C=>C.type==="element"&&C.attrs.length===0&&C.children.length===1&&C.firstChild.type==="text"&&!r(C.children[0].value)&&!C.firstChild.hasLeadingSpaces&&!C.firstChild.hasTrailingSpaces&&C.isLeadingSpaceSensitive&&!C.hasLeadingSpaces&&C.isTrailingSpaceSensitive&&!C.hasTrailingSpaces&&C.prev&&C.prev.type==="text"&&C.next&&C.next.type==="text";l.walk(C=>{if(C.children)for(let _=0;_<C.children.length;_++){let b=C.children[_];if(!d(b))continue;let N=b.prev,I=b.next;N.value+=`<${b.rawName}>`+b.firstChild.value+`</${b.rawName}>`+I.value,N.sourceSpan=new t(N.sourceSpan.start,I.sourceSpan.end),N.isTrailingSpaceSensitive=I.isTrailingSpaceSensitive,N.hasTrailingSpaces=I.hasTrailingSpaces,C.removeChild(b),_--,C.removeChild(I)}})}function k(l,d){if(d.parser==="html")return;let C=/{{(.+?)}}/s;l.walk(_=>{if(!!u(_))for(let b of _.children){if(b.type!=="text")continue;let N=b.sourceSpan.start,I=null,$=b.value.split(C);for(let M=0;M<$.length;M++,N=I){let q=$[M];if(M%2===0){I=N.moveBy(q.length),q.length>0&&_.insertChildBefore(b,{type:"text",value:q,sourceSpan:new t(N,I)});continue}I=N.moveBy(q.length+4),_.insertChildBefore(b,{type:"interpolation",sourceSpan:new t(N,I),children:q.length===0?[]:[{type:"text",value:q,sourceSpan:new t(N.moveBy(2),I.moveBy(-2))}]})}_.removeChild(b)}})}function P(l){l.walk(d=>{if(!d.children)return;if(d.children.length===0||d.children.length===1&&d.children[0].type==="text"&&s(d.children[0].value).length===0){d.hasDanglingSpaces=d.children.length>0,d.children=[];return}let C=g(d),_=c(d);if(!C)for(let b=0;b<d.children.length;b++){let N=d.children[b];if(N.type!=="text")continue;let{leadingWhitespace:I,text:$,trailingWhitespace:M}=a(N.value),q=N.prev,J=N.next;$?(N.value=$,N.sourceSpan=new t(N.sourceSpan.start.moveBy(I.length),N.sourceSpan.end.moveBy(-M.length)),I&&(q&&(q.hasTrailingSpaces=!0),N.hasLeadingSpaces=!0),M&&(N.hasTrailingSpaces=!0,J&&(J.hasLeadingSpaces=!0))):(d.removeChild(N),b--,(I||M)&&(q&&(q.hasTrailingSpaces=!0),J&&(J.hasLeadingSpaces=!0)))}d.isWhitespaceSensitive=C,d.isIndentationSensitive=_})}function R(l){l.walk(d=>{d.isSelfClosing=!d.children||d.type==="element"&&(d.tagDefinition.isVoid||d.startSourceSpan===d.endSourceSpan)})}function f(l,d){l.walk(C=>{C.type==="element"&&(C.hasHtmComponentClosingTag=C.endSourceSpan&&/^<\s*\/\s*\/\s*>$/.test(d.originalText.slice(C.endSourceSpan.start.offset,C.endSourceSpan.end.offset)))})}function x(l,d){l.walk(C=>{C.cssDisplay=i(C,d)})}function m(l,d){l.walk(C=>{let{children:_}=C;if(!!_){if(_.length===0){C.isDanglingSpaceSensitive=o(C);return}for(let b of _)b.isLeadingSpaceSensitive=y(b,d),b.isTrailingSpaceSensitive=h(b,d);for(let b=0;b<_.length;b++){let N=_[b];N.isLeadingSpaceSensitive=(b===0||N.prev.isTrailingSpaceSensitive)&&N.isLeadingSpaceSensitive,N.isTrailingSpaceSensitive=(b===_.length-1||N.next.isLeadingSpaceSensitive)&&N.isTrailingSpaceSensitive}}})}function E(l,d){if(d.parser==="vue"){let C=l.children.find(b=>p(b,d));if(!C)return;let{lang:_}=C.attrMap;(_==="ts"||_==="typescript")&&(d.__should_parse_vue_template_with_ts=!0)}}n.exports=v}}),Hd=ee({"src/language-html/pragma.js"(e,n){"use strict";re();function t(a){return/^\s*<!--\s*@(?:format|prettier)\s*-->/.test(a)}function s(a){return`<!-- @format -->

`+a.replace(/^\s*\n/,"")}n.exports={hasPragma:t,insertPragma:s}}}),Xn=ee({"src/language-html/loc.js"(e,n){"use strict";re();function t(a){return a.sourceSpan.start.offset}function s(a){return a.sourceSpan.end.offset}n.exports={locStart:t,locEnd:s}}}),er=ee({"src/language-html/print/tag.js"(e,n){"use strict";re();var t=Xt(),{isNonEmptyArray:s}=Ge(),{builders:{indent:a,join:r,line:u,softline:i,hardline:o},utils:{replaceTextEndOfLine:c}}=qe(),{locStart:y,locEnd:h}=Xn(),{isTextLikeNode:g,getLastDescendant:p,isPreLikeNode:D,hasPrettierIgnore:v,shouldPreserveContent:w,isVueSfcBlock:T}=Ot();function F(L,Y){return[L.isSelfClosing?"":A(L,Y),B(L,Y)]}function A(L,Y){return L.lastChild&&l(L.lastChild)?"":[k(L,Y),R(L,Y)]}function B(L,Y){return(L.next?m(L.next):E(L.parent))?"":[f(L,Y),P(L,Y)]}function k(L,Y){return E(L)?f(L.lastChild,Y):""}function P(L,Y){return l(L)?R(L.parent,Y):d(L)?q(L.next):""}function R(L,Y){if(t(!L.isSelfClosing),x(L,Y))return"";switch(L.type){case"ieConditionalComment":return"<!";case"element":if(L.hasHtmComponentClosingTag)return"<//";default:return`</${L.rawName}`}}function f(L,Y){if(x(L,Y))return"";switch(L.type){case"ieConditionalComment":case"ieConditionalEndComment":return"[endif]-->";case"ieConditionalStartComment":return"]><!-->";case"interpolation":return"}}";case"element":if(L.isSelfClosing)return"/>";default:return">"}}function x(L,Y){return!L.isSelfClosing&&!L.endSourceSpan&&(v(L)||w(L.parent,Y))}function m(L){return L.prev&&L.prev.type!=="docType"&&!g(L.prev)&&L.isLeadingSpaceSensitive&&!L.hasLeadingSpaces}function E(L){return L.lastChild&&L.lastChild.isTrailingSpaceSensitive&&!L.lastChild.hasTrailingSpaces&&!g(p(L.lastChild))&&!D(L)}function l(L){return!L.next&&!L.hasTrailingSpaces&&L.isTrailingSpaceSensitive&&g(p(L))}function d(L){return L.next&&!g(L.next)&&g(L)&&L.isTrailingSpaceSensitive&&!L.hasTrailingSpaces}function C(L){let Y=L.trim().match(/^prettier-ignore-attribute(?:\s+(.+))?$/s);return Y?Y[1]?Y[1].split(/\s+/):!0:!1}function _(L){return!L.prev&&L.isLeadingSpaceSensitive&&!L.hasLeadingSpaces}function b(L,Y,V){let O=L.getValue();if(!s(O.attrs))return O.isSelfClosing?" ":"";let K=O.prev&&O.prev.type==="comment"&&C(O.prev.value),se=typeof K=="boolean"?()=>K:Array.isArray(K)?ae=>K.includes(ae.rawName):()=>!1,Q=L.map(ae=>{let Ae=ae.getValue();return se(Ae)?c(Y.originalText.slice(y(Ae),h(Ae))):V()},"attrs"),le=O.type==="element"&&O.fullName==="script"&&O.attrs.length===1&&O.attrs[0].fullName==="src"&&O.children.length===0,X=Y.singleAttributePerLine&&O.attrs.length>1&&!T(O,Y)?o:u,oe=[a([le?" ":u,r(X,Q)])];return O.firstChild&&_(O.firstChild)||O.isSelfClosing&&E(O.parent)||le?oe.push(O.isSelfClosing?" ":""):oe.push(Y.bracketSameLine?O.isSelfClosing?" ":"":O.isSelfClosing?u:i),oe}function N(L){return L.firstChild&&_(L.firstChild)?"":J(L)}function I(L,Y,V){let O=L.getValue();return[$(O,Y),b(L,Y,V),O.isSelfClosing?"":N(O)]}function $(L,Y){return L.prev&&d(L.prev)?"":[M(L,Y),q(L)]}function M(L,Y){return _(L)?J(L.parent):m(L)?f(L.prev,Y):""}function q(L){switch(L.type){case"ieConditionalComment":case"ieConditionalStartComment":return`<!--[if ${L.condition}`;case"ieConditionalEndComment":return"<!--<!";case"interpolation":return"{{";case"docType":return"<!DOCTYPE";case"element":if(L.condition)return`<!--[if ${L.condition}]><!--><${L.rawName}`;default:return`<${L.rawName}`}}function J(L){switch(t(!L.isSelfClosing),L.type){case"ieConditionalComment":return"]>";case"element":if(L.condition)return"><!--<![endif]-->";default:return">"}}n.exports={printClosingTag:F,printClosingTagStart:A,printClosingTagStartMarker:R,printClosingTagEndMarker:f,printClosingTagSuffix:P,printClosingTagEnd:B,needsToBorrowLastChildClosingTagEndMarker:E,needsToBorrowParentClosingTagStartMarker:l,needsToBorrowPrevClosingTagEndMarker:m,printOpeningTag:I,printOpeningTagStart:$,printOpeningTagPrefix:M,printOpeningTagStartMarker:q,printOpeningTagEndMarker:J,needsToBorrowNextOpeningTagStartMarker:d,needsToBorrowParentOpeningTagEndMarker:_}}}),Gd=ee({"node_modules/parse-srcset/src/parse-srcset.js"(e,n){re(),function(t,s){typeof define=="function"&&define.amd?define([],s):typeof n=="object"&&n.exports?n.exports=s():t.parseSrcset=s()}(e,function(){return function(t,s){var a=s&&s.logger||console;function r(R){return R===" "||R==="	"||R===`
`||R==="\f"||R==="\r"}function u(R){var f,x=R.exec(t.substring(A));if(x)return f=x[0],A+=f.length,f}for(var i=t.length,o=/^[ \t\n\r\u000c]+/,c=/^[, \t\n\r\u000c]+/,y=/^[^ \t\n\r\u000c]+/,h=/[,]+$/,g=/^\d+$/,p=/^-?(?:[0-9]+|[0-9]*\.[0-9]+)(?:[eE][+-]?[0-9]+)?$/,D,v,w,T,F,A=0,B=[];;){if(u(c),A>=i)return B;D=u(y),v=[],D.slice(-1)===","?(D=D.replace(h,""),P()):k()}function k(){for(u(o),w="",T="in descriptor";;){if(F=t.charAt(A),T==="in descriptor")if(r(F))w&&(v.push(w),w="",T="after descriptor");else if(F===","){A+=1,w&&v.push(w),P();return}else if(F==="(")w=w+F,T="in parens";else if(F===""){w&&v.push(w),P();return}else w=w+F;else if(T==="in parens")if(F===")")w=w+F,T="in descriptor";else if(F===""){v.push(w),P();return}else w=w+F;else if(T==="after descriptor"&&!r(F))if(F===""){P();return}else T="in descriptor",A-=1;A+=1}}function P(){var R=!1,f,x,m,E,l={},d,C,_,b,N;for(E=0;E<v.length;E++)d=v[E],C=d[d.length-1],_=d.substring(0,d.length-1),b=parseInt(_,10),N=parseFloat(_),g.test(_)&&C==="w"?((f||x)&&(R=!0),b===0?R=!0:f=b):p.test(_)&&C==="x"?((f||x||m)&&(R=!0),N<0?R=!0:x=N):g.test(_)&&C==="h"?((m||x)&&(R=!0),b===0?R=!0:m=b):R=!0;R?a&&a.error&&a.error("Invalid srcset descriptor found in '"+t+"' at '"+d+"'."):(l.url=D,f&&(l.w=f),x&&(l.d=x),m&&(l.h=m),B.push(l))}}})}}),Ud=ee({"src/language-html/syntax-attribute.js"(e,n){"use strict";re();var t=Gd(),{builders:{ifBreak:s,join:a,line:r}}=qe();function u(o){let c=t(o,{logger:{error(k){throw new Error(k)}}}),y=c.some(k=>{let{w:P}=k;return P}),h=c.some(k=>{let{h:P}=k;return P}),g=c.some(k=>{let{d:P}=k;return P});if(y+h+g>1)throw new Error("Mixed descriptor in srcset is not supported");let p=y?"w":h?"h":"d",D=y?"w":h?"h":"x",v=k=>Math.max(...k),w=c.map(k=>k.url),T=v(w.map(k=>k.length)),F=c.map(k=>k[p]).map(k=>k?k.toString():""),A=F.map(k=>{let P=k.indexOf(".");return P===-1?k.length:P}),B=v(A);return a([",",r],w.map((k,P)=>{let R=[k],f=F[P];if(f){let x=T-k.length+1,m=B-A[P],E=" ".repeat(x+m);R.push(s(E," "),f+D)}return R}))}function i(o){return o.trim().split(/\s+/).join(" ")}n.exports={printImgSrcset:u,printClassNames:i}}}),Jd=ee({"src/language-html/syntax-vue.js"(e,n){"use strict";re();var{builders:{group:t}}=qe();function s(i,o){let{left:c,operator:y,right:h}=a(i);return[t(o(`function _(${c}) {}`,{parser:"babel",__isVueForBindingLeft:!0}))," ",y," ",o(h,{parser:"__js_expression"},{stripTrailingHardline:!0})]}function a(i){let o=/(.*?)\s+(in|of)\s+(.*)/s,c=/,([^,\]}]*)(?:,([^,\]}]*))?$/,y=/^\(|\)$/g,h=i.match(o);if(!h)return;let g={};if(g.for=h[3].trim(),!g.for)return;let p=h[1].trim().replace(y,""),D=p.match(c);D?(g.alias=p.replace(c,""),g.iterator1=D[1].trim(),D[2]&&(g.iterator2=D[2].trim())):g.alias=p;let v=[g.alias,g.iterator1,g.iterator2];if(!v.some((w,T)=>!w&&(T===0||v.slice(T+1).some(Boolean))))return{left:v.filter(Boolean).join(","),operator:h[2],right:g.for}}function r(i,o){return o(`function _(${i}) {}`,{parser:"babel",__isVueBindings:!0})}function u(i){let o=/^(?:[\w$]+|\([^)]*\))\s*=>|^function\s*\(/,c=/^[$A-Z_a-z][\w$]*(?:\.[$A-Z_a-z][\w$]*|\['[^']*']|\["[^"]*"]|\[\d+]|\[[$A-Z_a-z][\w$]*])*$/,y=i.trim();return o.test(y)||c.test(y)}n.exports={isVueEventBindingExpression:u,printVueFor:s,printVueBindings:r}}}),so=ee({"src/language-html/get-node-content.js"(e,n){"use strict";re();var{needsToBorrowParentClosingTagStartMarker:t,printClosingTagStartMarker:s,needsToBorrowLastChildClosingTagEndMarker:a,printClosingTagEndMarker:r,needsToBorrowParentOpeningTagEndMarker:u,printOpeningTagEndMarker:i}=er();function o(c,y){let h=c.startSourceSpan.end.offset;c.firstChild&&u(c.firstChild)&&(h-=i(c).length);let g=c.endSourceSpan.start.offset;return c.lastChild&&t(c.lastChild)?g+=s(c,y).length:a(c)&&(g-=r(c.lastChild,y).length),y.originalText.slice(h,g)}n.exports=o}}),zd=ee({"src/language-html/embed.js"(e,n){"use strict";re();var{builders:{breakParent:t,group:s,hardline:a,indent:r,line:u,fill:i,softline:o},utils:{mapDoc:c,replaceTextEndOfLine:y}}=qe(),h=Jn(),{printClosingTag:g,printClosingTagSuffix:p,needsToBorrowPrevClosingTagEndMarker:D,printOpeningTagPrefix:v,printOpeningTag:w}=er(),{printImgSrcset:T,printClassNames:F}=Ud(),{printVueFor:A,printVueBindings:B,isVueEventBindingExpression:k}=Jd(),{isScriptLikeTag:P,isVueNonHtmlBlock:R,inferScriptParser:f,htmlTrimPreserveIndentation:x,dedentString:m,unescapeQuoteEntities:E,isVueSlotAttribute:l,isVueSfcBindingsAttribute:d,getTextValueParts:C}=Ot(),_=so();function b(I,$,M){let q=Q=>new RegExp(Q.join("|")).test(I.fullName),J=()=>E(I.value),L=!1,Y=(Q,le)=>{let W=Q.type==="NGRoot"?Q.node.type==="NGMicrosyntax"&&Q.node.body.length===1&&Q.node.body[0].type==="NGMicrosyntaxExpression"?Q.node.body[0].expression:Q.node:Q.type==="JsExpressionRoot"?Q.node:Q;W&&(W.type==="ObjectExpression"||W.type==="ArrayExpression"||le.parser==="__vue_expression"&&(W.type==="TemplateLiteral"||W.type==="StringLiteral"))&&(L=!0)},V=Q=>s(Q),O=function(Q){let le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return s([r([o,Q]),le?o:""])},K=Q=>L?V(Q):O(Q),se=(Q,le)=>$(Q,Object.assign({__onHtmlBindingRoot:Y,__embeddedInHtml:!0},le));if(I.fullName==="srcset"&&(I.parent.fullName==="img"||I.parent.fullName==="source"))return O(T(J()));if(I.fullName==="class"&&!M.parentParser){let Q=J();if(!Q.includes("{{"))return F(Q)}if(I.fullName==="style"&&!M.parentParser){let Q=J();if(!Q.includes("{{"))return O(se(Q,{parser:"css",__isHTMLStyleAttribute:!0}))}if(M.parser==="vue"){if(I.fullName==="v-for")return A(J(),se);if(l(I)||d(I,M))return B(J(),se);let Q=["^@","^v-on:"],le=["^:","^v-bind:"],W=["^v-"];if(q(Q)){let X=J(),oe=k(X)?"__js_expression":M.__should_parse_vue_template_with_ts?"__vue_ts_event_binding":"__vue_event_binding";return K(se(X,{parser:oe}))}if(q(le))return K(se(J(),{parser:"__vue_expression"}));if(q(W))return K(se(J(),{parser:"__js_expression"}))}if(M.parser==="angular"){let Q=(z,H)=>se(z,Object.assign(Object.assign({},H),{},{trailingComma:"none"})),le=["^\\*"],W=["^\\(.+\\)$","^on-"],X=["^\\[.+\\]$","^bind(on)?-","^ng-(if|show|hide|class|style)$"],oe=["^i18n(-.+)?$"];if(q(W))return K(Q(J(),{parser:"__ng_action"}));if(q(X))return K(Q(J(),{parser:"__ng_binding"}));if(q(oe)){let z=J().trim();return O(i(C(I,z)),!z.includes("@@"))}if(q(le))return K(Q(J(),{parser:"__ng_directive"}));let ae=/{{(.+?)}}/s,Ae=J();if(ae.test(Ae)){let z=[];for(let[H,Z]of Ae.split(ae).entries())if(H%2===0)z.push(y(Z));else try{z.push(s(["{{",r([u,Q(Z,{parser:"__ng_interpolation",__isInHtmlInterpolation:!0})]),u,"}}"]))}catch{z.push("{{",y(Z),"}}")}return s(z)}}return null}function N(I,$,M,q){let J=I.getValue();switch(J.type){case"element":{if(P(J)||J.type==="interpolation")return;if(!J.isSelfClosing&&R(J,q)){let L=f(J,q);if(!L)return;let Y=_(J,q),V=/^\s*$/.test(Y),O="";return V||(O=M(x(Y),{parser:L,__embeddedInHtml:!0},{stripTrailingHardline:!0}),V=O===""),[v(J,q),s(w(I,q,$)),V?"":a,O,V?"":a,g(J,q),p(J,q)]}break}case"text":{if(P(J.parent)){let L=f(J.parent,q);if(L){let Y=L==="markdown"?m(J.value.replace(/^[^\S\n]*\n/,"")):J.value,V={parser:L,__embeddedInHtml:!0};if(q.parser==="html"&&L==="babel"){let O="script",{attrMap:K}=J.parent;K&&(K.type==="module"||K.type==="text/babel"&&K["data-type"]==="module")&&(O="module"),V.__babelSourceType=O}return[t,v(J,q),M(Y,V,{stripTrailingHardline:!0}),p(J,q)]}}else if(J.parent.type==="interpolation"){let L={__isInHtmlInterpolation:!0,__embeddedInHtml:!0};return q.parser==="angular"?(L.parser="__ng_interpolation",L.trailingComma="none"):q.parser==="vue"?L.parser=q.__should_parse_vue_template_with_ts?"__vue_ts_expression":"__vue_expression":L.parser="__js_expression",[r([u,M(J.value,L,{stripTrailingHardline:!0})]),J.parent.next&&D(J.parent.next)?" ":u]}break}case"attribute":{if(!J.value)break;if(/^PRETTIER_HTML_PLACEHOLDER_\d+_\d+_IN_JS$/.test(q.originalText.slice(J.valueSpan.start.offset,J.valueSpan.end.offset)))return[J.rawName,"=",J.value];if(q.parser==="lwc"&&/^{.*}$/s.test(q.originalText.slice(J.valueSpan.start.offset,J.valueSpan.end.offset)))return[J.rawName,"=",J.value];let L=b(J,(Y,V)=>M(Y,Object.assign({__isInHtmlAttribute:!0,__embeddedInHtml:!0},V),{stripTrailingHardline:!0}),q);if(L)return[J.rawName,'="',s(c(L,Y=>typeof Y=="string"?Y.replace(/"/g,"&quot;"):Y)),'"'];break}case"front-matter":return h(J,M)}}n.exports=N}}),io=ee({"src/language-html/print/children.js"(e,n){"use strict";re();var{builders:{breakParent:t,group:s,ifBreak:a,line:r,softline:u,hardline:i},utils:{replaceTextEndOfLine:o}}=qe(),{locStart:c,locEnd:y}=Xn(),{forceBreakChildren:h,forceNextEmptyLine:g,isTextLikeNode:p,hasPrettierIgnore:D,preferHardlineAsLeadingSpaces:v}=Ot(),{printOpeningTagPrefix:w,needsToBorrowNextOpeningTagStartMarker:T,printOpeningTagStartMarker:F,needsToBorrowPrevClosingTagEndMarker:A,printClosingTagEndMarker:B,printClosingTagSuffix:k,needsToBorrowParentClosingTagStartMarker:P}=er();function R(m,E,l){let d=m.getValue();return D(d)?[w(d,E),...o(E.originalText.slice(c(d)+(d.prev&&T(d.prev)?F(d).length:0),y(d)-(d.next&&A(d.next)?B(d,E).length:0))),k(d,E)]:l()}function f(m,E){return p(m)&&p(E)?m.isTrailingSpaceSensitive?m.hasTrailingSpaces?v(E)?i:r:"":v(E)?i:u:T(m)&&(D(E)||E.firstChild||E.isSelfClosing||E.type==="element"&&E.attrs.length>0)||m.type==="element"&&m.isSelfClosing&&A(E)?"":!E.isLeadingSpaceSensitive||v(E)||A(E)&&m.lastChild&&P(m.lastChild)&&m.lastChild.lastChild&&P(m.lastChild.lastChild)?i:E.hasLeadingSpaces?r:u}function x(m,E,l){let d=m.getValue();if(h(d))return[t,...m.map(_=>{let b=_.getValue(),N=b.prev?f(b.prev,b):"";return[N?[N,g(b.prev)?i:""]:"",R(_,E,l)]},"children")];let C=d.children.map(()=>Symbol(""));return m.map((_,b)=>{let N=_.getValue();if(p(N)){if(N.prev&&p(N.prev)){let Y=f(N.prev,N);if(Y)return g(N.prev)?[i,i,R(_,E,l)]:[Y,R(_,E,l)]}return R(_,E,l)}let I=[],$=[],M=[],q=[],J=N.prev?f(N.prev,N):"",L=N.next?f(N,N.next):"";return J&&(g(N.prev)?I.push(i,i):J===i?I.push(i):p(N.prev)?$.push(J):$.push(a("",u,{groupId:C[b-1]}))),L&&(g(N)?p(N.next)&&q.push(i,i):L===i?p(N.next)&&q.push(i):M.push(L)),[...I,s([...$,s([R(_,E,l),...M],{id:C[b]})]),...q]},"children")}n.exports={printChildren:x}}}),Xd=ee({"src/language-html/print/element.js"(e,n){"use strict";re();var{builders:{breakParent:t,dedentToRoot:s,group:a,ifBreak:r,indentIfBreak:u,indent:i,line:o,softline:c},utils:{replaceTextEndOfLine:y}}=qe(),h=so(),{shouldPreserveContent:g,isScriptLikeTag:p,isVueCustomBlock:D,countParents:v,forceBreakContent:w}=Ot(),{printOpeningTagPrefix:T,printOpeningTag:F,printClosingTagSuffix:A,printClosingTag:B,needsToBorrowPrevClosingTagEndMarker:k,needsToBorrowLastChildClosingTagEndMarker:P}=er(),{printChildren:R}=io();function f(x,m,E){let l=x.getValue();if(g(l,m))return[T(l,m),a(F(x,m,E)),...y(h(l,m)),...B(l,m),A(l,m)];let d=l.children.length===1&&l.firstChild.type==="interpolation"&&l.firstChild.isLeadingSpaceSensitive&&!l.firstChild.hasLeadingSpaces&&l.lastChild.isTrailingSpaceSensitive&&!l.lastChild.hasTrailingSpaces,C=Symbol("element-attr-group-id"),_=$=>a([a(F(x,m,E),{id:C}),$,B(l,m)]),b=$=>d?u($,{groupId:C}):(p(l)||D(l,m))&&l.parent.type==="root"&&m.parser==="vue"&&!m.vueIndentScriptAndStyle?$:i($),N=()=>d?r(c,"",{groupId:C}):l.firstChild.hasLeadingSpaces&&l.firstChild.isLeadingSpaceSensitive?o:l.firstChild.type==="text"&&l.isWhitespaceSensitive&&l.isIndentationSensitive?s(c):c,I=()=>(l.next?k(l.next):P(l.parent))?l.lastChild.hasTrailingSpaces&&l.lastChild.isTrailingSpaceSensitive?" ":"":d?r(c,"",{groupId:C}):l.lastChild.hasTrailingSpaces&&l.lastChild.isTrailingSpaceSensitive?o:(l.lastChild.type==="comment"||l.lastChild.type==="text"&&l.isWhitespaceSensitive&&l.isIndentationSensitive)&&new RegExp(`\\n[\\t ]{${m.tabWidth*v(x,M=>M.parent&&M.parent.type!=="root")}}$`).test(l.lastChild.value)?"":c;return l.children.length===0?_(l.hasDanglingSpaces&&l.isDanglingSpaceSensitive?o:""):_([w(l)?t:"",b([N(),R(x,m,E)]),I()])}n.exports={printElement:f}}}),Kd=ee({"src/language-html/printer-html.js"(e,n){"use strict";re();var{builders:{fill:t,group:s,hardline:a,literalline:r},utils:{cleanDoc:u,getDocParts:i,isConcat:o,replaceTextEndOfLine:c}}=qe(),y=Ld(),{countChars:h,unescapeQuoteEntities:g,getTextValueParts:p}=Ot(),D=Wd(),{insertPragma:v}=Hd(),{locStart:w,locEnd:T}=Xn(),F=zd(),{printClosingTagSuffix:A,printClosingTagEnd:B,printOpeningTagPrefix:k,printOpeningTagStart:P}=er(),{printElement:R}=Xd(),{printChildren:f}=io();function x(m,E,l){let d=m.getValue();switch(d.type){case"front-matter":return c(d.raw);case"root":return E.__onHtmlRoot&&E.__onHtmlRoot(d),[s(f(m,E,l)),a];case"element":case"ieConditionalComment":return R(m,E,l);case"ieConditionalStartComment":case"ieConditionalEndComment":return[P(d),B(d)];case"interpolation":return[P(d,E),...m.map(l,"children"),B(d,E)];case"text":{if(d.parent.type==="interpolation"){let _=/\n[^\S\n]*$/,b=_.test(d.value),N=b?d.value.replace(_,""):d.value;return[...c(N),b?a:""]}let C=u([k(d,E),...p(d),A(d,E)]);return o(C)||C.type==="fill"?t(i(C)):C}case"docType":return[s([P(d,E)," ",d.value.replace(/^html\b/i,"html").replace(/\s+/g," ")]),B(d,E)];case"comment":return[k(d,E),...c(E.originalText.slice(w(d),T(d)),r),A(d,E)];case"attribute":{if(d.value===null)return d.rawName;let C=g(d.value),_=h(C,"'"),b=h(C,'"'),N=_<b?"'":'"';return[d.rawName,"=",N,...c(N==='"'?C.replace(/"/g,"&quot;"):C.replace(/'/g,"&apos;")),N]}default:throw new Error(`Unexpected node type ${d.type}`)}}n.exports={preprocess:D,print:x,insertPragma:v,massageAstNode:y,embed:F}}}),Yd=ee({"src/language-html/options.js"(e,n){"use strict";re();var t=jt(),s="HTML";n.exports={bracketSameLine:t.bracketSameLine,htmlWhitespaceSensitivity:{since:"1.15.0",category:s,type:"choice",default:"css",description:"How to handle whitespaces in HTML.",choices:[{value:"css",description:"Respect the default value of CSS display property."},{value:"strict",description:"Whitespaces are considered sensitive."},{value:"ignore",description:"Whitespaces are considered insensitive."}]},singleAttributePerLine:t.singleAttributePerLine,vueIndentScriptAndStyle:{since:"1.19.0",category:s,type:"boolean",default:!1,description:"Indent script and style tags in Vue files."}}}}),Qd=ee({"src/language-html/parsers.js"(){re()}}),xn=ee({"node_modules/linguist-languages/data/HTML.json"(e,n){n.exports={name:"HTML",type:"markup",tmScope:"text.html.basic",aceMode:"html",codemirrorMode:"htmlmixed",codemirrorMimeType:"text/html",color:"#e34c26",aliases:["xhtml"],extensions:[".html",".hta",".htm",".html.hl",".inc",".xht",".xhtml"],languageId:146}}}),Zd=ee({"node_modules/linguist-languages/data/Vue.json"(e,n){n.exports={name:"Vue",type:"markup",color:"#41b883",extensions:[".vue"],tmScope:"text.html.vue",aceMode:"html",languageId:391}}}),eg=ee({"src/language-html/index.js"(e,n){"use strict";re();var t=Nt(),s=Kd(),a=Yd(),r=Qd(),u=[t(xn(),()=>({name:"Angular",since:"1.15.0",parsers:["angular"],vscodeLanguageIds:["html"],extensions:[".component.html"],filenames:[]})),t(xn(),o=>({since:"1.15.0",parsers:["html"],vscodeLanguageIds:["html"],extensions:[...o.extensions,".mjml"]})),t(xn(),()=>({name:"Lightning Web Components",since:"1.17.0",parsers:["lwc"],vscodeLanguageIds:["html"],extensions:[],filenames:[]})),t(Zd(),()=>({since:"1.10.0",parsers:["vue"],vscodeLanguageIds:["vue"]}))],i={html:s};n.exports={languages:u,printers:i,options:a,parsers:r}}}),tg=ee({"src/language-yaml/pragma.js"(e,n){"use strict";re();function t(r){return/^\s*@(?:prettier|format)\s*$/.test(r)}function s(r){return/^\s*#[^\S\n]*@(?:prettier|format)\s*?(?:\n|$)/.test(r)}function a(r){return`# @format

${r}`}n.exports={isPragma:t,hasPragma:s,insertPragma:a}}}),rg=ee({"src/language-yaml/loc.js"(e,n){"use strict";re();function t(a){return a.position.start.offset}function s(a){return a.position.end.offset}n.exports={locStart:t,locEnd:s}}}),ng=ee({"src/language-yaml/embed.js"(e,n){"use strict";re();function t(s,a,r,u){if(s.getValue().type==="root"&&u.filepath&&/(?:[/\\]|^)\.(?:prettier|stylelint|lintstaged)rc$/.test(u.filepath))return r(u.originalText,Object.assign(Object.assign({},u),{},{parser:"json"}))}n.exports=t}}),qt=ee({"src/language-yaml/utils.js"(e,n){"use strict";re();var{getLast:t,isNonEmptyArray:s}=Ge();function a(f,x){let m=0,E=f.stack.length-1;for(let l=0;l<E;l++){let d=f.stack[l];r(d)&&x(d)&&m++}return m}function r(f,x){return f&&typeof f.type=="string"&&(!x||x.includes(f.type))}function u(f,x,m){return x("children"in f?Object.assign(Object.assign({},f),{},{children:f.children.map(E=>u(E,x,f))}):f,m)}function i(f,x,m){Object.defineProperty(f,x,{get:m,enumerable:!1})}function o(f,x){let m=0,E=x.length;for(let l=f.position.end.offset-1;l<E;l++){let d=x[l];if(d===`
`&&m++,m===1&&/\S/.test(d))return!1;if(m===2)return!0}return!1}function c(f){switch(f.getValue().type){case"tag":case"anchor":case"comment":return!1}let m=f.stack.length;for(let E=1;E<m;E++){let l=f.stack[E],d=f.stack[E-1];if(Array.isArray(d)&&typeof l=="number"&&l!==d.length-1)return!1}return!0}function y(f){return s(f.children)?y(t(f.children)):f}function h(f){return f.value.trim()==="prettier-ignore"}function g(f){let x=f.getValue();if(x.type==="documentBody"){let m=f.getParentNode();return A(m.head)&&h(t(m.head.endComments))}return v(x)&&h(t(x.leadingComments))}function p(f){return!s(f.children)&&!D(f)}function D(f){return v(f)||w(f)||T(f)||F(f)||A(f)}function v(f){return s(f==null?void 0:f.leadingComments)}function w(f){return s(f==null?void 0:f.middleComments)}function T(f){return f==null?void 0:f.indicatorComment}function F(f){return f==null?void 0:f.trailingComment}function A(f){return s(f==null?void 0:f.endComments)}function B(f){let x=[],m;for(let E of f.split(/( +)/))E!==" "?m===" "?x.push(E):x.push((x.pop()||"")+E):m===void 0&&x.unshift(""),m=E;return m===" "&&x.push((x.pop()||"")+" "),x[0]===""&&(x.shift(),x.unshift(" "+(x.shift()||""))),x}function k(f,x,m){let E=x.split(`
`).map((l,d,C)=>d===0&&d===C.length-1?l:d!==0&&d!==C.length-1?l.trim():d===0?l.trimEnd():l.trimStart());return m.proseWrap==="preserve"?E.map(l=>l.length===0?[]:[l]):E.map(l=>l.length===0?[]:B(l)).reduce((l,d,C)=>C!==0&&E[C-1].length>0&&d.length>0&&!(f==="quoteDouble"&&t(t(l)).endsWith("\\"))?[...l.slice(0,-1),[...t(l),...d]]:[...l,d],[]).map(l=>m.proseWrap==="never"?[l.join(" ")]:l)}function P(f,x){let{parentIndent:m,isLastDescendant:E,options:l}=x,d=f.position.start.line===f.position.end.line?"":l.originalText.slice(f.position.start.offset,f.position.end.offset).match(/^[^\n]*\n(.*)$/s)[1],C;if(f.indent===null){let N=d.match(/^(?<leadingSpace> *)[^\n\r ]/m);C=N?N.groups.leadingSpace.length:Number.POSITIVE_INFINITY}else C=f.indent-1+m;let _=d.split(`
`).map(N=>N.slice(C));if(l.proseWrap==="preserve"||f.type==="blockLiteral")return b(_.map(N=>N.length===0?[]:[N]));return b(_.map(N=>N.length===0?[]:B(N)).reduce((N,I,$)=>$!==0&&_[$-1].length>0&&I.length>0&&!/^\s/.test(I[0])&&!/^\s|\s$/.test(t(N))?[...N.slice(0,-1),[...t(N),...I]]:[...N,I],[]).map(N=>N.reduce((I,$)=>I.length>0&&/\s$/.test(t(I))?[...I.slice(0,-1),t(I)+" "+$]:[...I,$],[])).map(N=>l.proseWrap==="never"?[N.join(" ")]:N));function b(N){if(f.chomping==="keep")return t(N).length===0?N.slice(0,-1):N;let I=0;for(let $=N.length-1;$>=0&&N[$].length===0;$--)I++;return I===0?N:I>=2&&!E?N.slice(0,-(I-1)):N.slice(0,-I)}}function R(f){if(!f)return!0;switch(f.type){case"plain":case"quoteDouble":case"quoteSingle":case"alias":case"flowMapping":case"flowSequence":return!0;default:return!1}}n.exports={getLast:t,getAncestorCount:a,isNode:r,isEmptyNode:p,isInlineNode:R,mapNode:u,defineShortcut:i,isNextLineEmpty:o,isLastDescendantNode:c,getBlockValueLineContents:P,getFlowScalarLineContents:k,getLastDescendantNode:y,hasPrettierIgnore:g,hasLeadingComments:v,hasMiddleComments:w,hasIndicatorComment:T,hasTrailingComment:F,hasEndComments:A}}}),ug=ee({"src/language-yaml/print-preprocess.js"(e,n){"use strict";re();var{defineShortcut:t,mapNode:s}=qt();function a(u){return s(u,r)}function r(u){switch(u.type){case"document":t(u,"head",()=>u.children[0]),t(u,"body",()=>u.children[1]);break;case"documentBody":case"sequenceItem":case"flowSequenceItem":case"mappingKey":case"mappingValue":t(u,"content",()=>u.children[0]);break;case"mappingItem":case"flowMappingItem":t(u,"key",()=>u.children[0]),t(u,"value",()=>u.children[1]);break}return u}n.exports=a}}),jr=ee({"src/language-yaml/print/misc.js"(e,n){"use strict";re();var{builders:{softline:t,align:s}}=qe(),{hasEndComments:a,isNextLineEmpty:r,isNode:u}=qt(),i=new WeakMap;function o(h,g){let p=h.getValue(),D=h.stack[0],v;return i.has(D)?v=i.get(D):(v=new Set,i.set(D,v)),!v.has(p.position.end.line)&&(v.add(p.position.end.line),r(p,g)&&!c(h.getParentNode()))?t:""}function c(h){return a(h)&&!u(h,["documentHead","documentBody","flowMapping","flowSequence"])}function y(h,g){return s(" ".repeat(h),g)}n.exports={alignWithSpaces:y,shouldPrintEndComments:c,printNextEmptyLine:o}}}),sg=ee({"src/language-yaml/print/flow-mapping-sequence.js"(e,n){"use strict";re();var{builders:{ifBreak:t,line:s,softline:a,hardline:r,join:u}}=qe(),{isEmptyNode:i,getLast:o,hasEndComments:c}=qt(),{printNextEmptyLine:y,alignWithSpaces:h}=jr();function g(D,v,w){let T=D.getValue(),F=T.type==="flowMapping",A=F?"{":"[",B=F?"}":"]",k=a;F&&T.children.length>0&&w.bracketSpacing&&(k=s);let P=o(T.children),R=P&&P.type==="flowMappingItem"&&i(P.key)&&i(P.value);return[A,h(w.tabWidth,[k,p(D,v,w),w.trailingComma==="none"?"":t(","),c(T)?[r,u(r,D.map(v,"endComments"))]:""]),R?"":k,B]}function p(D,v,w){let T=D.getValue();return D.map((A,B)=>[v(),B===T.children.length-1?"":[",",s,T.children[B].position.start.line!==T.children[B+1].position.start.line?y(A,w.originalText):""]],"children")}n.exports={printFlowMapping:g,printFlowSequence:g}}}),ig=ee({"src/language-yaml/print/mapping-item.js"(e,n){"use strict";re();var{builders:{conditionalGroup:t,group:s,hardline:a,ifBreak:r,join:u,line:i}}=qe(),{hasLeadingComments:o,hasMiddleComments:c,hasTrailingComment:y,hasEndComments:h,isNode:g,isEmptyNode:p,isInlineNode:D}=qt(),{alignWithSpaces:v}=jr();function w(B,k,P,R,f){let{key:x,value:m}=B,E=p(x),l=p(m);if(E&&l)return": ";let d=R("key"),C=F(B)?" ":"";if(l)return B.type==="flowMappingItem"&&k.type==="flowMapping"?d:B.type==="mappingItem"&&T(x.content,f)&&!y(x.content)&&(!k.tag||k.tag.value!=="tag:yaml.org,2002:set")?[d,C,":"]:["? ",v(2,d)];let _=R("value");if(E)return[": ",v(2,_)];if(o(m)||!D(x.content))return["? ",v(2,d),a,u("",P.map(R,"value","leadingComments").map(q=>[q,a])),": ",v(2,_)];if(A(x.content)&&!o(x.content)&&!c(x.content)&&!y(x.content)&&!h(x)&&!o(m.content)&&!c(m.content)&&!h(m)&&T(m.content,f))return[d,C,": ",_];let b=Symbol("mappingKey"),N=s([r("? "),s(v(2,d),{id:b})]),I=[a,": ",v(2,_)],$=[C,":"];o(m.content)||h(m)&&m.content&&!g(m.content,["mapping","sequence"])||k.type==="mapping"&&y(x.content)&&D(m.content)||g(m.content,["mapping","sequence"])&&m.content.tag===null&&m.content.anchor===null?$.push(a):m.content&&$.push(i),$.push(_);let M=v(f.tabWidth,$);return T(x.content,f)&&!o(x.content)&&!c(x.content)&&!h(x)?t([[d,M]]):t([[N,r(I,M,{groupId:b})]])}function T(B,k){if(!B)return!0;switch(B.type){case"plain":case"quoteSingle":case"quoteDouble":break;case"alias":return!0;default:return!1}if(k.proseWrap==="preserve")return B.position.start.line===B.position.end.line;if(/\\$/m.test(k.originalText.slice(B.position.start.offset,B.position.end.offset)))return!1;switch(k.proseWrap){case"never":return!B.value.includes(`
`);case"always":return!/[\n ]/.test(B.value);default:return!1}}function F(B){return B.key.content&&B.key.content.type==="alias"}function A(B){if(!B)return!0;switch(B.type){case"plain":case"quoteDouble":case"quoteSingle":return B.position.start.line===B.position.end.line;case"alias":return!0;default:return!1}}n.exports=w}}),ag=ee({"src/language-yaml/print/block.js"(e,n){"use strict";re();var{builders:{dedent:t,dedentToRoot:s,fill:a,hardline:r,join:u,line:i,literalline:o,markAsRoot:c},utils:{getDocParts:y}}=qe(),{getAncestorCount:h,getBlockValueLineContents:g,hasIndicatorComment:p,isLastDescendantNode:D,isNode:v}=qt(),{alignWithSpaces:w}=jr();function T(F,A,B){let k=F.getValue(),P=h(F,E=>v(E,["sequence","mapping"])),R=D(F),f=[k.type==="blockFolded"?">":"|"];k.indent!==null&&f.push(k.indent.toString()),k.chomping!=="clip"&&f.push(k.chomping==="keep"?"+":"-"),p(k)&&f.push(" ",A("indicatorComment"));let x=g(k,{parentIndent:P,isLastDescendant:R,options:B}),m=[];for(let[E,l]of x.entries())E===0&&m.push(r),m.push(a(y(u(i,l)))),E!==x.length-1?m.push(l.length===0?r:c(o)):k.chomping==="keep"&&R&&m.push(s(l.length===0?r:o));return k.indent===null?f.push(t(w(B.tabWidth,m))):f.push(s(w(k.indent-1+P,m))),f}n.exports=T}}),og=ee({"src/language-yaml/printer-yaml.js"(e,n){"use strict";re();var{builders:{breakParent:t,fill:s,group:a,hardline:r,join:u,line:i,lineSuffix:o,literalline:c},utils:{getDocParts:y,replaceTextEndOfLine:h}}=qe(),{isPreviousLineEmpty:g}=Ge(),{insertPragma:p,isPragma:D}=tg(),{locStart:v}=rg(),w=ng(),{getFlowScalarLineContents:T,getLastDescendantNode:F,hasLeadingComments:A,hasMiddleComments:B,hasTrailingComment:k,hasEndComments:P,hasPrettierIgnore:R,isLastDescendantNode:f,isNode:x,isInlineNode:m}=qt(),E=ug(),{alignWithSpaces:l,printNextEmptyLine:d,shouldPrintEndComments:C}=jr(),{printFlowMapping:_,printFlowSequence:b}=sg(),N=ig(),I=ag();function $(O,K,se){let Q=O.getValue(),le=[];Q.type!=="mappingValue"&&A(Q)&&le.push([u(r,O.map(se,"leadingComments")),r]);let{tag:W,anchor:X}=Q;W&&le.push(se("tag")),W&&X&&le.push(" "),X&&le.push(se("anchor"));let oe="";x(Q,["mapping","sequence","comment","directive","mappingItem","sequenceItem"])&&!f(O)&&(oe=d(O,K.originalText)),(W||X)&&(x(Q,["sequence","mapping"])&&!B(Q)?le.push(r):le.push(" ")),B(Q)&&le.push([Q.middleComments.length===1?"":r,u(r,O.map(se,"middleComments")),r]);let ae=O.getParentNode();return R(O)?le.push(h(K.originalText.slice(Q.position.start.offset,Q.position.end.offset).trimEnd(),c)):le.push(a(M(Q,ae,O,K,se))),k(Q)&&!x(Q,["document","documentHead"])&&le.push(o([Q.type==="mappingValue"&&!Q.content?"":" ",ae.type==="mappingKey"&&O.getParentNode(2).type==="mapping"&&m(Q)?"":t,se("trailingComment")])),C(Q)&&le.push(l(Q.type==="sequenceItem"?2:0,[r,u(r,O.map(Ae=>[g(K.originalText,Ae.getValue(),v)?r:"",se()],"endComments"))])),le.push(oe),le}function M(O,K,se,Q,le){switch(O.type){case"root":{let{children:W}=O,X=[];se.each((ae,Ae)=>{let z=W[Ae],H=W[Ae+1];Ae!==0&&X.push(r),X.push(le()),J(z,H)?(X.push(r,"..."),k(z)&&X.push(" ",le("trailingComment"))):H&&!k(H.head)&&X.push(r,"---")},"children");let oe=F(O);return(!x(oe,["blockLiteral","blockFolded"])||oe.chomping!=="keep")&&X.push(r),X}case"document":{let W=K.children[se.getName()+1],X=[];return L(O,W,K,Q)==="head"&&((O.head.children.length>0||O.head.endComments.length>0)&&X.push(le("head")),k(O.head)?X.push(["---"," ",le(["head","trailingComment"])]):X.push("---")),q(O)&&X.push(le("body")),u(r,X)}case"documentHead":return u(r,[...se.map(le,"children"),...se.map(le,"endComments")]);case"documentBody":{let{children:W,endComments:X}=O,oe="";if(W.length>0&&X.length>0){let ae=F(O);x(ae,["blockFolded","blockLiteral"])?ae.chomping!=="keep"&&(oe=[r,r]):oe=r}return[u(r,se.map(le,"children")),oe,u(r,se.map(le,"endComments"))]}case"directive":return["%",u(" ",[O.name,...O.parameters])];case"comment":return["#",O.value];case"alias":return["*",O.value];case"tag":return Q.originalText.slice(O.position.start.offset,O.position.end.offset);case"anchor":return["&",O.value];case"plain":return Y(O.type,Q.originalText.slice(O.position.start.offset,O.position.end.offset),Q);case"quoteDouble":case"quoteSingle":{let W="'",X='"',oe=Q.originalText.slice(O.position.start.offset+1,O.position.end.offset-1);if(O.type==="quoteSingle"&&oe.includes("\\")||O.type==="quoteDouble"&&/\\[^"]/.test(oe)){let Ae=O.type==="quoteDouble"?X:W;return[Ae,Y(O.type,oe,Q),Ae]}if(oe.includes(X))return[W,Y(O.type,O.type==="quoteDouble"?oe.replace(/\\"/g,X).replace(/'/g,W.repeat(2)):oe,Q),W];if(oe.includes(W))return[X,Y(O.type,O.type==="quoteSingle"?oe.replace(/''/g,W):oe,Q),X];let ae=Q.singleQuote?W:X;return[ae,Y(O.type,oe,Q),ae]}case"blockFolded":case"blockLiteral":return I(se,le,Q);case"mapping":case"sequence":return u(r,se.map(le,"children"));case"sequenceItem":return["- ",l(2,O.content?le("content"):"")];case"mappingKey":case"mappingValue":return O.content?le("content"):"";case"mappingItem":case"flowMappingItem":return N(O,K,se,le,Q);case"flowMapping":return _(se,le,Q);case"flowSequence":return b(se,le,Q);case"flowSequenceItem":return le("content");default:throw new Error(`Unexpected node type ${O.type}`)}}function q(O){return O.body.children.length>0||P(O.body)}function J(O,K){return k(O)||K&&(K.head.children.length>0||P(K.head))}function L(O,K,se,Q){return se.children[0]===O&&/---(?:\s|$)/.test(Q.originalText.slice(v(O),v(O)+4))||O.head.children.length>0||P(O.head)||k(O.head)?"head":J(O,K)?!1:K?"root":!1}function Y(O,K,se){let Q=T(O,K,se);return u(r,Q.map(le=>s(y(u(i,le)))))}function V(O,K){if(x(K))switch(delete K.position,K.type){case"comment":if(D(K.value))return null;break;case"quoteDouble":case"quoteSingle":K.type="quote";break}}n.exports={preprocess:E,embed:w,print:$,massageAstNode:V,insertPragma:p}}}),lg=ee({"src/language-yaml/options.js"(e,n){"use strict";re();var t=jt();n.exports={bracketSpacing:t.bracketSpacing,singleQuote:t.singleQuote,proseWrap:t.proseWrap}}}),cg=ee({"src/language-yaml/parsers.js"(){re()}}),pg=ee({"node_modules/linguist-languages/data/YAML.json"(e,n){n.exports={name:"YAML",type:"data",color:"#cb171e",tmScope:"source.yaml",aliases:["yml"],extensions:[".yml",".mir",".reek",".rviz",".sublime-syntax",".syntax",".yaml",".yaml-tmlanguage",".yaml.sed",".yml.mysql"],filenames:[".clang-format",".clang-tidy",".gemrc","CITATION.cff","glide.lock","yarn.lock"],aceMode:"yaml",codemirrorMode:"yaml",codemirrorMimeType:"text/x-yaml",languageId:407}}}),fg=ee({"src/language-yaml/index.js"(e,n){"use strict";re();var t=Nt(),s=og(),a=lg(),r=cg(),u=[t(pg(),i=>({since:"1.14.0",parsers:["yaml"],vscodeLanguageIds:["yaml","ansible","home-assistant"],filenames:[...i.filenames.filter(o=>o!=="yarn.lock"),".prettierrc",".stylelintrc",".lintstagedrc"]}))];n.exports={languages:u,printers:{yaml:s},options:a,parsers:r}}}),Dg=ee({"src/languages.js"(e,n){"use strict";re(),n.exports=[Jm(),pd(),vd(),bd(),Id(),eg(),fg()]}}),mg=ee({"src/standalone.js"(e,n){re();var{version:t}=sa(),s=pm(),{getSupportInfo:a}=On(),r=fm(),u=Dg(),i=qe();function o(y){let h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return function(){for(var g=arguments.length,p=new Array(g),D=0;D<g;D++)p[D]=arguments[D];let v=p[h]||{},w=v.plugins||[];return p[h]=Object.assign(Object.assign({},v),{},{plugins:[...u,...Array.isArray(w)?w:Object.values(w)]}),y(...p)}}var c=o(s.formatWithCursor);n.exports={formatWithCursor:c,format(y,h){return c(y,h).formatted},check(y,h){let{formatted:g}=c(y,h);return g===y},doc:i,getSupportInfo:o(a,0),version:t,util:r,__debug:{parse:o(s.parse),formatAST:o(s.formatAST),formatDoc:o(s.formatDoc),printToDoc:o(s.printToDoc),printDocToString:o(s.printDocToString)}}}}),U0=mg();export{U0 as default};