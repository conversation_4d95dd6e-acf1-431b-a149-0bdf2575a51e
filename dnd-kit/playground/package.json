{"name": "@dnd-kit/playground", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"start": "parcel index.html", "build": "parcel build index.html"}, "dependencies": {"react-app-polyfill": "^1.0.0"}, "alias": {"@dnd-kit/core": "../packages/core", "@dnd-kit/accesssibility": "../packages/accesssibility", "@dnd-kit/modifiers": "../packages/modifiers", "@dnd-kit/sortable": "../packages/sortable", "@dnd-kit/utilities": "../packages/utilities", "react": "../node_modules/react", "react-dom": "../node_modules/react-dom/profiling", "scheduler/tracing": "../node_modules/scheduler/tracing-profiling"}, "devDependencies": {"@types/react": "^16.9.11", "@types/react-dom": "^16.8.4", "parcel": "^1.12.3", "typescript": "^3.4.5"}}