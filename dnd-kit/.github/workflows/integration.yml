name: E2E Test

on:
  push:
    branches-ignore:
      - 'master'
jobs:
  cypress:
    name: <PERSON><PERSON>
    runs-on: ubuntu-18.04
    steps:
      - uses: actions/checkout@v2
      - name: Specify Node Version
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Cypress run
        uses: cypress-io/github-action@v2
        with:
          start: npm run start:storybook
          wait-on: 'http://localhost:6006'
          wait-on-timeout: 120
        env:
          # pass the Dashboard record key as an environment variable
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          # pass GitHub token to allow accurately detecting a build vs a re-run build
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
