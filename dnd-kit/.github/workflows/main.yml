name: Unit Test
on: [push]
jobs:
  test:
    name: Build and test on Node ${{ matrix.node }} and ${{ matrix.os }}

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node: ['12.x', '14.x', '16.x']
        os: [ubuntu-latest]

    steps:
      - name: Checkout repo
        uses: actions/checkout@v2

      - name: Use Node ${{ matrix.node }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node }}

      - name: Install dependencies and build (with cache)
        uses: bahmutov/npm-install@v1

      - name: Test
        run: yarn test --ci --coverage --maxWorkers=2
