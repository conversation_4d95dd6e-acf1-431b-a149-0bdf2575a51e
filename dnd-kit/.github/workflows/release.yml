name: Release
on:
  push:
    branches:
      - master
jobs:
  release:
    name: ${{ matrix.channel }}
    runs-on: ubuntu-latest
    environment: production
    strategy:
      max-parallel: 1
      matrix:
        channel:
          - latest
          - next

    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: 12.x
          # https://github.com/atlassian/changesets/issues/550#issuecomment-811245508
          registry-url: https://registry.npmjs.org

      - name: Install dependencies and build (with cache)
        uses: bahmutov/npm-install@v1

      - name: Create Release Pull Request or Publish to npm
        if: matrix.channel == 'latest'
        uses: changesets/action@master
        with:
          publish: yarn release
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      # https://github.com/atlassian/changesets/blob/master/docs/snapshot-releases.md
      - name: Publish on npm with @next tag
        if: matrix.channel == 'next'
        run: |
          yarn run version:next
          yarn run release:next
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
