name: 'Chromatic'

on: [push]

jobs:
  chromatic-deployment:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - name: Specify Node Version
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Install dependencies
        run: yarn
      - name: Publish to Chromatic
        uses: chromaui/action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          projectToken: ${{ secrets.CHROMATIC_TOKEN }}
          buildScriptName: 'build:storybook'
