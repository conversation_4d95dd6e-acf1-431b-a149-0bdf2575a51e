$switch-border-radius: 50px;
$switch-track-length: 50px;
$switch-track-height: 32px;
$switch-track-border: 2px;
$switch-thumb-diameter: 28px;
$switch-thumb-offset-left: 1px;
$switch-thumb-offset-right: $switch-track-length - $switch-thumb-diameter -
  $switch-thumb-offset-left - $switch-track-border;

.Switch {
  --color: rgba(70, 70, 70, 0.5);
  --background-color: rgba(0, 0, 0, 0.02);
  --cursor: grab;

  position: relative;
  appearance: none;
  background-color: var(--background-color);
  border: 0;
  border-radius: $switch-border-radius;
  cursor: pointer;
  display: inline-block;
  padding: 0;
  margin: 0;
  width: max-content;
  user-select: none;
  box-sizing: border-box;
  outline: none;
  touch-action: none;
  -webkit-tap-highlight-color: transparent;

  &:focus-visible {
    box-shadow: 0 0 3px 2px #4c9ffe;
  }

  &::after {
    border-radius: $switch-border-radius;
  }

  &.checked:not(.off):not(.disabled),
  &.on {
    --color: #2389ff;
    --background-color: #56a1f8;
  }

  &.disabled {
    --color: rgba(34, 33, 81, 0.5);
  }

  &.dragging {
    --cursor: grabbing;
    cursor: var(--cursor);
  }

  &:not(.disabled) .Thumb {
    cursor: var(--cursor);
  }

  &:hover:not(.disabled, .dragging) .Track {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.Track {
  display: block;
  box-sizing: border-box;
  border-radius: $switch-border-radius;
  height: $switch-track-height;
  width: $switch-track-length;
  border: $switch-track-border solid var(--color);
  transition: background-color 150ms ease;
}

.ThumbWrapper {
  position: absolute;
  top: $switch-track-border;
  left: $switch-thumb-offset-left;
  bottom: $switch-track-border;
  right: $switch-track-border;
}

.Thumb {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  box-sizing: border-box;
  background-color: #fff;
  border: 0.1em solid var(--color);
  border-radius: 50%;
  width: $switch-thumb-diameter;
  height: $switch-thumb-diameter;
  transition: transform 150ms ease;
  transform: translate3d(var(--transform), 0, 0);

  .checked & {
    transform: translate3d(
      calc($switch-thumb-offset-right + var(--transform)),
      0,
      0
    );
  }

  .checked:not(.off) &,
  .on & {
    border-color: transparent;
    transform: translateX($switch-thumb-offset-right);
  }
}

.OnRegion,
.OffRegion {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  pointer-events: none;
}

.OnRegion {
  right: 0;
}

.OffRegion {
  left: 0;
}

.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.Label {
  display: inline-flex;
  flex-direction: column;
  color: #666;
  padding: 1rem;
  gap: 0.8rem;
}
