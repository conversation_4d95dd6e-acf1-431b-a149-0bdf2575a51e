.Piece {
  --box-shadow: inset 0 0 0 4px rgba(255, 255, 255, 0.2),
    inset 0 0 0 10px var(--color), inset 0 0 0 30px rgba(0, 0, 0, 0.1);

  appearance: none;
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
  opacity: 1;
  border-radius: 50%;
  background-color: var(--color);
  border: 3px solid var(--border-color);
  box-shadow: var(--box-shadow);
  touch-action: none;
  cursor: grab;

  &:focus-visible {
    box-shadow: var(--box-shadow), 0 0 10px 2px #4c9ffe;
  }
}

.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.odd {
  --color: #596dff;
  --border-color: #3a49be;
}

.even {
  --color: #44464c;
  --border-color: #283144;
}

.clone {
  animation: pop 250ms cubic-bezier(0.18, 0.67, 0.6, 1.22) forwards;
  transform-origin: 0 0;
  cursor: grabbing;
  box-shadow: var(--box-shadow), -1px 0 15px 0 rgba(34, 33, 81, 0.01),
    0px 15px 15px 0 rgba(34, 33, 81, 0.25);
}

@keyframes pop {
  0% {
    transform: scale(1);
  }
  100% {
    transform: translate3d(-10%, -10%, 0) scale(1.15);
  }
}
