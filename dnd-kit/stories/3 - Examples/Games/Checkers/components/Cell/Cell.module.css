.Cell {
  --size: calc(4vw);
  --min-size: 30px;
  --max-size: calc(8vh);

  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--size);
  max-width: var(--max-size);
  min-width: var(--min-size);
  height: var(--size);
  max-height: var(--max-size);
  min-height: var(--min-size);
  padding: 8px;
}

.odd {
  background-color: #eef3f7;
}

.even {
  background-color: #fff;
}

.highlight {
  background-color: #b7fbd3;
}

.highlight.over {
  background-color: #7ef3af;
}
