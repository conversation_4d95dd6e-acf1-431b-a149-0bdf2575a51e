$box-shadow: 1px 1px 0 1px #f9f9fb, -1px 0 28px 0 rgba(34, 33, 81, 0.01),
  54px 54px 28px -10px rgba(34, 33, 81, 0.15);

.<PERSON>rapper {
  position: relative;
  width: 140px;
  height: 180px;
  margin-bottom: -145px;
}

.PlayingCard {
  display: flex;
  width: 200px;
  height: 300px;
  margin-left: 140px;
  background-color: #fff;
  box-shadow: $box-shadow;
  transform-style: preserve-3d;
  border-radius: 24px;
  font-size: 25px;
  font-family: 'Roboto Slab', Helvetica, sans-serif;
  user-select: none;
  transform-origin: 0 0;
  transform: scale(var(--scale, 1)) translate3d(var(--translate-x, 0), var(--translate-y, 0), 0)
    rotateX(60deg) rotateZ(33deg);
  transition: var(--transition);

  &:hover:not(.pickedUp) {
    --translate-x: 5px;
  }

  &:focus-visible {
    --translate-x: 5px;
    outline: none;
    box-shadow: 0 0 0 2px #4c9ffe, $box-shadow;
  }

  &.fadeIn {
    animation: fadeIn 600ms ease;
  }

  &.dragging {
    opacity: 0.7;
    --translate-x: 15px;
  }

  &.mountAnimation {
    animation: mount 2200ms ease;
  }

  &.pickedUp {
    --translate-x: 15px;

    opacity: 0.95;
    animation: pop-transform 250ms cubic-bezier(0.18, 0.67, 0.6, 1.22);
    transition: box-shadow 250ms ease;
    box-shadow: 1px 1px 0 1px #f9f9fb, -1px 0 28px 0 rgba(34, 33, 81, 0.01),
      54px 54px 28px -10px rgba(34, 33, 81, 0.15);
  }
}

sup,
sub {
  position: absolute;
  font-size: 17px;
}

strong {
  position: relative;
  left: -5px;
  margin: 0 auto;
  align-self: center;
  font-size: 50px;
}

sup {
  left: 20px;
  top: 20px;
}

sub {
  right: 20px;
  bottom: 20px;
}

@keyframes mount {
  0%,
  40% {
    transform: translate3d(0, calc(var(--index, 1) * -38px), 0) scale(1.1)
      rotateX(60deg) rotateZ(10deg);
    box-shadow: 1px 1px 0 1px #e5e5e7;
  }

  100% {
    transform: translate3d(0, 0, 0) scale(1) rotateX(60deg) rotateZ(33deg);
    box-shadow: $box-shadow;
  }
}

@keyframes pop-transform {
  0% {
    transform: scale(1) translate3d(var(--translate-x, 0), 0, 0) rotateX(60deg) rotateZ(33deg);
    box-shadow: 1px 1px 0 1px #f9f9fb, -1px 0 28px 0 rgba(34, 33, 81, 0.01),
      28px 28px 28px 0 rgba(34, 33, 81, 0.25);
  }
  100% {
    transform: scale(1.075) translate3d(var(--translate-x, 0), 0, 0) rotateX(60deg) rotateZ(33deg);
    box-shadow: $box-shadow;
  }
}

@keyframes fadeIn {
  0% {
    transform: scale(1.05) rotateX(60deg) rotateZ(30deg) translate3d(40px, 0, 0);
    opacity: 0;
  }
  100% {
    opacity: 0.7;
  }
}
