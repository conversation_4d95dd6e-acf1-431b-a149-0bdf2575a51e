$header-height: 100px;
$content-background-color: #fefefe;

.Drawer {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  inset: 0;
  pointer-events: none;
  overflow: hidden;

  background-color: rgba(0, 0, 0, 0.05);
}

.Sheet {
  position: relative;
  pointer-events: all;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  max-height: var(--max-height);
  transition: transform 200ms ease;
  transform: translate3d(
    0,
    calc(100% - $header-height + var(--transform, 0px)),
    0
  );

  &.dragging {
    transition: none;
  }

  &:after {
    content: '';
    display: block;
    position: absolute;
    bottom: 1px;
    width: 100%;
    height: 100%;
    transform: translateY(100%);
    background-color: $content-background-color;
  }

  &.expanded {
    transform: translate3d(0, var(--transform), 0);
  }
}

.Content {
  display: block;
  overflow-y: auto;
  padding: 20px;
  background-color: $content-background-color;
}

.Header {
  --radius: 25px;
  --cursor: grab;
  --color: rgba(0, 0, 0, 0.05);

  position: relative;
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
  height: $header-height;
  font-size: 20px;
  color: #333;
  padding: 1rem;
  touch-action: none;
  cursor: var(--cursor);

  &:before {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    top: 10px;
    border-radius: 10px;
    background-color: var(--color);
    transition: background-color 0.2s ease;
  }

  .dragging & {
    --cursor: grabbing;
    --color: rgba(0, 0, 0, 0.12);
  }
}

.DropRegions {
  position: absolute;
  inset: 0;
  display: grid;
  grid-template-rows: 1.2fr 0.8fr;
  pointer-events: none;
}

:global {
  button {
    float: right;
    cursor: pointer;
    appearance: none;
    font-size: 18px;
    color: #fff;
    background-color: #181a22;
    border: none;
    padding: 8px 18px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
  }
}
