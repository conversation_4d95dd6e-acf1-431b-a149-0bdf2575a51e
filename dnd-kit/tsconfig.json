{"extends": "./tsconfig.build.json", "include": ["packages", "types", "scripts", "playground", "stories"], "compilerOptions": {"allowJs": false, "baseUrl": ".", "importsNotUsedAsValues": "error", "isolatedModules": true, "typeRoots": ["./node_modules/@types", "./types"], "paths": {"@dnd-kit/core": ["packages/core/src"], "@dnd-kit/accessibility": ["packages/accessibility/src"], "@dnd-kit/modifiers": ["packages/modifiers/src"], "@dnd-kit/sortable": ["packages/sortable/src"], "@dnd-kit/utilities": ["packages/utilities/src"], "$test/*": ["test/*"]}}}