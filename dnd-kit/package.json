{"name": "dnd-kit", "description": "dnd kit – a lightweight React library for building performant and accessible drag and drop experiences", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "private": true, "workspaces": ["packages/*"], "scripts": {"lerna": "lerna", "start": "lerna run start --stream --parallel", "test": "lerna run test --", "lint": "lerna run lint -- --fix", "build": "lerna run build", "build:storybook": "build-storybook", "prepublish": "lerna run prepublish", "start:playground": "yarn run build && yarn --cwd playground && yarn --cwd playground start", "start:storybook": "start-storybook -p 6006", "cypress": "cypress run", "cypress:open": "cypress open", "changeset": "changeset", "release": "changeset publish", "version:next": "changeset version --snapshot next", "release:next": "changeset publish --tag next"}, "devDependencies": {"@changesets/changelog-github": "^0.4.0", "@changesets/cli": "^2.16.0", "@storybook/addon-essentials": "^6.5.5", "@storybook/addon-info": "^5.3.21", "@storybook/addon-links": "^6.5.5", "@storybook/addons": "^6.5.5", "@storybook/react": "^6.5.5", "@types/classnames": "^2.2.11", "@types/react": "^16.9.43", "@types/react-dom": "^16.9.8", "babel-jest": "^27.0.2", "babel-loader": "^8.2.1", "chromatic": "^5.4.0", "classnames": "^2.2.6", "css-loader": "^2.0.0", "cypress": "^9.5.2", "framer-motion": "^3.1.1", "lerna": "^3.15.0", "postcss": "^8.1.10", "postcss-loader": "^4.1.0", "postcss-nested": "^5.0.1", "postcss-simple-vars": "^6.0.1", "react": "^18.1.0", "react-dom": "^18.1.0", "react-tiny-virtual-list": "^2.2.0", "style-loader": "^2.0.0", "tsdx": "^0.14.1", "typescript": "^4.8.4"}, "resolutions": {"prettier": "^2.2.0", "**/@typescript-eslint/eslint-plugin": "^4.8.2", "**/@typescript-eslint/parser": "^4.8.2", "**/typescript": "^4.8.4", "jest": "^27.0.5", "ts-jest": "^27.0.3"}}